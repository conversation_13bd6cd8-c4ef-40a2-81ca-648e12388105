import { UserSelectType } from "@/db/services/users.service";
import { ListingData } from "@/types/listing";
import { ContactUser } from "@/utils/userUtils";

// Define interface for linked account structure
export interface LinkedAccount {
	provider: {
		id: string;
		userName: string;
	};
	buyer: {
		id: string;
		userName: string;
	};
}

export interface MessageBoxProps {
	backPage?: string;
	listingId: string;
	listingOwner: UserSelectType;
	previewUrl: string;
	data: ListingData;
}

export interface ContactSectionProps {
	backPage?: string;
	contact?: string;
	setContact: (value: string) => void;
	contactUsers: ContactUser[];
}

export interface MessageSectionProps {
	me: UserSelectType;
	token: string;
	listingId: string;
	backPage?: string;
	message: string;
	setMessage: (value: string) => void;
	contact?: string;
	previewUrl: string;
	data: ListingData;
}
