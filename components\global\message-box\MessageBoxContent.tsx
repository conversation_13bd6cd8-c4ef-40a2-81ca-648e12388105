"use client";
import { useEffect, useState } from "react";
import useGetStreamToken from "@/hooks/useGetStreamToken";
import { useConnections } from "@/hooks/useConnections";
import { UserSelectType } from "@/db/services/users.service";
import { ListingData } from "@/types/listing";
import { ContactSection } from "./ContactSection";
import { MessageSection } from "./MessageSection";
import { getContactUsers, canUserSendMessage } from "@/utils/userUtils";

interface MessageBoxContentProps {
	me: UserSelectType;
	backPage?: string;
	listingId: string;
	listingOwner: UserSelectType;
	previewUrl: string;
	data: ListingData;
}

// Message box content component
export const MessageBoxContent = ({
	me,
	backPage,
	listingId,
	listingOwner,
	previewUrl,
	data,
}: MessageBoxContentProps) => {
	const [contact, setContact] = useState<string>();
	const [message, setMessage] = useState<string>("");
	const { token } = useGetStreamToken(me.id);
	const { connections: linkedAccounts } = useConnections({ userId: me.id! });

	const contactUsers = getContactUsers(linkedAccounts, listingOwner, me);

	useEffect(() => {
		if (listingOwner && contactUsers.length > 0) {
			setContact(contactUsers[0].id);
		}
	}, [listingOwner, contactUsers, me.id]);

	// Check if user can send messages
	const canSendMessage = canUserSendMessage(
		contactUsers.length,
		me,
		listingOwner
	);

	return (
		<div>
			{token && canSendMessage && (
				<div className="w-full px-4 py-6 space-y-6 border shadow-sm rounded-xs bg-card font-manrope">
					<ContactSection
						backPage={backPage}
						contact={contact}
						setContact={setContact}
						contactUsers={contactUsers}
					/>

					<MessageSection
						me={me}
						token={token}
						listingId={listingId}
						backPage={backPage}
						message={message}
						setMessage={setMessage}
						contact={contact}
						previewUrl={previewUrl}
						data={data}
					/>
				</div>
			)}
		</div>
	);
};
