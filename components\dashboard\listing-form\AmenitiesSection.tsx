"use client";
import { UseFormReturn } from "react-hook-form";
import { IconType } from "react-icons";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { ListingFormValues } from "@/lib/schemas/listing-schema";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/ui/accordion";
import { amenities, features } from "@/constant/amenities";

type AmenityItemProps = {
	form: UseFormReturn<ListingFormValues>;
	amenityValue: string; // The value to be added to the amenities array
	label: string;
};

const AmenityItem = ({ form, amenityValue, label }: AmenityItemProps) => {
	// Get current amenities array from form
	const currentAmenities = form.watch("amenities") || [];

	// Check if this amenity is selected
	const isSelected = currentAmenities.includes(amenityValue);

	const handleChange = (checked: boolean) => {
		let newAmenities = [...currentAmenities];

		if (checked && !isSelected) {
			// Add to array if checked and not already included
			newAmenities.push(amenityValue);
		} else if (!checked && isSelected) {
			// Remove from array if unchecked and was included
			newAmenities = newAmenities.filter((item) => item !== amenityValue);
		}

		// Update the form field
		form.setValue("amenities", newAmenities, { shouldDirty: true });
	};

	return (
		<FormItem className="flex flex-row items-center justify-between p-2 space-x-3 space-y-0 bg-gray-100 rounded-md">
			<FormLabel className="flex-1 text-lg font-normal cursor-pointer font-manrope">
				{label}
			</FormLabel>
			<FormControl>
				<Checkbox checked={isSelected} onCheckedChange={handleChange} />
			</FormControl>
		</FormItem>
	);
};

// Feature Accordion Component
type FeatureAccordionProps = {
	form: UseFormReturn<ListingFormValues>;
	featureGroup: {
		id: string;
		title: string;
		items: { name: string; label: string; icon?: IconType }[];
	};
};

const FeatureAccordion = ({ form, featureGroup }: FeatureAccordionProps) => {
	return (
		<AccordionItem value={featureGroup.id}>
			<AccordionTrigger className="items-center px-4 py-1 text-lg font-bold bg-gray-300 font-manrope">
				{featureGroup.title}
			</AccordionTrigger>
			<AccordionContent>
				<div className="grid grid-cols-2 gap-4 pt-4 md:grid-cols-3 lg:grid-cols-4">
					{featureGroup.items.map((feature) => (
						<AmenityItem
							key={feature.name}
							form={form}
							amenityValue={feature.name}
							label={feature.label}
						/>
					))}
				</div>
			</AccordionContent>
		</AccordionItem>
	);
};

type AmenitiesSectionProps = {
	form: UseFormReturn<ListingFormValues>;
};

const AmenitiesSection = ({ form }: AmenitiesSectionProps) => {
	return (
		<div className="space-y-6">
			<h2 className="text-3xl font-normal font-lora">Amenities</h2>

			{/* Hidden field to ensure amenities is submitted even if empty */}
			<FormField control={form.control} name="amenities" render={() => <></>} />

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
				{amenities.map((amenity) => (
					<AmenityItem
						key={String(amenity.name)}
						form={form}
						amenityValue={amenity.name}
						label={amenity.label}
					/>
				))}
			</div>

			{/* Feature Groups Accordions */}
			<Accordion type="multiple" className="flex flex-col w-full gap-4">
				{features.map((featureGroup) => (
					<FeatureAccordion
						key={featureGroup.id}
						form={form}
						featureGroup={featureGroup}
					/>
				))}
			</Accordion>
		</div>
	);
};

export default AmenitiesSection;
