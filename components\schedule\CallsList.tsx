import React from "react";
import Image from "next/image";
import { format } from "date-fns";
import ScheduleListSkeleton from "./ScheduleListSkeleton";
import { ScheduledCall } from "@/types/scheduledCall";

interface CallsListProps {
	schedules: ScheduledCall[];
	selectedCall: ScheduledCall | null;
	isLoading: boolean;
	onSelectCall: (call: ScheduledCall) => void;
	getSafeImageUrl: (imagePath?: string) => string | null;
}

const CallsList: React.FC<CallsListProps> = ({
	schedules,
	selectedCall,
	isLoading,
	onSelectCall,
	getSafeImageUrl,
}) => {
	return (
		<div className="col-span-1 max-h-[80vh] overflow-y-auto bg-[#EFEFF1] rounded-lg">
			<div className="bg-white rounded-lg">
				<div className="flex flex-col rounded-t-[12px]">
					{isLoading ? (
						<ScheduleListSkeleton />
					) : schedules.length > 0 ? (
						schedules.map((call: ScheduledCall, index: number) => {
							const isSelected = selectedCall?.id === call.id;
							const imageUrl = getSafeImageUrl(
								call?.listing?.media?.[0]?.preview
							);
							const formattedDate = format(call.datetime, "MMMM d, yyyy");
							const formattedTime = format(call.datetime, "HH:mm");

							return (
								<button
									key={call.id}
									className={`text-left flex flex-col gap-5 p-6 transition hover:cursor-pointer border border-b-1 border-b-[#B6B6B6] ${
										index === 0 ? "rounded-t-[11px]" : ""
									} ${
										isSelected
											? "bg-[#CACAD1]"
											: "bg-[#EFEFF1] border-transparent hover:bg-gray-200"
									}`}
									onClick={() => onSelectCall(call)}
								>
									<div className="flex flex-row justify-between font-manrope">
										<div className="flex flex-col gap-1">
											<div className="text-lg font-medium leading-7">
												{call.callName}
											</div>
											<div className="text-base font-bold leading-6">
												<span>{formattedDate}</span>
												<br />
												<span>{formattedTime}</span>
											</div>
										</div>
									</div>
									<div className="flex flex-row items-center gap-4">
										{imageUrl && (
											<Image
												alt="listing preview"
												width={40}
												height={40}
												className="object-cover w-10 h-10 bg-white rounded-full"
												src={imageUrl}
											/>
										)}
										<div className="truncate text-base font-bold text-[#000000]">
											ID#{call?.listingId}
										</div>
									</div>
								</button>
							);
						})
					) : (
						<div className="p-6 text-center text-gray-500">
							No scheduled calls found
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default CallsList;
