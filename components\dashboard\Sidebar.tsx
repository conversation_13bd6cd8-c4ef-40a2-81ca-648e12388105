"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import LogoutPopup from "../global/LogoutPopup";
import { Button } from "../ui/button";
import CollapsibleSection from "./CollapsibleSection";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import {
  LISTING_MANAGEMENT_PATH_MAP,
  USER_MANAGEMENT_PATH_MAP,
} from "@/constant/const";

const DashboardSidebar = () => {
  const pathname = usePathname();
  const isMyListingsActive = pathname!.includes("/dashboard/listings/");
  const isUserManagementActive = pathname!.includes(
    "/dashboard/user-management/"
  );
  const isListingManagementActive = pathname!.includes(
    "/dashboard/listing-management/"
  );

  const { user: me } = useCurrentUser();

  return (
    <aside className="flex flex-col w-full h-full p-4 overflow-x-hidden scrollable font-manrope">
      <nav className="space-y-1">
        {me?.role === "advertiser" && (
          <NavItem
            href="/dashboard/listings"
            label="My Listings"
            active={pathname === "/dashboard/listings"}
          />
        )}
        {me?.role === "buyer" && (
          <NavItem
            href="/dashboard/listings/favorites"
            label="Favorites"
            active={pathname === "/dashboard/listings/favorites"}
          />
        )}

        {me?.role === "admin" && (
          <CollapsibleSection
            title="User Management"
            isActive={isUserManagementActive}
          >
            {Object.entries(USER_MANAGEMENT_PATH_MAP).map(([key, config]) => (
              <SubNavItem
                key={key}
                href={`/dashboard/user-management/${key}`}
                label={config.title}
                active={pathname?.includes(`/dashboard/user-management/${key}`)}
              />
            ))}
          </CollapsibleSection>
        )}

        {me?.role === "admin" && (
          <CollapsibleSection
            title="Listing Management"
            isActive={isListingManagementActive}
          >
            {Object.entries(LISTING_MANAGEMENT_PATH_MAP).map(
              ([key, config]) => (
                <SubNavItem
                  key={key}
                  href={`/dashboard/listing-management/${key}`}
                  label={config.title}
                  active={pathname?.includes(
                    `/dashboard/listing-management/${key}`
                  )}
                />
              )
            )}
          </CollapsibleSection>
        )}

        {/* Regular Nav Items */}
        <NavItem
          href="/dashboard/inbox"
          label="Inbox"
          active={pathname === "/dashboard/inbox"}
        />
        <NavItem
          href="/dashboard/schedule"
          label="Meetings"
          active={pathname === "/dashboard/schedule"}
        />
        {me?.role !== "advertiser" && (
          <>
            <NavItem
              href="/dashboard/foundation"
              label="Sacred Hearts Foundation"
              active={pathname === "/dashboard/foundation"}
            />
            <NavItem
              href="/dashboard/exclusives"
              label="MG Exclusives"
              active={pathname === "/dashboard/exclusives"}
            />
          </>
        )}
        {me?.role !== "admin" && (
          <NavItem
            href="/dashboard/profile"
            label="Profile"
            active={pathname === "/dashboard/profile"}
          />
        )}
      </nav>
      <LogoutPopup>
        <Button
          className="flex items-center justify-between w-full p-2 ml-1 text-lg font-normal text-gray-700 underline rounded-md hover:bg-gray-100"
          variant={"ghost"}
        >
          Logout
        </Button>
      </LogoutPopup>
    </aside>
  );
};

// Helper component for main navigation items
const NavItem = ({
  label,
  href,
  active = false,
}: {
  label: string;
  href: string;
  active?: boolean;
}) => {
  return (
    <Link
      href={href}
      className={cn(
        "flex items-center p-2 text-lg rounded-md hover:bg-gray-100 relative",
        active ? "font-bold" : "font-normal text-gray-700"
      )}
    >
      {active && (
        <div className="absolute top-0 bottom-0 left-0 my-2 w-0.75 bg-primary"></div>
      )}
      <span className="ml-1">{label}</span>
    </Link>
  );
};

// Helper component specifically for sub-navigation items
const SubNavItem = ({
  label,
  href,
  active = false,
}: {
  label: string;
  href: string;
  active?: boolean;
}) => {
  return (
    <Link
      href={href}
      className={cn(
        "flex items-center p-2 text-base rounded-md hover:bg-gray-100",
        active
          ? "font-bold underline underline-offset-4 decoration-2"
          : "font-normal text-gray-700"
      )}
    >
      <span>{label}</span>
    </Link>
  );
};

export default DashboardSidebar;
