import { ListingData } from "@/types/listing";
import { getPreviewMediaUrl } from "@/utils/media";
import Image from "next/image";
import { IoIosCalendar } from "react-icons/io";

interface PropertyInfoProps {
	listingDetails: ListingData;
	onCalendarClick: () => void;
}

const PropertyInfo = ({
	listingDetails,
	onCalendarClick,
}: PropertyInfoProps) => {
	const { title, media, id, location, price, realEstateDetails } =
		listingDetails;
	const image = getPreviewMediaUrl(media);
	return (
		<div className="flex flex-row w-full bg-[#EFEFF1] rounded-t-[11px] shadow-blue-100">
			<div className="w-[262px] self-stretch rounded-tl-[11px] overflow-hidden">
				{image ? (
					<Image
						alt="listingImage"
						width={262}
						height={183}
						src={image}
						className="object-cover w-full h-full"
					/>
				) : (
					<div className="flex items-center justify-center w-full h-full font-bold text-gray-600 bg-gray-300">
						{(title?.[0] || "U").toUpperCase()}
					</div>
				)}
			</div>
			<div className="flex flex-col w-full gap-4 p-5">
				<div className="flex flex-col gap-1">
					<div className="flex flex-row justify-between">
						<span className="text-base font-normal leading-6 font-manrope">
							{title || "Untitled Property"}
						</span>
						<span className="font-manrope text-lg leading-7 text-[#727272] font-bold">
							ID#{id || "N/A"}
						</span>
					</div>
					<span className="text-2xl leading-7 font-lora">
						{`${location.city}, ${location.state}`}
					</span>
					<span className="text-xl font-bold leading-7 font-manrope">
						{price || "Price on Request"}
					</span>
				</div>
				<div className="flex flex-row justify-between">
					<div className="flex flex-row gap-4 leading-6 font-manrope font-base">
						<span>{realEstateDetails.bedrooms} bedrooms</span>
						<span>{realEstateDetails.bathrooms} bathrooms</span>
						<span>{realEstateDetails.homeSize} sqf</span>
					</div>
					<IoIosCalendar
						className="w-8 h-8 cursor-pointer"
						onClick={onCalendarClick}
					/>
				</div>
			</div>
		</div>
	);
};

export default PropertyInfo;
