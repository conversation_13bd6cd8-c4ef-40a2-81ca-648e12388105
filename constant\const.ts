import { roleEnum } from "@/db/schema/enums";

export const USER_MANAGEMENT_PATH_MAP = {
	buyers: {
		role: "buyer",
		title: "Buyers",
	},
	searchers: {
		role: "searcher",
		title: "Searchers",
	},
	advertisers: {
		role: "advertiser",
		title: "Advertisers",
	},
	combination: {
		role: "combination",
		title: "Combination",
	},
	concierge: {
		role: "concierge",
		title: "Concierge",
	},
};

export const LISTING_MANAGEMENT_PATH_MAP = {
	"approved-listings": {
		status: "approved",
		title: "Approved Listings",
	},
	"pending-listings": {
		status: "pending",
		title: "Listings Needing Admin Approval",
		reviewerType: "admin",
	},
	"waiting-listings": {
		status: "pending",
		title: "Listings Needing Advertiser Review",
		reviewerType: "advertiser",
	},
	"declined-listings": {
		status: "declined",
		title: "Declined Listings",
	},
	"archived-listings": {
		status: "archived",
		title: "Archived Listings",
	},
};

export type UserManagementPathMapKey = keyof typeof USER_MANAGEMENT_PATH_MAP;
export type ListingManagementPathMapKey =
	keyof typeof LISTING_MANAGEMENT_PATH_MAP;

export const MEMBERSHIP_PLANS = [
	"Single Listing",
	"Top Producer",
	"Small Teams",
	"Mid-Size Teams",
	"Large Teams",
	"Small Office",
	"Enterprise",
];

export const SUBCATEGORY = [
	"Assistant",
	"Attorney",
	"Direct",
	"Family Office Manager",
	"Financial Advisor",
	"Real Estate Consultant",
	"Spouse",
	"Real Estate Agent/Broker",
];

export const PREFERRED_COMMUNICATIONS = ["In-App Only", "Email", "SMS"];

export const SITE_ACCESS = [
	"Real Estate",
	"Aviation",
	"Yachts",
	"Automobiles",
	"Fine Art & Collectibles",
];

export const ACCOUNT_TYPE = roleEnum.enumValues;

// Cookie constants for terms of service
export const TERMS_COOKIE_NAME = "hasAcceptedTerms";
