import { pgTable, text, timestamp, index, jsonb } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { timestamps, simplifyUuidField } from "../columns.helpers";
import { users } from "./users";
import { listings } from "./listings";

// Type for recipients JSONB structure
export type Recipients = Record<string, boolean>;

export const schedules = pgTable(
	"schedules",
	{
		id: simplifyUuidField(),
		callName: text("call_name"), // Call name for the scheduled call
		senderId: text("sender_id")
			.notNull()
			.references(() => users.id, { onDelete: "cascade" }),
		recipients: jsonb("recipients").$type<Recipients>(), // JSONB field for recipients
		scheduleTime: timestamp("schedule_time").notNull(),
		endTime: timestamp("end_time").notNull(),
		listingId: text("listing_id").references(() => listings.id, {
			onDelete: "set null",
		}), // Add reference constraint
		...timestamps,
	},
	(table) => [
		index("schedule_sender_idx").on(table.senderId),
		index("schedule_listing_idx").on(table.listingId),
		// GIN index for JSONB field to improve query performance
		index("schedule_recipients_gin_idx").using("gin", table.recipients),
	]
);

// Add relation definition
export const schedulesRelations = relations(schedules, ({ one }) => ({
	sender: one(users, {
		fields: [schedules.senderId],
		references: [users.id],
		relationName: "scheduleSender",
	}),
	listing: one(listings, {
		fields: [schedules.listingId],
		references: [listings.id],
	}),
}));
