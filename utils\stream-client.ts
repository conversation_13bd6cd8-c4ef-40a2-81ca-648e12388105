import { StreamChat, Channel } from "stream-chat";

// Global variable to store the StreamChat instance
let streamClientInstance: StreamChat | null = null;

// Type definitions for better type safety
export interface ChannelInfo {
	id: string;
	type: string;
	name?: string;
	image?: string;
	member_count?: number;
	created_at?: string;
	updated_at?: string;
	last_message_at?: string;
}

export interface ChannelsResponse {
	success: boolean;
	channels?: ChannelInfo[];
	error?: string;
}

/**
 * Validate required environment variables
 */
function validateEnvironment(): void {
	if (!process.env.NEXT_PUBLIC_GETSTREAM_API_KEY) {
		throw new Error("NEXT_PUBLIC_GETSTREAM_API_KEY is required");
	}
	if (!process.env.GETSTREAM_API_SECRET) {
		throw new Error("GETSTREAM_API_SECRET is required");
	}
}

/**
 * Get a singleton instance of StreamChat client
 * This ensures we reuse the same connection across requests
 * @returns StreamChat instance
 */
export function getStreamClient(): StreamChat {
	if (!streamClientInstance) {
		validateEnvironment();

		// Initialize StreamChat with API key and secret only if not already initialized
		streamClientInstance = StreamChat.getInstance(
			process.env.NEXT_PUBLIC_GETSTREAM_API_KEY!,
			process.env.GETSTREAM_API_SECRET!
		);
		console.log("StreamChat client initialized");
	}

	return streamClientInstance;
}

/**
 * Reset the StreamChat instance (useful for testing)
 */
export function resetStreamClient(): void {
	if (streamClientInstance) {
		streamClientInstance.disconnectUser();
		streamClientInstance = null;
		console.log("StreamChat client disconnected");
	}
}

/**
 * Transform channel data to a cleaner format
 */
function transformChannelData(channel: Channel): ChannelInfo {
	return {
		id: channel.id!,
		type: channel.type,
		member_count: channel.data?.member_count,
		created_at: channel.data?.created_at,
		updated_at: channel.data?.updated_at,
		last_message_at: channel.data?.last_message_at,
	};
}

/**
 * Get all channels from Stream Chat
 * @param userId - Optional user ID to filter channels for specific user
 * @param limit - Maximum number of channels to return (default: 100)
 * @returns Promise with channels data
 */
export async function getAllChannels(
	userId?: string,
	limit: number = 100
): Promise<ChannelsResponse> {
	try {
		const client = getStreamClient();

		// Build filter based on userId parameter
		const filter = userId ? { members: { $in: [userId] } } : {};

		const sort = { last_message_at: -1 as const }; // Sort by most recent activity (descending)
		const options = {
			limit: Math.min(limit, 100), // Cap at 100 for performance
			state: true, // Include channel state
			watch: false, // Don't watch for real-time updates
		};

		const channels = await client.queryChannels(filter, sort, options);

		return {
			success: true,
			channels: channels.map(transformChannelData),
		};
	} catch (error) {
		console.error("Error fetching channels:", error);

		// More specific error handling
		const errorMessage =
			error instanceof Error ? error.message : "Failed to fetch channels";

		return {
			success: false,
			error: errorMessage,
		};
	}
}

/**
 * Delete all channels from Stream Chat
 * @param userId - Optional user ID to filter channels for specific user (for safety)
 * @returns Promise with deletion result
 */
export async function deleteAllChannels(
	userId?: string
): Promise<ChannelsResponse> {
	try {
		const client = getStreamClient();

		// First get all channels to delete
		const channelsResult = await getAllChannels(userId);

		if (!channelsResult.success || !channelsResult.channels) {
			return {
				success: false,
				error: "Failed to fetch channels for deletion",
			};
		}

		// Delete each channel
		const deletionPromises = channelsResult.channels.map(
			async (channelInfo) => {
				try {
					const channel = client.channel(channelInfo.type, channelInfo.id);
					await channel.delete();
					return { success: true, id: channelInfo.id };
				} catch (error) {
					console.error(`Failed to delete channel ${channelInfo.id}:`, error);
					return { success: false, id: channelInfo.id, error };
				}
			}
		);

		const results = await Promise.allSettled(deletionPromises);
		const failedDeletions = results.filter(
			(result) => result.status === "rejected" || !result.value.success
		).length;

		if (failedDeletions > 0) {
			console.warn(
				`Failed to delete ${failedDeletions} out of ${channelsResult.channels.length} channels`
			);
		}

		return {
			success: true,
			channels: [], // Return empty array as all channels are deleted
		};
	} catch (error) {
		console.error("Error deleting all channels:", error);

		const errorMessage =
			error instanceof Error ? error.message : "Failed to delete channels";

		return {
			success: false,
			error: errorMessage,
		};
	}
}

/**
 * Check if StreamChat client is connected
 * @returns boolean indicating connection status
 */
export function isClientConnected(): boolean {
	return streamClientInstance
		? !streamClientInstance.wsConnection?.isDisconnected
		: false;
}
