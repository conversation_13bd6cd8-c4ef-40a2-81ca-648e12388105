"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Editor<PERSON>ontex<PERSON>, useEditor } from "@tiptap/react";
import { StarterKit } from "@tiptap/starter-kit";
import { HeadingButton } from "@/components/tiptap-ui/heading-button";
import { TextAlignButton } from "../tiptap-ui/text-align-button";
import { Separator } from "../tiptap-ui-primitive/separator";
import { TextAlign } from "@tiptap/extension-text-align";
import { UndoRedoButton } from "../tiptap-ui/undo-redo-button";
import { MarkButton } from "../tiptap-ui/mark-button";
import { TaskList } from "@tiptap/extension-task-list";
import { TaskItem } from "@tiptap/extension-task-item";
import { ListButton } from "@/components/tiptap-ui/list-button";
import { useEffect } from "react"; // Add useEffect import

import "@/components/tiptap-node/code-block-node/code-block-node.scss";
import "@/components/tiptap-node/list-node/list-node.scss";
import "@/components/tiptap-node/paragraph-node/paragraph-node.scss";
import { cn } from "@/lib/utils";

interface TiptapEditorProps {
	value?: string;
	onChange?: (value: string) => void;
	className?: string;
	editable?: boolean;
}

export const TiptapEditor = ({
	value,
	onChange,
	className,
	editable = false,
}: TiptapEditorProps) => {
	const editor = useEditor({
		extensions: [
			StarterKit,
			TextAlign.configure({ types: ["heading", "paragraph"] }),
			TaskList,
			TaskItem.configure({ nested: true }),
		],
		content: value || "", // Provide fallback for empty value
		editorProps: {
			attributes: {
				class: cn("outline-none min-h-[128px] px-3 py-2", className),
			},
		},
		onUpdate: ({ editor }) => {
			if (onChange) onChange(editor.getHTML());
		},
		editable: editable,
		immediatelyRender: false,
	});

	// Sync editor content when value prop changes
	useEffect(() => {
		if (editor && value !== undefined) {
			const currentContent = editor.getHTML();
			// Only update if the content is actually different
			if (currentContent !== value) {
				editor.commands.setContent(value);
			}
		}
	}, [editor, value]);

	return (
		<EditorContext.Provider value={{ editor }}>
			{editable && (
				<div className="tiptap-button-group" data-orientation="horizontal">
					<MarkButton type="bold" />
					<MarkButton type="italic" />
					<MarkButton type="underline" />
					<Separator />
					<ListButton type="bulletList" />
					<ListButton type="orderedList" />
					<ListButton type="taskList" />
					<Separator />
					<HeadingButton level={1}></HeadingButton>
					<HeadingButton level={2}></HeadingButton>
					<HeadingButton level={3}></HeadingButton>
					<Separator />
					<TextAlignButton align="left" />
					<TextAlignButton align="center" />
					<TextAlignButton align="right" />
					<TextAlignButton align="justify" />
					<Separator />
					<UndoRedoButton action="undo" />
					<UndoRedoButton action="redo" />
				</div>
			)}
			<EditorContent editor={editor} role="presentation" />
		</EditorContext.Provider>
	);
};
