"use client";

import { RealEstateDetailsType } from "@/db/services/realEstateDetails.service";

const CaseDialogDetails = ({ data }: { data: RealEstateDetailsType }) => {
	// Create structured details from the real estate data
	const details = [
		{ label: "Year Built", value: data.yearBuilt || "Not specified" },
		{ label: "Property Type", value: data.propertyType || "Not specified" },
		{
			label: "Historical Designation",
			value: data.historicalDesignation || "No",
		},
		{
			label: "Lot Size",
			value: `${data.lotSize || 0} ${data.lotSizeUnit || "sqft"}`,
		},
		{
			label: "Home Size",
			value: `${data.homeSize || 0} ${data.homeSizeUnit || "sqft"}`,
		},
		{ label: "Bedrooms", value: data.bedrooms || 0 },
		{ label: "Bathrooms", value: data.bathrooms || 0 },
		{ label: "Parking Type", value: data.parkingType || "None" },
	];

	return (
		<div className="my-6">
			<h1 className="text-2xl font-lora">Details</h1>
			<div className="grid grid-cols-2 gap-5 my-2 sm:grid-cols-4">
				{details.map((item, i) => (
					<div
						key={i}
						className="flex flex-col items-start justify-between w-full gap-2 px-1 py-4 text-lg font-manrope"
					>
						<label className="font-bold truncate">{item.label}</label>
						<span className="font-normal">{item.value}</span>
					</div>
				))}
			</div>
		</div>
	);
};

export default CaseDialogDetails;
