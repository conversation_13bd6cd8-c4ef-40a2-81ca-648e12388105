"use client";
import SearchSection from "@/components/home/<USER>";
import Cases from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import useSearchStore from "@/stores/search";

export default function Home() {
	const { hasSearched } = useSearchStore();

	return (
		<div className="z-0">
			{!hasSearched && <Hero />}
			{hasSearched && <SearchSection />}
			{!hasSearched && <Cases />}
			<Footer />
		</div>
	);
}
