"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useSignIn } from "@clerk/nextjs";
import { EmailLinkFactor, SignInFirstFactor } from "@clerk/types";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

const FormSchema = z.object({
	username: z.string().min(2, {
		message: "Username must be at least 2 characters.",
	}),
});

export default function SignInForm() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [verified, setVerified] = useState(false);
	const [verifying, setVerifying] = useState(false);
	const [error, setError] = useState("");
	const [cooldown, setCooldown] = useState(0);
	const [emailAddressId, setEmailAddressId] = useState<string | null>(null);
	const { signIn, isLoaded } = useSignIn();
	const redirectUrl = searchParams?.get("redirect_url") || "/";

	// Add a useEffect to handle navigation when verified changes
	useEffect(() => {
		if (verified) {
			router.push(redirectUrl);
		}
	}, [verified, router, redirectUrl]);

	// Handle cooldown countdown - using a simpler approach without refs
	useEffect(() => {
		let intervalId: NodeJS.Timeout | null = null;

		if (cooldown > 0) {
			// Start the interval
			intervalId = setInterval(() => {
				setCooldown((prevCooldown) => Math.max(0, prevCooldown - 1));
			}, 1000);
		}

		// Clean up the interval when component unmounts or cooldown changes
		return () => {
			if (intervalId) {
				clearInterval(intervalId);
			}
		};
	}, [cooldown]);

	const form = useForm<z.infer<typeof FormSchema>>({
		resolver: zodResolver(FormSchema),
		defaultValues: {
			username: "",
		},
	});

	if (!isLoaded) {
		return null;
	}

	const { startEmailLinkFlow, cancelEmailLinkFlow } =
		signIn.createEmailLinkFlow();

	const sendEmailLink = async (emailAddressId: string) => {
		try {
			// Dynamically set the host domain for dev and prod
			const protocol = window.location.protocol;
			const host = window.location.host;

			setCooldown(60);

			// Send the user an email with the email link
			const signInAttempt = await startEmailLinkFlow({
				emailAddressId,
				redirectUrl: `${protocol}//${host}/sign-in/verify`,
			});

			// Start cooldown - set this AFTER the successful API call

			// Check the verification result
			const verification = signInAttempt.firstFactorVerification;

			// Handle if verification expired
			if (verification.status === "expired") {
				setError("The email link has expired.");
			}

			// Handle if user visited the link and completed sign-in from /sign-in/verify
			if (verification.verifiedFromTheSameClient()) {
				setVerifying(false);
				setVerified(true);
			}
		} catch (err: unknown) {
			console.error("Error sending email:", JSON.stringify(err, null, 2));
			setError("An error occurred sending email link.");
		}
	};

	async function onSubmit(data: z.infer<typeof FormSchema>) {
		setVerified(false);
		setError("");

		if (!isLoaded || !signIn) return null;

		// Start the sign-in process using the email provided
		try {
			const { supportedFirstFactors } = await signIn.create({
				identifier: data.username,
			});

			setVerifying(true);

			// Filter the returned array to find the 'email_link' entry
			const isEmailLinkFactor = (
				factor: SignInFirstFactor
			): factor is EmailLinkFactor => {
				return factor.strategy === "email_link";
			};
			const emailLinkFactor = supportedFirstFactors?.find(isEmailLinkFactor);

			if (!emailLinkFactor) {
				setError("Email link factor not found");
				return;
			}

			const id = emailLinkFactor.emailAddressId;
			// console.log("Setting emailAddressId:", id);
			setEmailAddressId(id);
			await sendEmailLink(id);
		} catch (err: any) {
			// console.error(JSON.stringify(err, null, 2));
			setError(err.clerkError ? err.errors[0].message : "An error occurred.");
		}
	}

	function reset() {
		setVerifying(false);
		setError("");
		setCooldown(0);
		setEmailAddressId(null);
		cancelEmailLinkFlow();
	}

	async function handleResend() {
		if (cooldown > 0) {
			console.log("Cannot resend: cooldown active");
			return;
		}

		if (!emailAddressId) {
			console.log("Cannot resend: no emailAddressId");
			return;
		}

		console.log("Attempting to resend email");
		await sendEmailLink(emailAddressId);
	}

	if (error) {
		return (
			<div className="flex flex-col items-center">
				<p>Error: {error}</p>
				<div className="flex gap-4 mt-8">
					<Button onClick={reset}>Go back</Button>
				</div>
			</div>
		);
	}

	if (verifying) {
		return (
			<div className="flex flex-col items-center">
				<p>Check your email and visit the link that was sent to you.</p>
				<div className="flex gap-4 mt-8">
					<Button onClick={reset}>Go back</Button>
					<Button
						onClick={handleResend}
						disabled={cooldown > 0 || !emailAddressId}
						className="min-w-[140px]"
					>
						{cooldown > 0 ? `Resend (${cooldown}s)` : "Resend Email"}
					</Button>
				</div>
				<div className="mt-4 text-sm text-gray-500">
					{cooldown > 0 &&
						`You can request another email after ${cooldown} seconds.`}
				</div>
			</div>
		);
	}

	return (
		<>
			<p className="mb-4 text-lg font-normal text-slate-900 font-manrope">
				Welcome. Please enter your details.
			</p>
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="w-2/3 space-y-6"
				>
					<FormField
						control={form.control}
						name="username"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="text-base font-normal sr-only text-slate-900 font-manrope">
									Username
								</FormLabel>
								<FormControl>
									<Input placeholder="Username" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<Button type="submit" className="w-full font-bold font-manrope">
						Continue
					</Button>
				</form>
			</Form>
		</>
	);
}
