import { Skeleton } from "@/components/ui/skeleton";

const ScheduleListSkeleton = () => {
	return (
		<>
			{Array.from({ length: 5 }).map((_, index: number) => (
				<div
					key={index}
					className={`p-6 border border-b-1 border-b-[#B6B6B6] flex flex-col ${
						index === 0 ? "rounded-t-[11px]" : ""
					}`}
				>
					<div className="flex flex-col gap-5">
						<div className="flex flex-row justify-between">
							<div className="flex flex-col gap-1">
								<Skeleton className="w-32 h-4" />
								<Skeleton className="w-24 h-4" />
								<Skeleton className="w-20 h-4" />
							</div>
						</div>
						<div className="flex flex-row items-center gap-4">
							<Skeleton className="w-10 h-10 rounded-full" />
							<Skeleton className="w-16 h-4" />
						</div>
					</div>
				</div>
			))}
		</>
	);
};

export default ScheduleListSkeleton;
