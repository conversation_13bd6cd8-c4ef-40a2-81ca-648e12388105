import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { ContactSectionProps } from "./types";

// Extract contact display logic
export const ContactSection = ({
	backPage,
	contact,
	setContact,
	contactUsers,
}: ContactSectionProps) => (
	<div>
		<label className="block mb-1 text-base font-bold">
			Contact {backPage === "MG Exclusives" ? " Concierge" : ""}
		</label>
		{backPage !== "MG Exclusives" ? (
			<Select value={contact} onValueChange={setContact}>
				<SelectTrigger className="w-full">
					<SelectValue placeholder="Select a contact" />
				</SelectTrigger>
				<SelectContent>
					{contactUsers
						.map((user) => (
							<SelectItem key={user.id} value={user.id}>
								{user.userName}
							</SelectItem>
						))
						.filter(Boolean)}
				</SelectContent>
			</Select>
		) : null}
	</div>
);
