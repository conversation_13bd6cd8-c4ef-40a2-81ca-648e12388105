{"id": "1ef70242-9e02-4117-a894-7504178d71a6", "prevId": "544d6920-b29a-4d03-a5f0-7a043a67a125", "version": "7", "dialect": "postgresql", "tables": {"public.favorites_listings": {"name": "favorites_listings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_listing_favorite_unique": {"name": "user_listing_favorite_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "favorites_user_id_idx": {"name": "favorites_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "favorites_listing_id_idx": {"name": "favorites_listing_id_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"favorites_listings_user_id_users_id_fk": {"name": "favorites_listings_user_id_users_id_fk", "tableFrom": "favorites_listings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "favorites_listings_listing_id_listings_id_fk": {"name": "favorites_listings_listing_id_listings_id_fk", "tableFrom": "favorites_listings", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.geographic_areas": {"name": "geographic_areas", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"area_name_idx": {"name": "area_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.searcher_areas": {"name": "searcher_areas", "schema": "", "columns": {"searcher_id": {"name": "searcher_id", "type": "text", "primaryKey": false, "notNull": true}, "area_id": {"name": "area_id", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"searcher_area_searcher_idx": {"name": "searcher_area_searcher_idx", "columns": [{"expression": "searcher_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "searcher_area_area_idx": {"name": "searcher_area_area_idx", "columns": [{"expression": "area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"searcher_areas_searcher_id_users_id_fk": {"name": "searcher_areas_searcher_id_users_id_fk", "tableFrom": "searcher_areas", "tableTo": "users", "columnsFrom": ["searcher_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "searcher_areas_area_id_geographic_areas_id_fk": {"name": "searcher_areas_area_id_geographic_areas_id_fk", "tableFrom": "searcher_areas", "tableTo": "geographic_areas", "columnsFrom": ["area_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"searcher_areas_searcher_id_area_id_pk": {"name": "searcher_areas_searcher_id_area_id_pk", "columns": ["searcher_id", "area_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "clerk_id": {"name": "clerk_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'buyer'"}, "sub_category": {"name": "sub_category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "user_name": {"name": "user_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mg_exclusives": {"name": "mg_exclusives", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "two_factor_auth": {"name": "two_factor_auth", "type": "two_factor_auth", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'disabled'"}, "communication_type": {"name": "communication_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"username_idx": {"name": "username_idx", "columns": [{"expression": "user_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "email_idx": {"name": "email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "role_idx": {"name": "role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subcategory_idx": {"name": "subcategory_idx", "columns": [{"expression": "sub_category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_user_name_unique": {"name": "users_user_name_unique", "nullsNotDistinct": false, "columns": ["user_name"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_subscriptions": {"name": "user_subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "membership_plan": {"name": "membership_plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "billing_status": {"name": "billing_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'current'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_subscription_idx": {"name": "user_subscription_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "subscription_plan_idx": {"name": "subscription_plan_idx", "columns": [{"expression": "membership_plan", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_status_idx": {"name": "subscription_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "billing_status_idx": {"name": "billing_status_idx", "columns": [{"expression": "billing_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_dates_idx": {"name": "subscription_dates_idx", "columns": [{"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_subscriptions_user_id_users_id_fk": {"name": "user_subscriptions_user_id_users_id_fk", "tableFrom": "user_subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_site_access": {"name": "user_site_access", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "site_access": {"name": "site_access", "type": "site_access", "typeSchema": "public", "primaryKey": false, "notNull": true}, "has_access": {"name": "has_access", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_site_access_idx": {"name": "user_site_access_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "site_access", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "site_access_user_idx": {"name": "site_access_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_site_access_user_id_users_id_fk": {"name": "user_site_access_user_id_users_id_fk", "tableFrom": "user_site_access", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_geographic_access": {"name": "user_geographic_access", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "area_id": {"name": "area_id", "type": "text", "primaryKey": false, "notNull": true}, "access_level": {"name": "access_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'read'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_geo_access_idx": {"name": "user_geo_access_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "geo_access_user_idx": {"name": "geo_access_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "geo_access_area_idx": {"name": "geo_access_area_idx", "columns": [{"expression": "area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_geographic_access_user_id_users_id_fk": {"name": "user_geographic_access_user_id_users_id_fk", "tableFrom": "user_geographic_access", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_geographic_access_area_id_geographic_areas_id_fk": {"name": "user_geographic_access_area_id_geographic_areas_id_fk", "tableFrom": "user_geographic_access", "tableTo": "geographic_areas", "columnsFrom": ["area_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_connections": {"name": "user_connections", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "buyer_id": {"name": "buyer_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_role": {"name": "provider_role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"buyer_provider_unique": {"name": "buyer_provider_unique", "columns": [{"expression": "buyer_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "connection_buyer_id_idx": {"name": "connection_buyer_id_idx", "columns": [{"expression": "buyer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "connection_provider_id_idx": {"name": "connection_provider_id_idx", "columns": [{"expression": "provider_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_connections_buyer_id_users_id_fk": {"name": "user_connections_buyer_id_users_id_fk", "tableFrom": "user_connections", "tableTo": "users", "columnsFrom": ["buyer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_connections_provider_id_users_id_fk": {"name": "user_connections_provider_id_users_id_fk", "tableFrom": "user_connections", "tableTo": "users", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listings": {"name": "listings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "listing_type": {"name": "listing_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "amenities": {"name": "amenities", "type": "text[]", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "hide_street_address": {"name": "hide_street_address", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "favorite_count": {"name": "favorite_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "neighborhood": {"name": "neighborhood", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "visibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'private'"}, "parking_type": {"name": "parking_type", "type": "parking_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'None'"}, "parking_spaces": {"name": "parking_spaces", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "historical_designation": {"name": "historical_designation", "type": "historical_designation", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'None'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"slug_idx": {"name": "slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "listing_user_id_idx": {"name": "listing_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listing_type_idx": {"name": "listing_type_idx", "columns": [{"expression": "listing_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listing_visibility_idx": {"name": "listing_visibility_idx", "columns": [{"expression": "visibility", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listings_user_id_users_id_fk": {"name": "listings_user_id_users_id_fk", "tableFrom": "listings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "latitude": {"name": "latitude", "type": "numeric(9, 6)", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "numeric(9, 6)", "primaryKey": false, "notNull": false}, "map_zoom_level": {"name": "map_zoom_level", "type": "integer", "primaryKey": false, "notNull": false}, "neighborhood": {"name": "neighborhood", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "walk_ability_score": {"name": "walk_ability_score", "type": "integer", "primaryKey": false, "notNull": false}, "school_district": {"name": "school_district", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"location_listing_id_idx": {"name": "location_listing_id_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "city_country_idx": {"name": "city_country_idx", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "country", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"locations_listing_id_listings_id_fk": {"name": "locations_listing_id_listings_id_fk", "tableFrom": "locations", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.real_estate_details": {"name": "real_estate_details", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "property_type": {"name": "property_type", "type": "property_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'house'"}, "bedrooms": {"name": "bedrooms", "type": "numeric(5, 1)", "primaryKey": false, "notNull": true}, "bathrooms": {"name": "bathrooms", "type": "numeric(5, 1)", "primaryKey": false, "notNull": true}, "home_size": {"name": "home_size", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "home_size_unit": {"name": "home_size_unit", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'sqft'"}, "lot_size": {"name": "lot_size", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false}, "lot_size_unit": {"name": "lot_size_unit", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'sqft'"}, "parking_type": {"name": "parking_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "parking_spaces": {"name": "parking_spaces", "type": "numeric(5, 0)", "primaryKey": false, "notNull": false}, "year_built": {"name": "year_built", "type": "numeric(4, 0)", "primaryKey": false, "notNull": false}, "historical_designation": {"name": "historical_designation", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'None'"}, "architect_name": {"name": "architect_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "interior_designer": {"name": "interior_designer", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "views": {"name": "views", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "historic_details": {"name": "historic_details", "type": "text", "primaryKey": false, "notNull": false}, "hoa_fees": {"name": "hoa_fees", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "taxes": {"name": "taxes", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"re_listing_id_idx": {"name": "re_listing_id_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"real_estate_details_listing_id_listings_id_fk": {"name": "real_estate_details_listing_id_listings_id_fk", "tableFrom": "real_estate_details", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'image'"}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": false}, "preview": {"name": "preview", "type": "text", "primaryKey": false, "notNull": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": false}, "zl_config": {"name": "zl_config", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"media_listing_id_idx": {"name": "media_listing_id_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"media_listing_id_listings_id_fk": {"name": "media_listing_id_listings_id_fk", "tableFrom": "media", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inquiries": {"name": "inquiries", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"inquiry_listing_id_idx": {"name": "inquiry_listing_id_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "inquiry_sender_id_idx": {"name": "inquiry_sender_id_idx", "columns": [{"expression": "sender_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "inquiry_status_idx": {"name": "inquiry_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inquiries_listing_id_listings_id_fk": {"name": "inquiries_listing_id_listings_id_fk", "tableFrom": "inquiries", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inquiries_sender_id_users_id_fk": {"name": "inquiries_sender_id_users_id_fk", "tableFrom": "inquiries", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_recommendations": {"name": "listing_recommendations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "searcher_id": {"name": "searcher_id", "type": "text", "primaryKey": false, "notNull": true}, "buyer_id": {"name": "buyer_id", "type": "text", "primaryKey": false, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"recommendation_unique": {"name": "recommendation_unique", "columns": [{"expression": "searcher_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "buyer_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "recommendation_searcher_idx": {"name": "recommendation_searcher_idx", "columns": [{"expression": "searcher_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recommendation_buyer_idx": {"name": "recommendation_buyer_idx", "columns": [{"expression": "buyer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recommendation_listing_idx": {"name": "recommendation_listing_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recommendation_status_idx": {"name": "recommendation_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_recommendations_searcher_id_users_id_fk": {"name": "listing_recommendations_searcher_id_users_id_fk", "tableFrom": "listing_recommendations", "tableTo": "users", "columnsFrom": ["searcher_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_recommendations_buyer_id_users_id_fk": {"name": "listing_recommendations_buyer_id_users_id_fk", "tableFrom": "listing_recommendations", "tableTo": "users", "columnsFrom": ["buyer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_recommendations_listing_id_listings_id_fk": {"name": "listing_recommendations_listing_id_listings_id_fk", "tableFrom": "listing_recommendations", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_history": {"name": "payment_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "last_payment_date": {"name": "last_payment_date", "type": "date", "primaryKey": false, "notNull": false}, "last_payment_amount": {"name": "last_payment_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "next_payment_due_date": {"name": "next_payment_due_date", "type": "date", "primaryKey": false, "notNull": false}, "next_payment_amount": {"name": "next_payment_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payment_history_user_id_idx": {"name": "payment_history_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payment_history_user_id_users_id_fk": {"name": "payment_history_user_id_users_id_fk", "tableFrom": "payment_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schedules": {"name": "schedules", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "call_name": {"name": "call_name", "type": "text", "primaryKey": false, "notNull": false}, "sender_id": {"name": "sender_id", "type": "text", "primaryKey": false, "notNull": true}, "recipients": {"name": "recipients", "type": "jsonb", "primaryKey": false, "notNull": false}, "schedule_time": {"name": "schedule_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"schedule_sender_idx": {"name": "schedule_sender_idx", "columns": [{"expression": "sender_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "schedule_listing_idx": {"name": "schedule_listing_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "schedule_recipients_gin_idx": {"name": "schedule_recipients_gin_idx", "columns": [{"expression": "recipients", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}}, "foreignKeys": {"schedules_sender_id_users_id_fk": {"name": "schedules_sender_id_users_id_fk", "tableFrom": "schedules", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "schedules_listing_id_listings_id_fk": {"name": "schedules_listing_id_listings_id_fk", "tableFrom": "schedules", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_review_requests": {"name": "listing_review_requests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "listing_id": {"name": "listing_id", "type": "text", "primaryKey": false, "notNull": true}, "review_status": {"name": "review_status", "type": "review_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'drafts'"}, "request_date": {"name": "request_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "response_date": {"name": "response_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "approved_date": {"name": "approved_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "declined_date": {"name": "declined_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "archived_date": {"name": "archived_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "reviewer_id": {"name": "reviewer_id", "type": "text", "primaryKey": false, "notNull": false}, "reviewer_type": {"name": "reviewer_type", "type": "reviewer_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'admin'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"review_user_listing_unique": {"name": "review_user_listing_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "review_user_id_idx": {"name": "review_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_status_idx": {"name": "review_status_idx", "columns": [{"expression": "review_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_approved_date_idx": {"name": "review_approved_date_idx", "columns": [{"expression": "approved_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_declined_date_idx": {"name": "review_declined_date_idx", "columns": [{"expression": "declined_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_archived_date_idx": {"name": "review_archived_date_idx", "columns": [{"expression": "archived_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_reviewer_id_idx": {"name": "review_reviewer_id_idx", "columns": [{"expression": "reviewer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_reviewer_type_idx": {"name": "review_reviewer_type_idx", "columns": [{"expression": "reviewer_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_review_requests_user_id_users_id_fk": {"name": "listing_review_requests_user_id_users_id_fk", "tableFrom": "listing_review_requests", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_review_requests_listing_id_listings_id_fk": {"name": "listing_review_requests_listing_id_listings_id_fk", "tableFrom": "listing_review_requests", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_review_requests_reviewer_id_users_id_fk": {"name": "listing_review_requests_reviewer_id_users_id_fk", "tableFrom": "listing_review_requests", "tableTo": "users", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.historical_designation": {"name": "historical_designation", "schema": "public", "values": ["None", "National Register", "State Register", "Local Landmark", "Heritage Site", "Other"]}, "public.parking_type": {"name": "parking_type", "schema": "public", "values": ["Garage", "Carport", "Street", "Driveway", "None", "Other"]}, "public.property_type": {"name": "property_type", "schema": "public", "values": ["house", "penthouse", "apartment", "condo", "townhouse", "villa", "land", "commercial", "other"]}, "public.review_status": {"name": "review_status", "schema": "public", "values": ["approved", "pending", "drafts", "archived", "declined"]}, "public.reviewer_type": {"name": "reviewer_type", "schema": "public", "values": ["advertiser", "admin"]}, "public.role": {"name": "role", "schema": "public", "values": ["admin", "buyer", "searcher", "advertiser", "concierge", "combination"]}, "public.site_access": {"name": "site_access", "schema": "public", "values": ["real_estate", "aviation", "yachts", "automobiles", "fine_art_collectibles"]}, "public.two_factor_auth": {"name": "two_factor_auth", "schema": "public", "values": ["disabled", "sms", "authenticator"]}, "public.visibility": {"name": "visibility", "schema": "public", "values": ["private", "public", "off-market"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}