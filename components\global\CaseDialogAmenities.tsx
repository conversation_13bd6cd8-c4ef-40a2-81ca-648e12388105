"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState } from "react";
import CaseDialogAllAmenities from "./CaseDialogAllAmenities";
import { amenities, features } from "@/constant/amenities";

type Props = {
  amenitiesData: string[] | null;
};

const CaseDialogAmenities = ({ amenitiesData = [] }: Props) => {
  const [showAll, setShowAll] = useState(false);
  const handleValueChange = (value: string) => {
    setShowAll(value === "amenities");
  };

  // Ensure amenitiesData is an array
  const safeAmenitiesData = amenitiesData || [];

  // Filter amenities to only show those that match values in amenitiesData
  const filteredAmenities = amenities.filter((item) =>
    safeAmenitiesData.includes(item.name)
  );

  // Check if there are any features that match amenitiesData
  const hasFeatures = features.some((category) =>
    category.items.some((item) =>
      safeAmenitiesData.includes(item.name as string)
    )
  );

  // Don't render anything if no amenities
  if (filteredAmenities.length === 0 && !hasFeatures) {
    return null;
  }

  return (
		<div className="my-6">
			<h1 className="mb-4 text-2xl font-lora">Featured amenities</h1>
			<div className="grid grid-cols-4 gap-4 my-2 font-manrope">
				{filteredAmenities.map((item) => {
					const IconComponent = item.icon; // Get the icon component
					return (
						<div key={item.name} className="flex flex-row items-center gap-4">
							<div className="text-xl">
								<IconComponent /> {/* Render as React component */}
							</div>
							<div className="text-base font-bold">{item.label}</div>
						</div>
					);
				})}
			</div>

			{/* Only render Accordion if there are matching features */}
			{hasFeatures && (
				<Accordion type="single" collapsible onValueChange={handleValueChange}>
					<AccordionItem value="amenities">
						<AccordionTrigger className="flex-row-reverse items-center justify-center h-10 p-2 text-base font-normal font-manrope bg-zinc-100">
							{`See ${showAll ? "less" : "all amenities"}`}
						</AccordionTrigger>
						<AccordionContent>
							<CaseDialogAllAmenities amenitiesData={safeAmenitiesData} />
						</AccordionContent>
					</AccordionItem>
				</Accordion>
			)}
		</div>
	);
};

export default CaseDialogAmenities;
