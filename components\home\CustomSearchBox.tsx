"use client";
import { Input } from "../ui/input";
import { IoSearchOutline } from "react-icons/io5";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import useSearchStore from "@/stores/search";
import FollowupSearchPanel from "../search/FollowupSearchPanel";
import dynamic from "next/dynamic";
import useAppStore from "@/stores/app";
const VoiceSearch = dynamic(() => import("./VoiceSearch"), { ssr: false });

export interface FacetResponse {
	facets: Record<string, any>;
	missing: string[];
	clarifyNeeded: boolean;
	isSubjective: boolean;
}

const CustomSearchBox = () => {
	const [missingFacets, setMissingFacets] = useState<string[]>([]);
	const [facets, setFacets] = useState<Record<string, any>>({});
	const [inputValue, setInputValue] = useState(""); // Local input state
	const { isDialogOpen } = useAppStore();
	const {
		loading,
		initialQuery,
		searchValue,
		setLoading,
		setError,
		setShowResults,
		setInitialQuery,
		setSearchValue,
		setHasSearched,
		isNewSearch,
		setIsNewSearch,
	} = useSearchStore();

	const handleSearch = async () => {
		// Use inputValue as the search query when user triggers search
		const searchQuery = inputValue || searchValue;
		if (!searchQuery.trim()) return;

		// Update the search value in store when actually searching
		setSearchValue(searchQuery);
		setLoading(true);
		setError(null);
		setInitialQuery(searchQuery);

		try {
			const response = await fetch("/api/nlp/facets", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ query: searchQuery }),
			});

			const data: FacetResponse = await response.json();

			if (!response.ok) {
				throw new Error("Failed to search");
			}

			setFacets(data.facets);
			setMissingFacets(data.missing);

			setShowResults(true);
			setHasSearched(true);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
			setIsNewSearch(true);
			// Don't clear searchValue here - we need it for SearchResultsDisplay
			setInputValue(""); // Only clear the input field
		}
	};

	const resetState = (): void => {
		setMissingFacets([]);
		setFacets({});
		setIsNewSearch(false);
		setShowResults(false);
		// Clear search value when resetting to start fresh
		setSearchValue("");
		setInputValue("");
	};

	// Hide the search box when dialog is open
	if (isDialogOpen) {
		return null;
	}

	return (
		<div
			id="hero-search-section"
			className="w-full bg-[var(--search-panel-foreground)] rounded-lg text-gray-200 h-full transition-all duration-500 ease-in-out"
		>
			{!isNewSearch ? (
				<div className="flex flex-row items-center gap-2 px-2 pt-1 pb-0! transition-opacity duration-500 ease-in-out">
					<IoSearchOutline className="hidden ml-2 text-2xl text-white transition-opacity duration-500 ease-in-out md:block" />
					<span className="text-MG-STONE-WHITE leading-normal text-nowrap hidden font-manrope md:inline text-base !font-bold md:text-base transition-opacity duration-500 ease-in-out">
						Search for
					</span>
					<div className="relative flex-1 transition-all duration-500 ease-in-out">
						<Input
							className="rounded border-none p-1 !pl-0 font-manrope placeholder:text-[var(--search-panel-placeholder)] text-neutral-300 !text-base font-normal! leading-normal focus-visible:ring-0 focus-visible:ring-offset-0 selection:bg-blue-200 selection:text-black transition-all duration-500 ease-in-out"
							type="text"
							placeholder="a three-bedroom house in Times Square"
							value={inputValue || searchValue}
							disabled={loading}
							onChange={(e) => {
								setInputValue(e.target.value); // Only update local input state
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleSearch();
								}
							}}
						/>
						{loading && (
							<div className="absolute transition-opacity duration-500 ease-in-out -translate-y-1/2 right-2 top-1/2">
								<Loader2 className="w-4 h-4 text-white animate-spin" />
							</div>
						)}
					</div>
					<VoiceSearch />
				</div>
			) : (
				<div className="transition-all duration-500 ease-in-out">
					<FollowupSearchPanel
						initialQuery={initialQuery}
						resetState={resetState}
						setIsNewSearch={setIsNewSearch}
						loading={loading}
						handleSearch={handleSearch}
						missingFacets={missingFacets}
						facets={facets}
						setFacets={setFacets}
					/>
				</div>
			)}
		</div>
	);
};

export default CustomSearchBox;
