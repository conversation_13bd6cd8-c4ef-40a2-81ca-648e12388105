import { db } from "../index";
import { schedules, Recipients } from "../schema/schedules";
import { eq, or, sql } from "drizzle-orm";
import { enrichListingsWithCompleteData } from "./listings.service";

export type ScheduleType = typeof schedules.$inferInsert;
export type ScheduleSelectType = typeof schedules.$inferSelect;

// Helper function to validate recipients object
export const validateRecipients = (
	recipients: any
): recipients is Recipients => {
	if (!recipients || typeof recipients !== "object") return false;
	return Object.values(recipients).every((value) => typeof value === "boolean");
};

// Helper function to create recipients object
export const createRecipientsObject = (
	userIds: string[],
	defaultStatus: boolean = false
): Recipients => {
	return userIds.reduce(
		(acc, userId) => ({ ...acc, [userId]: defaultStatus }),
		{}
	);
};

// Helper function to get recipient IDs from recipients object
export const getRecipientIds = (recipients: Recipients): string[] => {
	return Object.keys(recipients);
};

// Helper function to get pending recipients
export const getPendingRecipients = (recipients: Recipients): string[] => {
	return (
		Object.entries(recipients)
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			.filter(([_, status]) => !status)
			.map(([userId]) => userId)
	);
};

export const createSchedule = async (
	schedule: Omit<ScheduleType, "createdAt" | "updatedAt">
) => {
	try {
		// Validate recipients before creating
		if (!validateRecipients(schedule.recipients)) {
			throw new Error("Invalid recipients format");
		}

		const newSchedule = await db
			.insert(schedules)
			.values({
				...schedule,
			})
			.returning();
		return { success: true, schedule: newSchedule[0] };
	} catch (error) {
		console.error("Error in createSchedule:", error);
		return { success: false, error: "Failed to create schedule" };
	}
};

export const getScheduleById = async (id: string) => {
	try {
		const schedule = await db.query.schedules.findFirst({
			where: eq(schedules.id, id),
		});
		return schedule || null;
	} catch (error) {
		console.error("Error in getScheduleById:", error);
		return null;
	}
};

export const updateSchedule = async (
	id: string,
	updateData: Partial<ScheduleType>
) => {
	try {
		// Validate recipients if it's being updated
		if (updateData.recipients && !validateRecipients(updateData.recipients)) {
			throw new Error("Invalid recipients format");
		}

		const updatedSchedule = await db
			.update(schedules)
			.set({ ...updateData, updatedAt: new Date() })
			.where(eq(schedules.id, id))
			.returning();
		return { success: true, schedule: updatedSchedule[0] };
	} catch (error) {
		console.error("Error in updateSchedule:", error);
		return { success: false, error: "Failed to update schedule" };
	}
};

export const deleteSchedule = async (id: string) => {
	try {
		await db.delete(schedules).where(eq(schedules.id, id));
		return { success: true };
	} catch (error) {
		console.error("Error in deleteSchedule:", error);
		return { success: false, error: "Failed to delete schedule" };
	}
};

export const getSchedulesBySenderId = async (senderId: string) => {
	try {
		const scheduleList = await db.query.schedules.findMany({
			where: eq(schedules.senderId, senderId),
		});
		return scheduleList;
	} catch (error) {
		console.error("Error in getSchedulesBySenderId:", error);
		return [];
	}
};

export const getSchedulesByReceiverId = async (receiverId: string) => {
	try {
		const scheduleList = await db.query.schedules.findMany({
			where: sql`${schedules.recipients} ? ${receiverId}`, // Fixed: Use parameter binding instead of string interpolation
		});
		return scheduleList;
	} catch (error) {
		console.error("Error in getSchedulesByReceiverId:", error);
		return [];
	}
};

// New function to update recipient status
export const updateRecipientStatus = async (
	scheduleId: string,
	userId: string,
	status: boolean
) => {
	try {
		const updatedSchedule = await db
			.update(schedules)
			.set({
				recipients: sql`jsonb_set(${schedules.recipients}, ${`{${userId}}`}, ${status.toString()})`,
				updatedAt: new Date(),
			})
			.where(eq(schedules.id, scheduleId))
			.returning();

		return { success: true, schedule: updatedSchedule[0] };
	} catch (error) {
		console.error("Error in updateRecipientStatus:", error);
		return { success: false, error: "Failed to update recipient status" };
	}
};

// New function to add recipient
export const addRecipient = async (
	scheduleId: string,
	userId: string,
	status: boolean = false
) => {
	try {
		const updatedSchedule = await db
			.update(schedules)
			.set({
				recipients: sql`${schedules.recipients} || jsonb_build_object(${userId}, ${status})`,
				updatedAt: new Date(),
			})
			.where(eq(schedules.id, scheduleId))
			.returning();

		return { success: true, schedule: updatedSchedule[0] };
	} catch (error) {
		console.error("Error in addRecipient:", error);
		return { success: false, error: "Failed to add recipient" };
	}
};

// New function to remove recipient
export const removeRecipient = async (scheduleId: string, userId: string) => {
	try {
		const updatedSchedule = await db
			.update(schedules)
			.set({
				recipients: sql`${schedules.recipients} - ${userId}`,
				updatedAt: new Date(),
			})
			.where(eq(schedules.id, scheduleId))
			.returning();

		return { success: true, schedule: updatedSchedule[0] };
	} catch (error) {
		console.error("Error in removeRecipient:", error);
		return { success: false, error: "Failed to remove recipient" };
	}
};

export const getSchedulesByUserId = async (userId: string) => {
	try {
		const scheduleList = await db.query.schedules.findMany({
			where: or(
				eq(schedules.senderId, userId),
				// Use JSONB ? operator to check if userId exists as a key in recipients
				sql`${schedules.recipients} ? ${userId}`
			),
		});

		// Extract unique listing IDs from schedules
		const listingIds = [
			...new Set(
				scheduleList
					.map((schedule) => schedule.listingId)
					.filter((id): id is string => id !== null)
			),
		];

		// Use shared function to get all listing details with complete data
		const listingDetails = await enrichListingsWithCompleteData(listingIds);

		// Create a map for quick lookup of listing details
		const listingMap = new Map(
			listingDetails.map((listing) => [listing.id, listing])
		);

		// Enrich schedules with listing data using the map
		const enrichedSchedules = scheduleList.map((schedule) => {
			if (schedule.listingId && listingMap.has(schedule.listingId)) {
				return {
					...schedule,
					listing: listingMap.get(schedule.listingId),
				};
			}
			return schedule;
		});

		return enrichedSchedules;
	} catch (error) {
		console.error("Error in getSchedulesByUserId:", error);
		throw new Error("Failed to fetch schedules");
	}
};
