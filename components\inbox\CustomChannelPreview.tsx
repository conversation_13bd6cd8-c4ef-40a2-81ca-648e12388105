import Image from "next/image";
import { IoIosTrash } from "react-icons/io";
import { Attachment } from "stream-chat";

const CustomChannelPreview = (props: any) => {
  const { channel, active, setActiveChannel, ...rest } = props;
  const members = Object.values(channel.state.members || {});
  const imageUrl = channel.data.image || "";
  const receiver = members.find(
    (m: any) => (m as any).user_id !== rest?.me?.id
  ) as any;
  const receiverUser = receiver?.user;
  const receiverName = receiverUser?.name || receiverUser?.id || "Unknown";
  const lastMessage = channel.state.messages[channel.state.messages.length - 1];
  const lastMessageTime = lastMessage
    ? new Date(lastMessage.created_at).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })
    : "";
  const lastMessageText = lastMessage?.text || "";
  const isMyMessage = lastMessage?.user?.id === rest?.me?.id;
  const lastMessageDisplay = lastMessageText
    ? `${isMyMessage ? "You" : receiverName}: ${lastMessageText}`
    : "";

  const isFileMessage = lastMessage?.attachments?.some(
    (attachment: Attachment) => attachment.type === "file"
  );
  const fileMessageDisplay = isFileMessage
    ? `${isMyMessage ? "You" : receiverName} shared a file`
    : lastMessageDisplay;

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm("Are you sure you want to delete this chat?")) {
      try {
        await channel.delete();
      } catch (err) {
        alert("Failed to delete chat.");
        console.log(err);
      }
    }
  };

  return (
    <div
      className={`group font-manrope flex cursor-pointer items-center gap-5 rounded-[11px] py-4 pr-4 pl-5 ${
        active ? "bg-[#CACAD1]" : "hover:bg-gray-100"
      }`}
      onClick={() => setActiveChannel && setActiveChannel(channel)}
    >
      {imageUrl ? (
        <Image
          src={imageUrl}
          width={40}
          height={40}
          className="w-10 h-10 rounded-full"
          alt={receiverName}
        />
      ) : (
        <div className="flex items-center justify-center w-10 h-10 font-bold text-gray-600 bg-gray-300 rounded-full">
          {receiverName?.[0]?.toUpperCase() || "?"}
        </div>
      )}
      <div className="flex-1 min-w-0">
        <div className="flex flex-row items-center justify-between">
          <div className="truncate text-base font-bold text-[#000000]">
            {receiverName}
          </div>
          <div className="ml-2 text-base text-gray-500 whitespace-nowrap">
            {lastMessageTime}
          </div>
        </div>
        <div className="relative flex items-center justify-between gap-2 text-sm text-gray-600 truncate">
          <span className="w-full">
            {fileMessageDisplay.length > 25
              ? `${fileMessageDisplay.substring(0, 25)}...`
              : fileMessageDisplay}
          </span>
          <button
            className="ml-2 text-gray-400 transition-opacity opacity-0 group-hover:opacity-100 hover:text-red-400 hover:cursor-pointer"
            onClick={handleDelete}
            title="Delete chat"
            tabIndex={-1}
          >
            <IoIosTrash className="w-6 h-6" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomChannelPreview;
