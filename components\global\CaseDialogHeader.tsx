"use client";
import Logo from "../ui/Logo";
import { <PERSON><PERSON> } from "../ui/button";
import ArchiveButton from "../ui/ArchiveButton";
import ReactivateButton from "../ui/ReactivateButton";
import PublishButton from "../ui/PublishButton";
import SendForReviewButton from "../ui/SendForReviewButton";
import DeclineButton from "../ui/DeclineButton";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { MdChevronLeft } from "react-icons/md";
import useAppStore from "@/stores/app";
import FavoriteButton from "../dashboard/cards/FavoriteButton";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { toggleFavorite } from "@/db/services/favoritesListings.service";
import useFavoritesStore from "@/stores/favorites";
import { useListingStatusActions } from "@/hooks/useListingStatusActions";
import { KeyedMutator } from "swr";
import { ReviewerTypeType, ReviewStatusType } from "@/db/schema/enums";
import PromoteButton from "../ui/PromoteButton";
import EditButton from "../ui/EditButton";

interface CaseDialogHeaderProps {
	backPage?: string;
	listingId: string;
	isFavored?: boolean;
	mutate?: KeyedMutator<any>;
	listingReviewRequestId?: string;
	reviewerType?: ReviewerTypeType | null;
	ownerId?: string;
	status?: ReviewStatusType | ReviewStatusType[];
	onEdit?: (listingId: string) => void; // Add onEdit prop
}

const CaseDialogHeader = ({
	backPage,
	listingId,
	isFavored,
	mutate,
	listingReviewRequestId,
	reviewerType,
	ownerId,
	status,
	onEdit, // Destructure onEdit prop
}: CaseDialogHeaderProps) => {
	// Get state for iframe and photos from app store
	const {
		dialogShowIframe,
		setDialogShowIframe,
		dialogShowPhotos,
		setDialogShowPhotos,
		dialogShowListings,
		setDialogShowListings,
	} = useAppStore();
	const { user: me } = useCurrentUser();
	const { setFavorite } = useFavoritesStore();
	const {
		archiveListing,
		reactivateListing,
		declineListing,
		approveListing,
		sendForReview,
	} = useListingStatusActions(mutate);

	// Handle favorite toggle
	const handleFavoriteToggle = async (newFavoriteState: boolean) => {
		if (listingId && me) {
			await toggleFavorite(me.id, listingId);
			// Update store to sync with other components
			setFavorite(listingId, newFavoriteState);
		}
	};

	// Handle archive action
	const handleArchive = async () => {
		if (listingReviewRequestId) {
			await archiveListing(listingReviewRequestId);
			setDialogShowIframe(false);
		}
	};

	// Handle reactivate action
	const handleReactivate = async () => {
		if (listingReviewRequestId) {
			await reactivateListing(listingReviewRequestId);
			setDialogShowIframe(false);
		}
	};

	// Handle decline action
	const handleDecline = async () => {
		if (listingReviewRequestId) {
			await declineListing(listingReviewRequestId);
			setDialogShowIframe(false);
		}
	};

	// Handle publish action
	const handlePublish = async () => {
		if (listingReviewRequestId) {
			await approveListing(listingReviewRequestId);
			setDialogShowIframe(false);
		}
	};

	// Handle send for review action
	const handleSendForReview = async () => {
		if (listingReviewRequestId && ownerId) {
			if (me?.role === "admin") {
				await sendForReview({
					listingReviewRequestId,
					reviewerId: ownerId,
					reviewerType: "advertiser",
				});
			} else {
				await sendForReview({
					listingReviewRequestId,
					reviewerType: "admin",
				});
			}
			setDialogShowIframe(false);
		}
	};

	// Handle promote action
	const handlePromote = async () => {
		if (listingReviewRequestId) {
			console.log("Promote button clicked for listing ID:", listingId);
		}
	};

	// Handle edit action
	const handleEditClick = () => {
		if (onEdit) {
			onEdit(listingId);
		}
	};

	// Get current listing status from props instead of slug
	const getCurrentListingStatus = () => {
		if (Array.isArray(status)) {
			return status[0]; // Use first status if it's an array
		}
		return status || null;
	};

	// Render admin action button based on listing status
	const renderAdminActionButton = (): JSX.Element | null => {
		const currentStatus = getCurrentListingStatus();

		switch (currentStatus) {
			case "approved":
				return <ArchiveButton variant="default" onArchive={handleArchive} />;
			case "pending":
				if (reviewerType !== "admin") return null; // Only show for admin reviewers
				return (
					<div className="flex gap-2">
						<EditButton onEdit={handleEditClick} />
						<PublishButton variant="default" onPublish={handlePublish} />
						<SendForReviewButton
							variant="outline"
							onSendForReview={handleSendForReview}
						/>
						<DeclineButton variant="destructive" onDecline={handleDecline} />
					</div>
				);
			case "declined":
				return null; // No button for declined status
			case "archived":
				return (
					<ReactivateButton variant="default" onReactivate={handleReactivate} />
				);
			default:
				return <ArchiveButton variant="default" onArchive={handleArchive} />;
		}
	};

	// Render advertiser action button based on listing status
	const renderAdvertiserActionButton = (): JSX.Element | null => {
		const currentStatus = getCurrentListingStatus();

		switch (currentStatus) {
			case "approved":
				return (
					<div className="flex gap-2">
						<PromoteButton variant="outline" onPromote={handlePromote} />
						<ArchiveButton variant="outline" onArchive={handleArchive} />
					</div>
				);
			case "pending":
				return reviewerType !== "admin" ? (
					<div className="flex gap-2">
						<EditButton onEdit={handleEditClick} />
						<SendForReviewButton
							variant="outline"
							onSendForReview={handleSendForReview}
						/>
					</div>
				) : null;
			case "drafts":
				return (
					<div className="flex gap-2">
						<SendForReviewButton
							variant="outline"
							onSendForReview={handleSendForReview}
						/>
						<ArchiveButton variant="outline" onArchive={handleArchive} />
					</div>
				);
			case "declined":
				return null; // No button for declined status
			case "archived":
				return null; // No action for archived listings
			default:
				return <ArchiveButton variant="default" onArchive={handleArchive} />;
		}
	};

	// Handle back button click based on current state
	const handleBackClick = () => {
		if (dialogShowIframe) {
			// Return from iframe view
			setDialogShowIframe(false);
			// If we came from gallery, we should return to gallery
			// Otherwise return to listing
			return;
		}
		if (dialogShowListings) {
			setDialogShowListings(false);
		}
		if (dialogShowPhotos) {
			setDialogShowPhotos(false);
		}
	};

	return (
		<div className="relative flex flex-row items-center justify-between w-full gap-4">
			{/* Left side button */}
			<div className="flex-shrink-0">
				{dialogShowIframe || dialogShowListings ? (
					// Back button when in iframe mode
					<Button
						variant={"secondary"}
						className="flex flex-row items-center justify-center"
						onClick={handleBackClick}
					>
						<MdChevronLeft />
						<span className="text-sm font-inter">
							{dialogShowPhotos && !dialogShowListings
								? "Back to gallery"
								: "Back to listing"}
						</span>
					</Button>
				) : (
					// Regular close dialog button
					<DialogPrimitive.Close asChild>
						<Button
							variant={"secondary"}
							className="flex flex-row items-center justify-center"
						>
							<MdChevronLeft />
							<span className="text-sm font-inter">
								{backPage ? `Back to ${backPage}` : "Back"}
							</span>
						</Button>
					</DialogPrimitive.Close>
				)}
			</div>

			{/* Logo centered using absolute positioning */}
			<div className="absolute transform -translate-x-1/2 left-1/2">
				<Logo className="w-auto h-12" />
			</div>

			{/* Right side button */}
			<div className="flex-shrink-0">
				{me?.role === "buyer" && typeof isFavored === "boolean" && (
					<FavoriteButton
						listingId={listingId}
						isFavored={isFavored}
						onToggleFavorite={handleFavoriteToggle}
					/>
				)}
				{me?.role === "admin" && renderAdminActionButton()}
				{me?.role === "advertiser" && renderAdvertiserActionButton()}
			</div>
		</div>
	);
};

export default CaseDialogHeader;
