import { NextRequest, NextResponse } from "next/server";
import {
	createSchedule,
	updateSchedule,
	deleteSchedule,
	getScheduleById,
	getSchedulesByUserId,
	updateRecipientStatus,
	validateRecipients,
} from "@/db/services/schedules.service";

export async function POST(req: NextRequest) {
	try {
		const body = await req.json();
		const { senderId, recipients, scheduleTime, callName, endTime, listingId } =
			body;

		if (!senderId || !recipients || !scheduleTime) {
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 }
			);
		}

		// Validate recipients format
		if (!validateRecipients(recipients)) {
			return NextResponse.json(
				{ error: "Invalid recipients format" },
				{ status: 400 }
			);
		}

		// Remove manual ID generation - database will auto-generate using simplifyUuidField()
		const result = await createSchedule({
			senderId,
			listingId,
			recipients,
			scheduleTime: new Date(scheduleTime),
			callName,
			endTime: new Date(endTime),
		});

		if (result.success) {
			return NextResponse.json(result.schedule, { status: 201 });
		} else {
			return NextResponse.json({ error: result.error }, { status: 400 });
		}
	} catch (error) {
		console.error("Error in POST /api/schedules:", error);
		return NextResponse.json(
			{ error: "Internal Server Error" },
			{ status: 500 }
		);
	}
}

export async function GET(req: NextRequest) {
	try {
		const { searchParams } = new URL(req.url);
		const userId = searchParams.get("userId");

		if (!userId) {
			return NextResponse.json(
				{ error: "userId is required" },
				{ status: 400 }
			);
		}

		const scheduleList = await getSchedulesByUserId(userId);

		return NextResponse.json(scheduleList);
	} catch (error) {
		console.error("Error in GET /api/schedules:", error);
		return NextResponse.json(
			{ error: "Internal Server Error" },
			{ status: 500 }
		);
	}
}

export async function PUT(req: NextRequest) {
	try {
		const { searchParams } = new URL(req.url);
		const id = searchParams.get("id");
		if (!id) {
			return NextResponse.json(
				{ error: "Schedule ID is required" },
				{ status: 400 }
			);
		}

		const body = await req.json();
		const { scheduleTime } = body;

		if (!scheduleTime) {
			return NextResponse.json(
				{ error: "scheduleTime is required" },
				{ status: 400 }
			);
		}

		const result = await updateSchedule(id, { scheduleTime });

		if (result.success) {
			return NextResponse.json(result.schedule);
		} else {
			return NextResponse.json({ error: result.error }, { status: 400 });
		}
	} catch (error) {
		console.error("Error in PUT /api/schedules:", error);
		return NextResponse.json(
			{ error: "Internal Server Error" },
			{ status: 500 }
		);
	}
}

export async function DELETE(req: NextRequest) {
	try {
		const { searchParams } = new URL(req.url);
		const id = searchParams.get("id");
		if (!id) {
			return NextResponse.json(
				{ error: "Schedule ID is required" },
				{ status: 400 }
			);
		}

		const result = await deleteSchedule(id);

		if (result.success) {
			return NextResponse.json({ message: "Schedule deleted successfully" });
		} else {
			return NextResponse.json({ error: result.error }, { status: 400 });
		}
	} catch (error) {
		console.error("Error in DELETE /api/schedules:", error);
		return NextResponse.json(
			{ error: "Internal Server Error" },
			{ status: 500 }
		);
	}
}

export async function PATCH(req: NextRequest) {
	try {
		const { searchParams } = new URL(req.url);
		const id = searchParams.get("id");
		const body = await req.json();
		const { updateId } = body;

		if (!id || !updateId) {
			return NextResponse.json(
				{ error: "Both id and updateId are required" },
				{ status: 400 }
			);
		}

		const schedule = await getScheduleById(id);

		if (!schedule) {
			return NextResponse.json(
				{ error: "Schedule not found" },
				{ status: 404 }
			);
		}

		// Check if updateId exists in recipients, handling both arrays and objects
		// Helper function to check if recipient exists
		const isRecipientFound = (
			recipients: string[] | Record<string, unknown> | null,
			id: string
		): boolean => {
			if (!recipients) return false;
			if (Array.isArray(recipients)) {
				return recipients.includes(id);
			}
			return id in recipients;
		};

		if (!isRecipientFound(schedule.recipients, updateId)) {
			return NextResponse.json(
				{ error: "updateId not found in recipients" },
				{ status: 400 }
			);
		}

		// Update recipient status to true
		const result = await updateRecipientStatus(id, updateId, true);

		if (result.success) {
			return NextResponse.json(result.schedule);
		} else {
			return NextResponse.json({ error: result.error }, { status: 400 });
		}
	} catch (error) {
		console.error("Error in PATCH /api/schedules:", error);
		return NextResponse.json(
			{ error: "Internal Server Error" },
			{ status: 500 }
		);
	}
}
