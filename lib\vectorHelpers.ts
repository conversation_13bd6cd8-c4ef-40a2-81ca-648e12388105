import { ReviewStatusType } from "@/db/schema";
import { getEmbedding } from "@/lib/openai";
import { Index } from "@upstash/vector";

const VECTOR_URL = process.env.UPSTASH_VECTOR_REST_URL;
const VECTOR_TOKEN = process.env.UPSTASH_VECTOR_REST_TOKEN;
if (!VECTOR_URL) {
	throw new Error("Environment variable UPSTASH_VECTOR_REST_URL is not set.");
}
if (!VECTOR_TOKEN) {
	throw new Error("Environment variable UPSTASH_VECTOR_REST_TOKEN is not set.");
}

const index = new Index({
	url: VECTOR_URL,
	token: VECTOR_TOKEN,
});

export interface ListingVectorData {
	// Listing fields
	id: string;
	title: string;
	description?: string;
	listingType: string;
	price: number;
	currency: string;
	amenities?: string[];
	neighborhood?: string;
	visibility: string;

	// Location fields
	address?: string;
	city?: string;
	state?: string;
	country?: string;
	zipCode?: string;
	schoolDistrict?: string;

	// Real Estate Details fields
	propertyType: string;
	bedrooms: number;
	bathrooms: number;
	homeSize: number;
	homeSizeUnit: string;
	lotSize?: number;
	lotSizeUnit?: string;
	parkingType?: string;
	parkingSpaces?: number;
	yearBuilt?: number;
	historicalDesignation?: string;
	architectName?: string;
	interiorDesigner?: string;
	views?: string;
	historicDetails?: string;
	hoaFees?: number;
	taxes?: number;

	reviewStatus: ReviewStatusType;
}

// Helper function to convert listing data to metadata format
function convertToMetadata(
	listingData: ListingVectorData
): Record<string, string> {
	const metadata: Record<string, string> = {};

	// Convert all fields to strings, handling undefined/null values
	Object.entries(listingData).forEach(([key, value]) => {
		if (value !== undefined && value !== null) {
			if (Array.isArray(value)) {
				metadata[key] = JSON.stringify(value);
			} else {
				metadata[key] = value.toString();
			}
		}
	});

	return metadata;
}

// Helper function to build content string efficiently
function buildContentString(listingData: ListingVectorData): string {
	const contentParts: string[] = [];

	// Add basic information with labels for consistency
	if (listingData.title) contentParts.push(`Title: ${listingData.title}`);
	if (listingData.description)
		contentParts.push(`Description: ${listingData.description}`);

	// Add structured information
	contentParts.push(`Property Type: ${listingData.propertyType}`);
	contentParts.push(`Price: ${listingData.price} ${listingData.currency}`);

	// Build location string only if components exist
	const locationParts = [
		listingData.address,
		listingData.city,
		listingData.state,
		listingData.country,
		listingData.zipCode,
	].filter(Boolean);

	if (locationParts.length > 0) {
		contentParts.push(`Location: ${locationParts.join(", ")}`);
	}

	// Add optional fields only if they exist
	if (listingData.neighborhood)
		contentParts.push(`Neighborhood: ${listingData.neighborhood}`);
	if (listingData.schoolDistrict)
		contentParts.push(`School District: ${listingData.schoolDistrict}`);

	// Add property details
	contentParts.push(
		`Bedrooms: ${listingData.bedrooms}`,
		`Bathrooms: ${listingData.bathrooms}`,
		`Home Size: ${listingData.homeSize} ${listingData.homeSizeUnit}`
	);

	// Add optional property details
	if (listingData.lotSize) {
		contentParts.push(
			`Lot Size: ${listingData.lotSize} ${listingData.lotSizeUnit}`
		);
	}

	if (listingData.parkingType) {
		const parkingInfo = listingData.parkingSpaces
			? `${listingData.parkingType} (${listingData.parkingSpaces} spaces)`
			: listingData.parkingType;
		contentParts.push(`Parking: ${parkingInfo}`);
	}

	// Add additional details
	const additionalFields = [
		{ key: "yearBuilt", label: "Year Built" },
		{ key: "historicalDesignation", label: "Historical Designation" },
		{ key: "architectName", label: "Architect" },
		{ key: "interiorDesigner", label: "Interior Designer" },
		{ key: "views", label: "Views" },
		{ key: "historicDetails", label: "Historic Details" },
		{ key: "hoaFees", label: "HOA Fees" },
		{ key: "taxes", label: "Property Taxes" },
	] as const;

	additionalFields.forEach(({ key, label }) => {
		const value = listingData[key];
		if (value !== undefined && value !== null) {
			contentParts.push(`${label}: ${value}`);
		}
	});

	// Add amenities
	if (listingData.amenities?.length) {
		contentParts.push(`Amenities: ${listingData.amenities.join(", ")}`);
	}

	return contentParts.join("\n");
}

export async function upsertVector(
	vectorKey: string,
	listingData: ListingVectorData
): Promise<void> {
	// Input validation
	if (!vectorKey?.trim()) {
		throw new Error("Vector key is required and cannot be empty");
	}

	if (!listingData) {
		throw new Error("Listing data is required");
	}

	// Validate required fields
	const requiredFields = [
		"id",
		"title",
		"propertyType",
		"price",
		"currency",
		"bedrooms",
		"bathrooms",
		"homeSize",
		"homeSizeUnit",
	];
	for (const field of requiredFields) {
		if (
			listingData[field as keyof ListingVectorData] === undefined ||
			listingData[field as keyof ListingVectorData] === null
		) {
			throw new Error(`Required field '${field}' is missing from listing data`);
		}
	}

	try {
		// Build content string efficiently
		const content = buildContentString(listingData);

		// Get embedding with error handling
		const embedding = await getEmbedding(content);

		if (!embedding || embedding.length === 0) {
			throw new Error("Failed to generate embedding: empty embedding returned");
		}

		// Convert metadata efficiently
		const metadata = convertToMetadata(listingData);

		// Upsert with retry logic
		const maxRetries = 3;
		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				await index.upsert({
					id: vectorKey,
					vector: embedding,
					metadata,
				});
				return; // Success, exit function
			} catch (error) {
				lastError = error as Error;

				// Don't retry on client errors (4xx), only on server errors or network issues
				if (
					error instanceof Error &&
					"response" in error &&
					(error as any).response?.status >= 400 &&
					(error as any).response?.status < 500
				) {
					throw error;
				}

				if (attempt < maxRetries) {
					// Exponential backoff: wait 1s, 2s, 4s
					const delayMs = Math.pow(2, attempt - 1) * 1000;
					await new Promise((resolve) => setTimeout(resolve, delayMs));
				}
			}
		}

		// If all retries failed, throw the last error
		throw new Error(
			`Failed to upsert vector after ${maxRetries} attempts: ${lastError?.message}`
		);
	} catch (error) {
		// Add context to the error
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";
		throw new Error(
			`Failed to upsert vector for key '${vectorKey}': ${errorMessage}`
		);
	}
}

export async function deleteVector(vectorKey: string) {
	await index.delete(vectorKey);
}

export async function queryVector(
	query: string,
	topK: number = 10,
	scoreThreshold: number = 0.7,
	filter?: string
): Promise<{ matchedIds: string[]; recommendedIds: string[] }> {
	const embedding = await getEmbedding(query);

	// Build query options - initially without filter to avoid serialization issues
	const queryOptions: any = {
		vector: embedding,
		topK,
	};

	if (filter) queryOptions.filter = filter;

	const result = await index.query(queryOptions);

	// Separate results based on similarity score to ensure matched results are truly relevant
	const matchedIds: string[] = [];
	const recommendedIds: string[] = [];

	result.forEach((m: any) => {
		// Only add to matchedIds if the similarity score is above threshold
		if (m.score && m.score >= scoreThreshold) {
			matchedIds.push(m.id);
		} else {
			// All other results go to recommendedIds
			recommendedIds.push(m.id);
		}
	});

	// matchedIds can be empty if no results meet the threshold
	return {
		matchedIds,
		recommendedIds,
	};
}

export interface PartialListingVectorData extends Partial<ListingVectorData> {
	id: string; // ID is always required for partial updates
}

// Helper function to merge partial data with existing metadata
async function mergeWithExistingMetadata(
	vectorKey: string,
	partialData: PartialListingVectorData
): Promise<Record<string, string>> {
	try {
		// Fetch existing vector data
		const existingVector = await index.fetch([vectorKey]);
		// Fix: Cast the response to the correct type to handle string keys
		const existingMetadata = (existingVector as any)[vectorKey]?.metadata || {};

		// Convert partial data to metadata format
		const partialMetadata = convertToMetadata(partialData as ListingVectorData);

		// Merge existing metadata with new partial metadata
		return {
			...existingMetadata,
			...partialMetadata,
		};
	} catch (error) {
		console.warn(
			`Could not fetch existing metadata for ${vectorKey}, using partial data only:`,
			error
		);
		return convertToMetadata(partialData as ListingVectorData);
	}
}

// Helper function to build partial content string
function buildPartialContentString(
	partialData: PartialListingVectorData,
	existingContent?: string
): string {
	const contentParts: string[] = [];

	// Add basic information with labels for consistency
	if (partialData.title !== undefined)
		contentParts.push(`Title: ${partialData.title}`);
	if (partialData.description !== undefined)
		contentParts.push(`Description: ${partialData.description}`);

	// Add structured information
	if (partialData.propertyType !== undefined)
		contentParts.push(`Property Type: ${partialData.propertyType}`);
	if (partialData.price !== undefined && partialData.currency !== undefined) {
		contentParts.push(`Price: ${partialData.price} ${partialData.currency}`);
	}

	// Build location string only if components exist
	const locationParts = [
		partialData.address,
		partialData.city,
		partialData.state,
		partialData.country,
		partialData.zipCode,
	].filter(Boolean);

	if (locationParts.length > 0) {
		contentParts.push(`Location: ${locationParts.join(", ")}`);
	}

	// Add optional fields only if they exist in the partial data
	if (partialData.neighborhood !== undefined)
		contentParts.push(`Neighborhood: ${partialData.neighborhood}`);
	if (partialData.schoolDistrict !== undefined)
		contentParts.push(`School District: ${partialData.schoolDistrict}`);

	// Add property details if they exist in partial data
	if (partialData.bedrooms !== undefined)
		contentParts.push(`Bedrooms: ${partialData.bedrooms}`);
	if (partialData.bathrooms !== undefined)
		contentParts.push(`Bathrooms: ${partialData.bathrooms}`);
	if (
		partialData.homeSize !== undefined &&
		partialData.homeSizeUnit !== undefined
	) {
		contentParts.push(
			`Home Size: ${partialData.homeSize} ${partialData.homeSizeUnit}`
		);
	}

	// Add optional property details
	if (
		partialData.lotSize !== undefined &&
		partialData.lotSizeUnit !== undefined
	) {
		contentParts.push(
			`Lot Size: ${partialData.lotSize} ${partialData.lotSizeUnit}`
		);
	}

	if (partialData.parkingType !== undefined) {
		const parkingInfo =
			partialData.parkingSpaces !== undefined
				? `${partialData.parkingType} (${partialData.parkingSpaces} spaces)`
				: partialData.parkingType;
		contentParts.push(`Parking: ${parkingInfo}`);
	}

	// Add additional details if they exist in partial data
	const additionalFields = [
		{ key: "yearBuilt", label: "Year Built" },
		{ key: "historicalDesignation", label: "Historical Designation" },
		{ key: "architectName", label: "Architect" },
		{ key: "interiorDesigner", label: "Interior Designer" },
		{ key: "views", label: "Views" },
		{ key: "historicDetails", label: "Historic Details" },
		{ key: "hoaFees", label: "HOA Fees" },
		{ key: "taxes", label: "Property Taxes" },
	] as const;

	additionalFields.forEach(({ key, label }) => {
		const value = partialData[key];
		if (value !== undefined && value !== null) {
			contentParts.push(`${label}: ${value}`);
		}
	});

	// Add amenities if they exist in partial data
	if (partialData.amenities?.length) {
		contentParts.push(`Amenities: ${partialData.amenities.join(", ")}`);
	}

	// If no partial content but we have existing content, return existing
	if (contentParts.length === 0 && existingContent) {
		return existingContent;
	}

	return contentParts.join("\n");
}

export async function upsertVectorPartial(
	vectorKey: string,
	partialData: PartialListingVectorData
): Promise<void> {
	// Input validation
	if (!vectorKey?.trim()) {
		throw new Error("Vector key is required and cannot be empty");
	}

	if (!partialData || !partialData.id) {
		throw new Error("Partial listing data with ID is required");
	}

	try {
		let embedding: number[];
		let metadata: Record<string, string>;

		// Check if we need to generate new embedding or if we can reuse existing
		const shouldRegenerateEmbedding = Boolean(
			partialData.title !== undefined ||
				partialData.description !== undefined ||
				partialData.propertyType !== undefined ||
				partialData.price !== undefined ||
				partialData.address !== undefined ||
				partialData.city !== undefined ||
				partialData.state !== undefined ||
				partialData.country !== undefined ||
				partialData.bedrooms !== undefined ||
				partialData.bathrooms !== undefined ||
				partialData.amenities !== undefined
		);

		if (shouldRegenerateEmbedding) {
			// Build partial content string
			const content = buildPartialContentString(partialData);

			// Get embedding with error handling
			embedding = await getEmbedding(content);

			if (!embedding || embedding.length === 0) {
				throw new Error(
					"Failed to generate embedding: empty embedding returned"
				);
			}

			// Merge with existing metadata
			metadata = await mergeWithExistingMetadata(vectorKey, partialData);
		} else {
			// Only metadata changes, try to preserve existing embedding
			try {
				const existingVector = await index.fetch([vectorKey]);
				// Fix: Cast the response to the correct type to handle string keys
				const existingData = (existingVector as any)[vectorKey];

				if (existingData?.values) {
					embedding = existingData.values;
					metadata = await mergeWithExistingMetadata(vectorKey, partialData);
				} else {
					throw new Error("No existing embedding found, regenerating");
				}
			} catch (error) {
				// Fallback to full regeneration if we can't get existing data
				console.warn(
					"Failed to fetch existing embedding, regenerating:",
					error
				);
				const content = buildPartialContentString(partialData);
				embedding = await getEmbedding(content);
				metadata = convertToMetadata(partialData as ListingVectorData);
			}
		}

		// Upsert with retry logic
		const maxRetries = 3;
		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				await index.upsert({
					id: vectorKey,
					vector: embedding,
					metadata,
				});
				return; // Success, exit function
			} catch (error) {
				lastError = error as Error;

				// Don't retry on client errors (4xx), only on server errors or network issues
				if (
					error instanceof Error &&
					"response" in error &&
					(error as any).response?.status >= 400 &&
					(error as any).response?.status < 500
				) {
					throw error;
				}

				if (attempt < maxRetries) {
					// Exponential backoff: wait 1s, 2s, 4s
					const delayMs = Math.pow(2, attempt - 1) * 1000;
					await new Promise((resolve) => setTimeout(resolve, delayMs));
				}
			}
		}

		// If all retries failed, throw the last error
		throw new Error(
			`Failed to upsert partial vector after ${maxRetries} attempts: ${lastError?.message}`
		);
	} catch (error) {
		// Add context to the error
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";
		throw new Error(
			`Failed to upsert partial vector for key '${vectorKey}': ${errorMessage}`
		);
	}
}
