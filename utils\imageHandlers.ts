/**
 * <PERSON><PERSON> click on images to show iframe with ZoomLook viewer
 * @param item Media item with ZLConfig property
 * @param setIframeUrl Function to set iframe URL in app state
 * @param setDialogShowIframe Function to show iframe in dialog
 * @param fromGallery Whether the image was clicked from gallery view
 */
export const handleImageClickForIframe = (
	item: any,
	setIframeUrl: (url: string) => void,
	setDialogShowIframe: (show: boolean) => void,
	fromGallery: boolean = false
) => {
	if (!item || !item.ZLConfig) {
		console.warn("Media item has no ZLConfig property", item);
		return;
	}

	// Set iframe URL based on ZLConfig property
	const iframeUrl = `https://zoomlook.io/player?configFile=${encodeURIComponent(`${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${item.ZLConfig}`)}&fullscreenMode=device`;

	// Important: set the iframe URL first, then show the iframe
	setIframeUrl(iframeUrl);

	// We need to know if we came from gallery to return to the correct view
	// We'll store this in sessionStorage since we don't want to modify the app store
	if (fromGallery) {
		sessionStorage.setItem("cameFromGallery", "true");
	}

	// Add a small delay to ensure URL is set before showing iframe
	setTimeout(() => {
		setDialogShowIframe(true);
	}, 50);
};
