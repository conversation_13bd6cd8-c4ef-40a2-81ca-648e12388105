"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { useUploadStore } from "@/stores/upload-store";
import { ListingFormValues, listingSchema } from "@/lib/schemas/listing-schema";
import { ListingData } from "@/types/listing";
import MainInformationSection from "./MainInformationSection";
import PropertyDetailsSection from "./PropertyDetailsSection";
import AmenitiesSection from "./AmenitiesSection";
import DescriptionSection from "./DescriptionSection";
import MediaUploadSection from "./MediaUploadSection";
import { ImageFile } from "@/components/ui/image-upload";
import { ReviewStatusType } from "@/db/schema/enums";
import { Checkbox } from "@/components/ui/checkbox";
import { convertInitialDataToFormValues } from "@/utils/listingDataUtils";

// Default values for the form
const defaultValues: Partial<ListingFormValues> = {
	propertyTitle: "",
	price: { currency: "USD", amount: 0 },
	streetAddress: "",
	hideStreetAddress: false,
	city: "",
	stateRegion: "",
	zipCode: "",
	country: "US",
	neighborhood: "",
	propertyType: "house",
	bedrooms: 0,
	bathrooms: 0,
	homeSize: { unit: "sqft", value: 0 },
	lotSize: { unit: "acres", value: 0 },
	parkingType: "None",
	parkingSpaces: 0,
	yearBuilt: new Date().getFullYear(),
	historicalDesignation: "None",
	amenities: [],
	description: "",
	media: [],
	visibility: "private",
};

// Exported hook for form methods
export function useFormMethods() {
	return useForm<ListingFormValues>({
		resolver: zodResolver(listingSchema),
		defaultValues,
		mode: "onChange",
	});
}

type AddNewListingFormProps = {
	formMethods: ReturnType<typeof useFormMethods>;
	onSubmit: (reviewStatus: ReviewStatusType) => Promise<void>;
	initialData?: ListingData | null;
	isEdit?: boolean;
};

const AddNewListingForm = ({
	formMethods,
	onSubmit,
	initialData,
	isEdit = false,
}: AddNewListingFormProps) => {
	const { isUploading, isSubmitting, setIsSubmitting } = useUploadStore();

	const [isTermsAccepted, setIsTermsAccepted] = useState(false);

	// Control form visibility during reset
	const [showForm, setShowForm] = useState(true);

	// Compute initial images for MediaUploadSection
	const initialImages: ImageFile[] =
		isEdit && initialData
			? initialData.media.map((media) => ({
					id: media.id!,
					preview: `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${media.preview}`,
					progress: 100,
					uploaded: true,
					key: media.key,
				})) || []
			: [];

	useEffect(() => {
		if (isEdit && initialData) {
			setShowForm(false);
			formMethods.reset(convertInitialDataToFormValues(initialData));
			setShowForm(true);
		} else if (!isEdit) {
			setShowForm(false);
			formMethods.reset({ ...defaultValues });
			setShowForm(true);
		}
	}, [isEdit, initialData, formMethods]);

	// Handle submit button click
	const handleSubmitClick = async (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setIsSubmitting(true);
		try {
			await onSubmit("pending");
		} catch (error) {
			console.error("Error during submission:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Form {...formMethods}>
			<div className="relative">
				{showForm && (
					<form className="px-6 py-4 scrollable max-h-[calc(90vh-120px)]">
						<div className="space-y-12">
							<MainInformationSection form={formMethods} />
							<div className="h-px my-8 bg-gray-200" />
							<PropertyDetailsSection form={formMethods} />
							<div className="h-px my-8 bg-gray-200" />
							<AmenitiesSection form={formMethods} />
							<div className="h-px my-8 bg-gray-200" />
							<DescriptionSection form={formMethods} />
							<div className="h-px my-8 bg-gray-200" />
							<MediaUploadSection
								form={formMethods}
								initialImages={initialImages}
							/>
						</div>
						{(!initialData ||
							initialData?.listingReviewRequest?.reviewStatus === "drafts") && (
							<>
								<div className="flex items-start max-w-4xl px-4 mx-auto mt-10 space-x-2 text-gray-500 sm:px-6 lg:px-8">
									<Checkbox
										id="terms"
										className="mt-2 peer"
										checked={isTermsAccepted}
										onCheckedChange={(checked) => {
											setIsTermsAccepted(checked as boolean);
										}}
									/>
									<label
										htmlFor="terms"
										className="ml-5 text-base font-medium leading-relaxed tracking-wide font-manrope peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
									>
										I certify that I am either the seller of this property or
										have been granted the exclusive right to market it pursuant
										to a valid marketing/listing agreement executed by the
										seller or the seller&apos;s authorized representative.
									</label>
								</div>
								<div className="pt-8 mt-12 border-t border-gray-200">
									<div className="flex justify-center">
										<Button
											variant="default"
											type="button"
											disabled={!isTermsAccepted || isUploading || isSubmitting}
											className="px-8 py-3 min-w-[200px]"
											onClick={handleSubmitClick}
										>
											{isSubmitting
												? "Submitting..."
												: isUploading
													? "Uploading..."
													: "Submit listing for review"}
										</Button>
									</div>
								</div>
							</>
						)}
					</form>
				)}
			</div>
		</Form>
	);
};

export default AddNewListingForm;
