"use client";
import { But<PERSON> } from "./button";
import { Edit, CircleAlert } from "lucide-react";
import type { MouseEvent } from "react";

interface EditButtonProps {
	variant?:
		| "default"
		| "destructive"
		| "outline"
		| "secondary"
		| "ghost"
		| "link";
	onEdit: () => void;
	disabled?: boolean;
	className?: string;
	size?: "default" | "sm" | "lg" | "icon";
	showAlert?: boolean;
	alertText?: string;
}

const EditButton = ({
	variant = "outline",
	onEdit,
	disabled = false,
	className = "",
	size = "default",
	showAlert = false,
	alertText = "Needs review",
}: EditButtonProps) => {
	const handleEditClick = (e: MouseEvent) => {
		// Prevent event bubbling to parent
		e.preventDefault();
		e.stopPropagation();

		if (!disabled) {
			onEdit();
		}
	};

	return (
		<Button
			variant={variant}
			size={size}
			className={`flex items-center gap-2 text-base font-normal font-manrope ${className}`}
			onClick={handleEditClick}
			disabled={disabled}
		>
			{showAlert ? (
				<CircleAlert className="w-4 h-4 stroke-current fill-transparent" />
			) : (
				<Edit className="w-4 h-4" />
			)}
			{showAlert ? alertText : "Edit"}
		</Button>
	);
};

export default EditButton;
