import { CardHeader } from "../../ui/card";
import { Badge } from "../../ui/badge";
import { Circle } from "lucide-react";
import { ListingData } from "@/types/listing";
import BaseListingCard from "./BaseListingCard";
import IdDisplay from "../../ui/IdDisplay";

interface PendingListingCardProps {
	item: ListingData;
}

const PendingListingCard = ({ item }: PendingListingCardProps) => {

	return (
		<BaseListingCard item={item}>
			<CardHeader className="pt-2 pb-4">
				<div className="flex flex-row items-center justify-between">
					<IdDisplay
						id={item.id}
						className="text-lg font-normal text-neutral-500 font-manrope"
					/>
					<Badge
						variant={"outline"}
						className="flex items-center gap-2 text-base font-normal font-manrope"
					>
						<Circle className="w-3 h-3 fill-current" />
						{item.listingReviewRequest?.reviewerType === "admin"
							? "Submitted"
							: "Review"}
					</Badge>
				</div>
			</CardHeader>
		</BaseListingCard>
	);
};

export default PendingListingCard;
