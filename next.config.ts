import type { NextConfig } from "next";

const nextConfig: NextConfig = {
	env: {
		// Ensure DATABASE_URL is available in the runtime
		DATABASE_URL: process.env.DATABASE_URL,
	},
	sassOptions: {
		includePaths: ["./styles"],
	},
	images: {
		remotePatterns: [
			{
				protocol: "https",
				hostname: "zoomlook.media",
				port: "",
				pathname: "**",
			},
			{
				protocol: "https",
				hostname: "d38e3d0xptm34e.cloudfront.net",
				port: "",
				pathname: "**",
			},
			{
				protocol: "https",
				hostname: "cloud.maison.global",
				port: "",
				pathname: "**",
			},
			{
				protocol: "https",
				hostname: "placehold.co",
				port: "",
				pathname: "**",
			},
			{
				protocol: "https",
				hostname: "image.tmdb.org",
				port: "",
				pathname: "**",
			},
			{
				protocol: "https",
				hostname: "img.clerk.com",
				port: "",
				pathname: "**",
			},
			{
				protocol: "https",
				hostname: "getstream.io",
				port: "",
				pathname: "**",
			},
		],
	},
};

export default nextConfig;
