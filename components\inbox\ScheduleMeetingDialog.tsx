import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import MeetingForm, { MeetingFormData } from "./MeetingForm";

interface User {
	id: string;
	name: string;
}

interface ScheduleMeetingDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSchedule: (data: MeetingFormData) => void;
	availableUsers: User[];
}

const ScheduleMeetingDialog = ({
	open,
	onOpenChange,
	onSchedule,
	availableUsers,
}: ScheduleMeetingDialogProps) => {
	const handleCancel = () => {
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="flex flex-col items-center justify-center max-w-md p-8 mx-auto bg-white shadow-2xl rounded-2xl">
				<DialogTitle className="mb-6 text-2xl font-normal leading-8 text-center font-lora">
					Schedule Meeting
				</DialogTitle>
				<MeetingForm
					onSchedule={onSchedule}
					onCancel={handleCancel}
					availableUsers={availableUsers}
				/>
			</DialogContent>
		</Dialog>
	);
};

export default ScheduleMeetingDialog;
