import { useState, useEffect, useCallback, useMemo } from "react";
import useS<PERSON> from "swr";
import { fetcher } from "@/lib/utils";

/**
 * Custom hook for managing connections/linked accounts
 * Handles fetching, searching, pagination and unlinking of accounts
 */
export function useConnections({
	userId,
	initialConnections = [],
	initialPageSize = 10,
}: {
	userId: string;
	initialConnections?: any[];
	initialPageSize?: number;
}) {
	// State for search and pagination
	const [searchTerm, setSearchTerm] = useState("");
	const [inputValue, setInputValue] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = initialPageSize;

	// Fetch connected accounts data
	const { data, error, isLoading, mutate } = useSWR(
		`/api/connections?userId=${userId}&search=${encodeURIComponent(
			searchTerm || ""
		)}&page=${currentPage}&pageSize=${pageSize}`,
		fetcher,
		{
			revalidateOnFocus: false,
			dedupingInterval: 60000, // Cache for 1 minute
			refreshInterval: 600000, // Refresh every 10 minutes
			revalidateOnReconnect: true,
			fallbackData: {
				connections: initialConnections,
				pagination: {
					total: initialConnections?.length || 0,
					pageSize,
					currentPage: 1,
					totalPages:
						Math.ceil((initialConnections?.length || 0) / pageSize) || 1,
				},
			},
		}
	);

	const connections: any[] = data?.connections || initialConnections || [];

	const pagination = useMemo(
		() =>
			data?.pagination || {
				total: connections.length,
				pageSize,
				currentPage: 1,
				totalPages: Math.ceil(connections.length / pageSize),
			},
		[data?.pagination, connections.length, pageSize]
	);

	// Reset to first page when search changes
	useEffect(() => {
		setCurrentPage(1);
	}, [searchTerm]);

	// Debounce search input
	useEffect(() => {
		const timer = setTimeout(() => {
			setSearchTerm(inputValue);
		}, 300);

		return () => clearTimeout(timer);
	}, [inputValue]);

	// Page change handler
	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	// Handle unlink button click
	const handleUnlink = async (connectionId: number) => {
		try {
			const response = await fetch(`/api/connections/${connectionId}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				throw new Error("Failed to unlink account");
			}

			// Refresh data after unlinking
			mutate();
			return { success: true };
		} catch (error) {
			console.error("Failed to unlink account:", error);
			return { success: false, error };
		}
	};

	// Generate pagination numbers
	const getPageNumbers = useCallback(() => {
		const { currentPage, totalPages } = pagination;
		const pageNumbers = [];

		// Always include first page
		pageNumbers.push(1);

		// Add pages around current page
		for (
			let i = Math.max(2, currentPage - 1);
			i <= Math.min(totalPages - 1, currentPage + 1);
			i++
		) {
			pageNumbers.push(i);
		}

		// Always include last page if more than one page
		if (totalPages > 1) {
			pageNumbers.push(totalPages);
		}

		// Make sure array is unique and sorted
		return [...new Set(pageNumbers)].sort((a, b) => a - b);
	}, [pagination]);

	return {
		connections,
		isLoading,
		error,
		pagination,
		inputValue,
		setInputValue,
		handlePageChange,
		handleUnlink,
		getPageNumbers,
		mutate,
	};
}
