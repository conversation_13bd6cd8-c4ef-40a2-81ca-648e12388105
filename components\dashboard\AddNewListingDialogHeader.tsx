"use client";
import Logo from "@/components/ui/Logo";
import { Button } from "@/components/ui/button";
import { MdChevronLeft } from "react-icons/md";
import { useUploadStore } from "@/stores/upload-store";
import { ReviewStatusType } from "@/db/schema/enums";
import { ListingData } from "@/types/listing";

interface AddNewListingDialogHeaderProps {
	closeDialog: () => void;
	onSubmit: (reviewStatus: ReviewStatusType) => Promise<void>; // Make onSubmit async
	initialData?: ListingData | null;
}

const AddNewListingDialogHeader = ({
	closeDialog,
	onSubmit,
	initialData,
}: AddNewListingDialogHeaderProps) => {
	// Get both uploading and submitting states from the store
	const { isUploading, isSubmitting, setIsSubmitting } = useUploadStore();

	const handleSubmitClick = async (e: React.MouseEvent) => {
		// Prevent any default behavior
		e.preventDefault();
		e.stopPropagation();

		// Set submitting state to true
		setIsSubmitting(true);

		try {
			await onSubmit(
				initialData?.listingReviewRequest?.reviewStatus || "drafts"
			);
		} catch {
			// Handle submission error silently
			setIsSubmitting(false);
		} finally {
			// Reset submitting state
			setIsSubmitting(false);
		}
	};

	// Calculate if button should be disabled
	const isDisabled = isUploading || isSubmitting;

	return (
		<div className="flex flex-row items-center justify-between w-full gap-4">
			<Button
				variant={"secondary"}
				className="flex flex-row items-center justify-center"
				onClick={closeDialog}
			>
				<MdChevronLeft />
				<span className="text-sm font-inter">Back</span>
			</Button>

			<Logo className="w-auto h-12" />

			<div className="flex items-center gap-2">
				{!initialData ||
				initialData?.listingReviewRequest?.reviewStatus === "drafts" ? (
					<Button
						variant={"outline"}
						className="flex justify-end cursor-pointer"
						onClick={handleSubmitClick}
						type="button"
						disabled={isDisabled}
					>
						{isSubmitting
							? "Saving..."
							: isUploading
								? "Uploading..."
								: "Save as Draft"}
					</Button>
				) : (
					<Button
						variant={"outline"}
						className="flex justify-end cursor-pointer"
						onClick={handleSubmitClick}
						type="button"
						disabled={isDisabled}
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				)}
			</div>
		</div>
	);
};

export default AddNewListingDialogHeader;
