import { <PERSON><PERSON>eader } from "../../ui/card";
import ArchiveButton from "../../ui/ArchiveButton";
import EditButton from "../../ui/EditButton";
import { Eye, Heart } from "lucide-react";
import { formatSimplePrice } from "@/lib/utils";
import { ListingData } from "@/types/listing";
import BaseListingCard from "./BaseListingCard";
import { useListingStatusActions } from "@/hooks/useListingStatusActions";
import { KeyedMutator } from "swr";
import IdDisplay from "../../ui/IdDisplay";

interface ManagementApprovedListingCardProps {
	item: ListingData;
	onEdit?: (listingId: string) => void;
	mutate?: KeyedMutator<any>;
}

const ManagementApprovedListingCard = ({
	item,
	onEdit,
	mutate,
}: ManagementApprovedListingCardProps) => {
	const { archiveListing } = useListingStatusActions(mutate);

	const handleEditClick = () => {
		if (onEdit) {
			onEdit(item.id);
		}
	};

	const handleArchive = async () => {
		if (item.listingReviewRequest?.id) {
			await archiveListing(item.listingReviewRequest.id);
		}
	};

	return (
		<BaseListingCard item={item}>
			<CardHeader className="py-2">
				{/* First row: ID, viewCount, favoriteCount */}
				<div className="flex flex-row items-center justify-between">
					<IdDisplay
						id={item.id}
						className="text-lg font-bold text-slate-900 font-manrope"
					/>
					<div className="flex items-center gap-4">
						{/* View Count */}
						<div className="flex items-center gap-1 text-lg font-normal text-neutral-500 font-manrope">
							<Eye className="w-4 h-4" />
							<span>{item.viewCount || 0}</span>
						</div>
						{/* Favorite Count */}
						<div className="flex items-center gap-1 text-lg font-normal text-neutral-500 font-manrope">
							<Heart className="w-4 h-4" />
							<span>{item.favoriteCount || 0}</span>
						</div>
					</div>
				</div>

				{/* Second row: Price */}
				<div className="mt-2">
					<span className="text-lg font-normal text-slate-900 font-manrope">
						{formatSimplePrice(Number(item.price))}
					</span>
				</div>

				{/* Third row: Address */}
				{item.location && (
					<div className="mt-1">
						<span className="text-base font-normal text-gray-700 font-manrope">
							{item.hideStreetAddress
								? "Address hidden"
								: item.location.address}
						</span>
					</div>
				)}

				{/* Fourth row: Country */}
				{item.location && (
					<div>
						<span className="text-base font-normal text-gray-700 font-manrope">
							{`${item.location.city}, ${item.location.state}`}
						</span>
					</div>
				)}

				{/* Bottom row: Action buttons */}
				<div className="grid grid-cols-2 gap-4 mt-4">
					<EditButton
						variant="default"
						onEdit={handleEditClick}
						className="justify-center"
					/>
					<ArchiveButton variant="outline" onArchive={handleArchive} />
				</div>
			</CardHeader>
		</BaseListingCard>
	);
};

export default ManagementApprovedListingCard;
