import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { ListingData } from "@/types/listing";
import { ReviewStatusType } from "@/db/schema/enums";
import AddNewListingDialogHeader from "./AddNewListingDialogHeader";
import AddNewListingForm from "./listing-form/AddNewListingForm";
import { UseFormReturn } from "react-hook-form";
import { ListingFormValues } from "@/lib/schemas/listing-schema";

interface EditListingDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	editingListing: ListingData | null;
	onSubmit: (reviewStatus: ReviewStatusType) => Promise<void>;
	onClose: () => void;
	formMethods: UseFormReturn<ListingFormValues>;
}

const EditListingDialog = ({
	open,
	onOpenChange,
	editingListing,
	onSubmit,
	onClose,
	formMethods,
}: EditListingDialogProps) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				closeButton={false}
				className="sm:max-w-2xl md:max-w-4xl lg:max-w-7xl max-h-[90dvh] overflow-hidden flex flex-col"
			>
				<DialogTitle className="sr-only">Edit Listing</DialogTitle>
				<DialogDescription className="sr-only">
					Please edit the form below to update the property listing
				</DialogDescription>
				<DialogHeader>
					<AddNewListingDialogHeader
						closeDialog={onClose}
						onSubmit={onSubmit}
						initialData={editingListing}
					/>
				</DialogHeader>
				<AddNewListingForm
					formMethods={formMethods}
					onSubmit={onSubmit}
					initialData={editingListing}
					isEdit
				/>
			</DialogContent>
		</Dialog>
	);
};

export default EditListingDialog;
