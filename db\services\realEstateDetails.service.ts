"use server";
import { eq, gte, and } from "drizzle-orm";
import { db } from "../index";
import { realEstateDetails } from "../schema/realEstateDetails";
import { PropertyTypeType } from "../schema";

export type RealEstateDetailsType = typeof realEstateDetails.$inferInsert;
export type RealEstateDetailsSelectType = typeof realEstateDetails.$inferSelect;

/**
 * Gets real estate details by listing ID
 */
export const getRealEstateDetailsByListingId = async (
	listingId: string
): Promise<RealEstateDetailsSelectType | null> => {
	try {
		const result = await db
			.select()
			.from(realEstateDetails)
			.where(eq(realEstateDetails.listingId, listingId));

		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting real estate details:", error);
		return null;
	}
};

/**
 * Creates real estate details for a listing
 */
export const createRealEstateDetails = async (
	detailsData: RealEstateDetailsType
): Promise<RealEstateDetailsSelectType | null> => {
	try {
		const [newDetails] = await db
			.insert(realEstateDetails)
			.values(detailsData)
			.returning();

		return newDetails;
	} catch (error) {
		console.error("Error creating real estate details:", error);
		return null;
	}
};

/**
 * Updates real estate details for a listing
 */
export const updateRealEstateDetails = async (
	listingId: string,
	updates: Partial<Omit<RealEstateDetailsType, "id" | "listingId">>
): Promise<boolean> => {
	try {
		await db
			.update(realEstateDetails)
			.set({
				...updates,
				updatedAt: new Date(),
			})
			.where(eq(realEstateDetails.listingId, listingId));

		return true;
	} catch (error) {
		console.error("Error updating real estate details:", error);
		return false;
	}
};

/**
 * Deletes real estate details for a listing
 */
export const deleteRealEstateDetails = async (
	listingId: string
): Promise<boolean> => {
	try {
		await db
			.delete(realEstateDetails)
			.where(eq(realEstateDetails.listingId, listingId));

		return true;
	} catch (error) {
		console.error("Error deleting real estate details:", error);
		return false;
	}
};

/**
 * Creates or updates real estate details for a listing with partial data support
 */
export const upsertRealEstateDetails = async (
	listingId: string,
	data: Partial<Omit<RealEstateDetailsType, "listingId">>
): Promise<RealEstateDetailsSelectType | null> => {
	try {
		// Check if details already exist
		const existingDetails = await getRealEstateDetailsByListingId(listingId);

		if (existingDetails) {
			// Only update if there are fields to update
			if (Object.keys(data).length > 0) {
				await updateRealEstateDetails(listingId, data);
			}
			return await getRealEstateDetailsByListingId(listingId);
		} else {
			// For new real estate details, we need at least some required fields
			// Create new record only if we have essential data
			if (Object.keys(data).length > 0) {
				return await createRealEstateDetails({
					...data,
					listingId,
				} as RealEstateDetailsType);
			}
			return null;
		}
	} catch (error) {
		console.error("Error upserting real estate details:", error);
		return null;
	}
};

/**
 * Gets listing IDs by real estate details filters
 * Returns listing IDs that match the specified real estate criteria
 */
export const getListingIdsByRealEstateFilters = async (
	filters: {
		bedrooms?: number;
		bathrooms?: number;
		propertyType?: PropertyTypeType;
	} = {}
) => {
	const { bedrooms, bathrooms, propertyType } = filters;

	// If no filters provided, return empty array (no filtering needed)
	if (!bedrooms && !bathrooms && !propertyType) {
		return [];
	}

	// Build conditions array for real estate details filtering
	const conditions = [];

	// Add bedrooms filter if provided
	if (bedrooms) {
		conditions.push(gte(realEstateDetails.bedrooms, String(bedrooms)));
	}

	// Add bathrooms filter if provided
	if (bathrooms) {
		conditions.push(gte(realEstateDetails.bathrooms, String(bathrooms)));
	}

	// Add property type filter if provided
	if (propertyType) {
		conditions.push(eq(realEstateDetails.propertyType, propertyType));
	}

	// Combine all conditions with AND
	const whereClause =
		conditions.length === 1 ? conditions[0] : and(...conditions);

	// Query real estate details with filters and return listing IDs
	const filteredDetails = await db.query.realEstateDetails.findMany({
		where: whereClause,
		columns: {
			listingId: true,
		},
	});

	return filteredDetails.map((detail) => detail.listingId);
};
