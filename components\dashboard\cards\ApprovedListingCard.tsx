import { <PERSON><PERSON>eader } from "../../ui/card";
import { Eye, Heart } from "lucide-react";
import { ListingData } from "@/types/listing";
import BaseListingCard from "./BaseListingCard";
import PromoteButton from "../../ui/PromoteButton";
import EditButton from "../../ui/EditButton";
import IdDisplay from "../../ui/IdDisplay";

interface ApprovedListingCardProps {
	item: ListingData;
	onEdit?: (listingId: string) => void;
}

const ApprovedListingCard = ({ item, onEdit }: ApprovedListingCardProps) => {
	const handleEditClick = () => {
		if (onEdit) {
			onEdit(item.id);
		}
	};

	const handlePromoteClick = async () => {
		console.log("Promote button clicked for listing ID:", item.id);
	};

	return (
		<BaseListingCard item={item}>
			<CardHeader className="pt-2 pb-4">
				<div className="flex flex-row items-center justify-between gap-4">
					<IdDisplay
						id={item.id}
						className="text-lg font-normal text-neutral-500 font-manrope"
					/>

					<div className="flex items-center gap-4">
						{/* View Count */}
						<div className="flex items-center gap-1 text-lg font-normal text-slate-900 font-manrope">
							<Eye className="w-4 h-4" />
							<span>{item.viewCount || 0}</span>
						</div>

						{/* Favorite Count */}
						<div className="flex items-center gap-1 text-lg font-normal text-slate-900 font-manrope">
							<Heart className="w-4 h-4" />
							<span>{item.favoriteCount || 0}</span>
						</div>
					</div>

					<div className="flex items-center gap-4">
						{/* Promote Button */}
						<PromoteButton
							variant="default"
							size="sm"
							onPromote={handlePromoteClick}
						/>

						{/* Edit Button */}
						<EditButton onEdit={handleEditClick} />
					</div>
				</div>
			</CardHeader>
		</BaseListingCard>
	);
};

export default ApprovedListingCard;
