import { Skeleton } from "@/components/ui/skeleton";

const CustomerHeaderSkeleton = () => {
	return (
		<div className="flex flex-row w-full bg-[#EFEFF1] rounded-t-[11px] shadow-blue-100">
			{/* Image skeleton - matches Image component dimensions from PropertyInfo */}
			<Skeleton className="w-[262px] h-[183px] rounded-tl-[11px]" />

			{/* Content skeleton - matches the flex structure */}
			<div className="flex flex-col w-full gap-4 p-5">
				<div className="flex flex-col gap-1">
					<div className="flex flex-row justify-between">
						{/* Title skeleton - text-base font-normal leading-6 */}
						<Skeleton className="w-48 h-6" />
						{/* ID skeleton - text-lg leading-7 */}
						<Skeleton className="w-20 h-7" />
					</div>
					{/* Location skeleton - text-2xl leading-7 */}
					<Skeleton className="w-40 h-7" />
					{/* Price skeleton - text-xl leading-7 */}
					<Skeleton className="w-32 h-7" />
				</div>

				<div className="flex flex-row justify-between">
					{/* Property details skeleton - matches the gap-4 structure */}
					<div className="flex flex-row gap-4 leading-6">
						<Skeleton className="w-20 h-6" />
						<Skeleton className="h-6 w-22" />
						<Skeleton className="w-16 h-6" />
					</div>
					{/* Calendar icon skeleton - w-8 h-8 */}
					<Skeleton className="w-8 h-8 rounded" />
				</div>
			</div>
		</div>
	);
};

export default CustomerHeaderSkeleton;
