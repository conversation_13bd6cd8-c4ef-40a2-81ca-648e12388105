const Hero = () => {
	// Get current date and calculate week of month
	const getCurrentWeekOfMonth = () => {
		const now = new Date();
		const year = now.getFullYear();
		const month = now.toLocaleString("en-US", { month: "long" });

		// Get the first day of the month
		const firstDay = new Date(year, now.getMonth(), 1);
		const currentDate = now.getDate();

		// Calculate which week of the month we're in
		const weekNumber = Math.ceil((currentDate + firstDay.getDay()) / 7);

		// Convert week number to ordinal
		const getOrdinal = (num: number) => {
			const suffixes = ["th", "st", "nd", "rd"];
			const v = num % 100;
			return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);
		};

		return `${getOrdinal(weekNumber)} Week of ${month} ${year}`;
	};

	return (
		<section
			id="hero-section"
			className="flex flex-col items-center w-3/4 max-w-3xl gap-4 mx-auto mt-8 mb-10 font-lora"
		>
			<header id="hero-header" className="flex flex-col items-center gap-4">
				<h1
					id="hero-title"
					className="text-6xl font-medium text-center capitalize text-slate-900"
				>
					Curated selection
				</h1>
				<h2
					id="hero-subtitle"
					className="text-lg font-normal text-center text-neutral-500 font-manrope"
				>
					{getCurrentWeekOfMonth()}
				</h2>
			</header>
			<div id="hero-content">
				<p
					id="hero-description"
					className="text-xl font-normal text-center text-slate-900"
				>
					Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
					eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
					minim veniam, quis nostrud exercitation ullamco laboris nisi ut
					aliquip ex ea commodo consequat.
				</p>
			</div>
		</section>
	);
};

export default Hero;
