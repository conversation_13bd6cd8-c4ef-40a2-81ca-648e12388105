import { serial, timestamp, text } from "drizzle-orm/pg-core";
import { customAlphabet, nanoid } from "nanoid";

export const timestamps = {
	updatedAt: timestamp("updated_at"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
};

export const idField = () => serial("id").primaryKey();

const simplifyNanoid = customAlphabet(
	"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
	8
);
// Add UUID primary key field helper function
export const simplifyUuidField = () =>
	text("id")
		.primaryKey()
		.$defaultFn(() => simplifyNanoid());

// Add UUID field helper function
export const uuidField = () =>
	text("id")
		.primaryKey()
		.$defaultFn(() => nanoid());
