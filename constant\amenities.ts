import {
  MdAcUnit,
  MdLocalFireDepartment,
  MdPool,
  MdWork,
  MdWifi,
  MdSecurity,
  MdHome,
  MdFitnessCenter,
  MdLocalLibrary,
  MdElevator,
  MdOutlineViewQuilt,
  MdOutlineCropFree,
  MdOutlineWbSunny,
  MdOutlineWaves,
  MdOutlineLandscape,
  MdOutlineBalcony,
  MdOutlineSportsBasketball,
  MdOutlineWindow,
  MdOutlineVerticalAlignTop,
  MdOutlineWhatshot,
  MdOutlineShower,
  MdOutlineBathtub,
  MdOutlineWc,
  MdOutlineRestaurantMenu,
  MdOutlineKitchen,
  MdOutlineCountertops,
  MdOutlineKebabDining,
  MdOutlineChair,
  MdOutlineGames,
  MdOutlineTheaters,
  MdOutlineLocalBar,
  MdOutlineGolfCourse,
  MdOutlineYard,
  MdOutlineLock,
  MdOutlineDirectionsCar,
  MdOutlineLocalDining,
  MdOutlineSailing,
  MdOutlineFlightTakeoff,
  MdOutlineSecurity as MdOutlineSecurityIcon,
  MdOutlineHomeWork,
  MdOutlineCleaningServices,
  MdOutlineRestaurant,
  MdOutlineRectangle, // Replace MdOutlineMirror
} from "react-icons/md";
import {
  HiOutlineHome,
  HiOutlineMicrophone,
  HiOutlineCamera,
  HiOutlineGlobeAlt,
  HiOutlineSpeakerWave,
  HiOutlineSparkles,
  HiOutlineMoon,
} from "react-icons/hi2";
import {
  TbAirConditioning,
  TbFlame,
  TbTemperature,
  TbSolarPanel,
  TbBowling,
  TbHorse,
  TbBottle,
  TbCoffee,
  TbCar,
} from "react-icons/tb";
import {
	FaUtensils,
	FaWineBottle,
	FaHotTub,
	FaSwimmingPool,
	FaBiking,
} from "react-icons/fa";

// Import custom SVG icons
import {
	AirConditioningIcon,
	CarpetFreeIcon,
	ChefsKitchenIcon,
	FireplaceIcon,
	GuestHouseIcon,
	HomeOfficeIcon,
	MainLevelBedroomIcon,
	NaturalLightIcon,
	PoolIcon,
	PrivateElevatorIcon,
	PrivateGymIcon,
	ScenicViewIcon,
	TerraceIcon,
	WineCellarIcon,
} from "@/components/ui/custom-icons";

const amenities = [
	{
		name: "carpetFree",
		label: "Carpet Free",
		icon: CarpetFreeIcon,
	},
	{
		name: "centralAir",
		label: "Central Air",
		icon: AirConditioningIcon,
	},
	{
		name: "fireplace",
		label: "Fireplace",
		icon: FireplaceIcon,
	},
	{
		name: "gourmetKitchen",
		label: "Gourmet Kitchen",
		icon: ChefsKitchenIcon,
	},
	{
		name: "guestQuarters",
		label: "Guest Quarters",
		icon: GuestHouseIcon,
	},
	{
		name: "homeOffice",
		label: "Home Office",
		icon: HomeOfficeIcon,
	},
	{
		name: "naturalLight",
		label: "Natural Light",
		icon: NaturalLightIcon,
	},
	{
		name: "pool",
		label: "Pool",
		icon: PoolIcon,
	},
	{
		name: "primaryOnMain",
		label: "Primary on Main",
		icon: MainLevelBedroomIcon,
	},
	{
		name: "privateElevator",
		label: "Private Elevator",
		icon: PrivateElevatorIcon,
	},
	{
		name: "privateGym",
		label: "Private Gym",
		icon: PrivateGymIcon,
	},
	{
		name: "scenicView",
		label: "Scenic View",
		icon: ScenicViewIcon,
	},
	{
		name: "tennisSportCourt",
		label: "Tennis/Sport Court",
		icon: MdOutlineSportsBasketball,
	},
	{
		name: "terrace",
		label: "Terrace",
		icon: TerraceIcon,
	},
	{
		name: "waterView",
		label: "Water View",
		icon: MdOutlineWaves,
	},
	{
		name: "wineCellar",
		label: "Wine Cellar",
		icon: WineCellarIcon,
	},
];

// Features groups based on the UI image
const features = [
	{
		id: "interior-features",
		title: "Interior features",
		items: [
			{ name: "carpetFree", label: "Carpet-Free", icon: MdOutlineCropFree },
			{
				name: "customLighting",
				label: "Custom Lighting",
				icon: HiOutlineSparkles,
			},
			{
				name: "floorToCeilingWindows",
				label: "Floor-to-Ceiling Windows",
				icon: MdOutlineWindow,
			},
			{ name: "gasFireplace", label: "Gas Fireplace", icon: TbFlame },
			{
				name: "hardwoodFloors",
				label: "Hardwood Floors",
				icon: MdOutlineChair,
			},
			{ name: "heatedFloors", label: "Heated Floors", icon: MdOutlineWhatshot },
			{
				name: "highCeilings",
				label: "High Ceilings",
				icon: MdOutlineVerticalAlignTop,
			},
			{ name: "homeOffice", label: "Home Office", icon: MdWork },
			{ name: "indoorPool", label: "Indoor Pool", icon: FaSwimmingPool },
			{
				name: "integratedSoundSystem",
				label: "Integrated Sound System",
				icon: HiOutlineSpeakerWave,
			},
			{ name: "library", label: "Library", icon: MdLocalLibrary },
			{ name: "naturalLight", label: "Natural Light", icon: MdOutlineWbSunny },
			{
				name: "openFloorPlan",
				label: "Open Floor Plan",
				icon: MdOutlineViewQuilt,
			},
			{ name: "privateElevator", label: "Private Elevator", icon: MdElevator },
			{ name: "safeRoom", label: "Safe Room / Panic Room", icon: MdSecurity },
			{
				name: "stoneFloors",
				label: "Stone Floors",
				icon: MdOutlineCountertops,
			},
			{
				name: "wellnessFitnessFacilities",
				label: "Wellness/Fitness Facilities",
				icon: FaBiking,
			},
			{ name: "wineCellar", label: "Wine Cellar", icon: TbBottle },
			{
				name: "woodBurningFireplace",
				label: "Wood burning Fireplace",
				icon: MdLocalFireDepartment,
			},
		],
	},
	{
		id: "kitchen-features",
		title: "Kitchen features",
		items: [
			{
				name: "bertazzoniAppliances",
				label: "Bertazzoni Appliances",
				icon: MdOutlineKitchen,
			},
			{
				name: "breakfastNook",
				label: "Breakfast Nook",
				icon: MdOutlineKebabDining,
			},
			{
				name: "builtInCoffeeStation",
				label: "Built-in Coffee Station",
				icon: TbCoffee,
			},
			{
				name: "customCabinetry",
				label: "Custom Cabinetry",
				icon: MdOutlineKitchen,
			},
			{
				name: "expansiveKitchenIsland",
				label: "Expansive Kitchen Island",
				icon: MdOutlineRestaurantMenu,
			},
			{
				name: "gaggenauAppliances",
				label: "Gaggenau Appliances",
				icon: MdOutlineKitchen,
			},
			{
				name: "gourmetChefsKitchen",
				label: "Gourmet Chef's Kitchen",
				icon: FaUtensils,
			},
			{
				name: "graniteCountertops",
				label: "Granite Countertops",
				icon: MdOutlineCountertops,
			},
			{
				name: "laCornueAppliances",
				label: "La Cornue Appliances",
				icon: FaUtensils,
			},
			{
				name: "marbleCountertops",
				label: "Marble Countertops",
				icon: MdOutlineCountertops,
			},
			{
				name: "mieleAppliances",
				label: "Miele Appliances",
				icon: MdOutlineKitchen,
			},
			{
				name: "quartzCountertops",
				label: "Quartz Countertops",
				icon: MdOutlineCountertops,
			},
			{
				name: "soapstoneCountertops",
				label: "Soapstone Countertops",
				icon: MdOutlineCountertops,
			},
			{
				name: "subZeroAppliances",
				label: "Sub-Zero Appliances",
				icon: MdAcUnit,
			},
			{
				name: "thermadorAppliances",
				label: "Thermador Appliances",
				icon: TbTemperature,
			},
			{
				name: "vikingAppliances",
				label: "Viking Appliances",
				icon: MdOutlineKitchen,
			},
			{
				name: "wineFridge",
				label: "Wine Fridge / Wine Storage",
				icon: FaWineBottle,
			},
			{
				name: "wolfAppliances",
				label: "Wolf Appliances",
				icon: MdOutlineKitchen,
			},
		],
	},
	{
		id: "bathroom-features",
		title: "Bathroom features",
		items: [
			{
				name: "bathroomCustomLighting",
				label: "Custom Lighting",
				icon: HiOutlineSparkles,
			},
			{
				name: "bathroomHeatedFloors",
				label: "Heated Floors",
				icon: MdOutlineWhatshot,
			},
			{ name: "bidet", label: "Bidet", icon: MdOutlineWc },
			{ name: "dualVanities", label: "Dual Vanities", icon: MdOutlineWc },
			{
				name: "extendedHeightVanities",
				label: "Extended Height Vanities",
				icon: MdOutlineVerticalAlignTop,
			},
			{
				name: "rainfallShower",
				label: "Rainfall Shower",
				icon: MdOutlineShower,
			},
			{
				name: "smartMirrors",
				label: "Smart Mirrors",
				icon: MdOutlineRectangle,
			},
			{ name: "soakingTub", label: "Soaking Tub", icon: MdOutlineBathtub },
			{ name: "steamRoomSauna", label: "Steam Room / Sauna", icon: FaHotTub },
			{ name: "steamShower", label: "Steam Shower", icon: MdOutlineShower },
		],
	},
	{
		id: "bedroom-features",
		title: "Bedroom features",
		items: [
			{
				name: "advancedClimateControl",
				label: "Advanced Climate Control",
				icon: TbTemperature,
			},
			{
				name: "bedroomCustomLighting",
				label: "Custom Lighting",
				icon: HiOutlineSparkles,
			},
			{
				name: "bedroomFireplace",
				label: "Fireplace",
				icon: MdLocalFireDepartment,
			},
			{
				name: "customClosetSystem",
				label: "Custom Closet System",
				icon: MdOutlineHomeWork,
			},
			{ name: "noiseReduction", label: "Noise Reduction", icon: HiOutlineMoon },
			{
				name: "oversizedWalkInClosets",
				label: "Oversized Walk-in Closets",
				icon: MdOutlineHomeWork,
			},
			{
				name: "privateTerraceBalcony",
				label: "Private Terrace / Balcony",
				icon: MdOutlineBalcony,
			},
			{ name: "sittingArea", label: "Sitting Area", icon: MdOutlineChair },
		],
	},
	{
		id: "outdoor-exterior-features",
		title: "Outdoor & Exterior features",
		items: [
			{ name: "gatedEntrance", label: "Gated Entrance", icon: MdOutlineLock },
			{
				name: "greenhouseGarden",
				label: "Greenhouse / Garden",
				icon: MdOutlineYard,
			},
			{
				name: "guestQuartersCasita",
				label: "Guest Quarters/Casita",
				icon: HiOutlineHome,
			},
			{ name: "helipad", label: "Helipad", icon: MdOutlineFlightTakeoff },
			{ name: "hotTub", label: "Hot Tub", icon: MdOutlineWhatshot },
			{ name: "infinityPool", label: "Infinity Pool", icon: MdOutlineWaves },
			{
				name: "motorCourt",
				label: "Motor Court / Circular Driveway",
				icon: MdOutlineDirectionsCar,
			},
			{
				name: "outdoorBarDiningArea",
				label: "Outdoor Bar / Dining Area",
				icon: MdOutlineLocalDining,
			},
			{
				name: "outdoorFireplaceFirePit",
				label: "Outdoor Fireplace / Fire Pit",
				icon: TbFlame,
			},
			{
				name: "outdoorKitchen",
				label: "Outdoor Kitchen",
				icon: MdOutlineKitchen,
			},
			{ name: "pool", label: "Pool", icon: MdPool },
			{ name: "privateDock", label: "Private Dock", icon: MdOutlineSailing },
			{ name: "privateGarden", label: "Private Garden", icon: MdOutlineYard },
			{
				name: "tennisSportCourt",
				label: "Tennis/Sport Court",
				icon: MdOutlineSportsBasketball,
			},
			{ name: "terrace", label: "Terrace", icon: MdOutlineBalcony },
		],
	},
	{
		id: "security-privacy",
		title: "Security & Privacy",
		items: [
			{ name: "gatedCommunity", label: "Gated Community", icon: MdOutlineLock },
			{ name: "guardHouse", label: "Guard House", icon: MdHome },
			{ name: "privateAccessRoad", label: "Private Access Road", icon: TbCar },
			{
				name: "security247",
				label: "24/7 Security",
				icon: MdOutlineSecurityIcon,
			},
			{
				name: "smartSecuritySystem",
				label: "Smart Security System",
				icon: MdSecurity,
			},
		],
	},
	{
		id: "technology-smart-features",
		title: "Technology & Smart features",
		items: [
			{
				name: "centralizedAudioSystem",
				label: "Centralized Audio System",
				icon: HiOutlineSpeakerWave,
			},
			{
				name: "highSpeedFiberInternet",
				label: "High-Speed Fiber Internet",
				icon: HiOutlineGlobeAlt,
			},
			{
				name: "homeAutomationSystem",
				label: "Home Automation System",
				icon: MdHome,
			},
			{
				name: "smartThermostat",
				label: "Smart Thermostat",
				icon: TbTemperature,
			},
			{
				name: "surveillanceCameras",
				label: "Surveillance Cameras",
				icon: HiOutlineCamera,
			},
		],
	},
	{
		id: "entertainment-features",
		title: "Entertainment features",
		items: [
			{ name: "bowlingAlley", label: "Bowling Alley", icon: TbBowling },
			{ name: "gameRoom", label: "Game Room", icon: MdOutlineGames },
			{ name: "homeTheater", label: "Home Theater", icon: MdOutlineTheaters },
			{
				name: "privateBarLounge",
				label: "Private Bar / Lounge",
				icon: MdOutlineLocalBar,
			},
			{ name: "privateGym", label: "Private Gym", icon: MdFitnessCenter },
			{
				name: "puttingGreen",
				label: "Putting Green",
				icon: MdOutlineGolfCourse,
			},
			{
				name: "recordingStudio",
				label: "Recording Studio",
				icon: HiOutlineMicrophone,
			},
			{ name: "wineCellarCave", label: "Wine Cellar / Cave", icon: TbBottle },
		],
	},
	{
		id: "location-views",
		title: "Location & Views",
		items: [
			{
				name: "backyard",
				label: "Backyard",
				icon: MdOutlineYard,
			},
			{ name: "beachAccess", label: "Beach Access", icon: MdOutlineWaves },
			{ name: "citySkylineViews", label: "City Skyline Views", icon: MdHome },
			{
				name: "courtyard",
				label: "Courtyard",
				icon: MdOutlineYard,
			},
			{
				name: "equestrianFacilities",
				label: "Equestrian Facilities",
				icon: TbHorse,
			},
			{
				name: "golfCourseViews",
				label: "Golf Course Views",
				icon: MdOutlineGolfCourse,
			},
			{ name: "lakefront", label: "Lakefront", icon: MdOutlineLandscape },
			{
				name: "mountainViews",
				label: "Mountain Views",
				icon: MdOutlineLandscape,
			},
			{ name: "oceanfront", label: "Oceanfront", icon: MdOutlineWaves },
			{ name: "privateVineyard", label: "Private Vineyard", icon: TbBottle },
			{ name: "scenicView", label: "Scenic View", icon: MdOutlineLandscape },
			{
				name: "streetView",
				label: "Street View",
				icon: MdOutlineDirectionsCar,
			},
			{ name: "waterfront", label: "Waterfront", icon: MdOutlineWaves },
			{ name: "waterView", label: "Water View", icon: MdOutlineWaves },
		],
	},
	{
		id: "service-memberships",
		title: "Services & Memberships",
		items: [
			{ name: "conciergeServices", label: "Concierge Services", icon: MdWork },
			{
				name: "exclusiveClubMemberships",
				label: "Exclusive Club Memberships",
				icon: MdOutlineGolfCourse,
			},
			{
				name: "housekeepingServices",
				label: "Housekeeping Services",
				icon: MdOutlineCleaningServices,
			},
			{
				name: "onSiteStaffQuarters",
				label: "On-Site Staff Quarters",
				icon: MdHome,
			},
			{
				name: "personalChef",
				label: "Personal Chef",
				icon: MdOutlineRestaurant,
			},
			{ name: "privateChauffeur", label: "Private Chauffeur", icon: TbCar },
		],
	},
	{
		id: "heating-cooling",
		title: "Heating and cooling",
		items: [
			{
				name: "architecturalConcealedAC",
				label: "Architectural Concealed AC",
				icon: MdHome,
			},
			{
				name: "buildingCentralHeating",
				label: "Building Central Heating",
				icon: TbFlame,
			},
			{
				name: "centralAirConditioning",
				label: "Central Air Conditioning",
				icon: MdAcUnit,
			},
			{
				name: "chilledWaterCoolingSystem",
				label: "Chilled Water Cooling System",
				icon: MdOutlineWaves,
			},
			{
				name: "ductlessMiniSplitAC",
				label: "Ductless Mini-Split AC",
				icon: TbAirConditioning,
			},
			{ name: "gasFurnace", label: "Gas Furnace", icon: TbFlame },
			{
				name: "geothermalCooling",
				label: "Geothermal Cooling",
				icon: TbTemperature,
			},
			{
				name: "geothermalHeatPump",
				label: "Geothermal Heat Pump",
				icon: MdOutlineWhatshot,
			},
			{
				name: "hydronicBaseboardHeating",
				label: "Hydronic Baseboard Heating",
				icon: TbTemperature,
			},
			{
				name: "integratedAirPurification",
				label: "Integrated Air Purification & Humidity Control Systems",
				icon: MdWifi,
			},
			{
				name: "radiantFloorHeating",
				label: "Radiant Floor Heating",
				icon: TbFlame,
			},
			{
				name: "radiatorBasedHeating",
				label: "Radiator-based Heating",
				icon: TbFlame,
			},
			{ name: "solarHeating", label: "Solar Heating", icon: TbSolarPanel },
			{
				name: "underfloorCoolingSystems",
				label: "Underfloor Cooling Systems",
				icon: MdAcUnit,
			},
		],
	},
];

export { amenities, features };
