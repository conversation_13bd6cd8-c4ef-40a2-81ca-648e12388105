"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { IoMdAdd } from "react-icons/io";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import AddNewListingDialogHeader from "./AddNewListingDialogHeader";
import AddNewListingForm, {
	useFormMethods,
} from "./listing-form/AddNewListingForm";
import { ReviewStatusType } from "@/db/schema/enums";
import { useListingSubmit } from "@/hooks/useListingSubmit";

const AddNewListingButton = () => {
	const formMethods = useFormMethods();
	const { handleCreateSubmit, createDialogOpen, setCreateDialogOpen } =
		useListingSubmit();

	const handleFormSubmit = async (
		reviewStatus: ReviewStatusType
	): Promise<void> => {
		return new Promise((resolve, reject) => {
			const submitHandler = formMethods.handleSubmit(
				async (data) => {
					try {
						await handleCreateSubmit({
							data,
							reviewStatus,
						});
						// Close dialog and show success message
						setCreateDialogOpen(false);
						// Reset form after successful submission
						formMethods.reset();
						toast.success("Listing created successfully", {
							description: "New listing has been added to the system",
						});
						resolve();
					} catch (error: any) {
						toast.error("Failed to create listing", {
							description: error?.message || "An unexpected error occurred",
						});
						reject(error);
					}
				},
				() => {
					reject(new Error("Form validation failed"));
				}
			);
			submitHandler();
		});
	};

	// Handle dialog close and reset form
	const handleDialogChange = (isOpen: boolean) => {
		setCreateDialogOpen(isOpen);
		if (!isOpen) {
			// Reset form when dialog closes
			formMethods.reset();
		}
	};

	return (
		<div>
			<Button className="mr-2" onClick={() => setCreateDialogOpen(true)}>
				<IoMdAdd />
				Add a new listing
			</Button>
			<Dialog open={createDialogOpen} onOpenChange={handleDialogChange}>
				<DialogContent
					closeButton={false}
					className="sm:max-w-2xl md:max-w-4xl lg:max-w-7xl max-h-[90dvh] overflow-hidden flex flex-col"
				>
					<DialogTitle className="sr-only">Add New Listing</DialogTitle>
					<DialogDescription className="sr-only">
						Please fill out the form below to add a new property listing
					</DialogDescription>
					<DialogHeader>
						<AddNewListingDialogHeader
							closeDialog={() => setCreateDialogOpen(false)}
							onSubmit={handleFormSubmit}
						/>
					</DialogHeader>
					<AddNewListingForm
						formMethods={formMethods}
						onSubmit={handleFormSubmit}
					/>
				</DialogContent>
			</Dialog>
		</div>
	);
};

export default AddNewListingButton;
