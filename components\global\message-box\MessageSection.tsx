import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useCreateChatClient } from "stream-chat-react";
import { toast } from "sonner";
import { MessageSectionProps } from "./types";
import { CustomChannelData } from "@/types/channel";

// Extract message section logic
export const MessageSection = ({
	me,
	token,
	listingId,
	backPage,
	message,
	setMessage,
	contact,
	previewUrl,
	data,
}: MessageSectionProps) => {
	const client = useCreateChatClient({
		apiKey: process.env.NEXT_PUBLIC_GETSTREAM_API_KEY!,
		tokenOrProvider: token,
		userData: {
			id: me.id!,
			name: me.userName,
			image: me.imageUrl || undefined,
		},
	});

	const handleSend = async () => {
		if (contact && message.trim()) {
			if (!client) {
				toast.error("Chat client is not available. Please try again.");
				return;
			}

			try {
				const meId = me.role === "buyer" ? me.id : contact;
				const receiverId = me.role === "buyer" ? contact : me.id;

				// Updated channel creation to match schema changes
				const channel = client.channel(
					"messaging",
					`${meId && meId.localeCompare(receiverId) > 0 ? `${meId}-${listingId}-${receiverId}` : `${receiverId}-${listingId}-${meId}`}`,
					{
						members: [meId, receiverId],
						image: previewUrl,
						title: data.title,
						listingId,
					} as CustomChannelData
				);

				await channel.create();
				await channel.sendMessage({
					text: message,
				});

				setMessage("");
				toast.success("Message sent successfully!");
			} catch (error) {
				console.error("Error sending message:", error);
				toast.error("Failed to send message. Please try again.");
			}
		}
	};

	return (
		<div>
			{backPage !== "MG Exclusives" && (
				<label className="block mb-1 text-base font-bold">Message</label>
			)}
			<Textarea
				value={message}
				onChange={(e) => setMessage(e.target.value)}
				placeholder="Type your message here..."
				className="w-full resize-none min-h-[150px]"
				// Add keyboard shortcut support
				onKeyDown={(e) => {
					// Send message when pressing Ctrl+Enter or Command+Enter
					if (
						(e.ctrlKey || e.metaKey) &&
						e.key === "Enter" &&
						message.trim() &&
						contact
					) {
						e.preventDefault();
						handleSend();
					}
				}}
			/>
			{/* Send button */}
			<div className="flex justify-end mt-6">
				<Button
					onClick={handleSend}
					disabled={!contact || !message.trim()}
					className="w-full font-bold sm:w-auto"
				>
					Send
				</Button>
			</div>
		</div>
	);
};
