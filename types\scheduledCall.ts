import { ListingData } from "./listing";

// Recipients type for scheduled calls
export interface Recipients {
	[key: string]: boolean;
}

// Scheduled call interface
export interface ScheduledCall {
	id: string | number;
	participant: string;
	datetime: string | Date;
	status: string;
	details?: string;
	allIds: string[];
	listingDetails?: ListingData;
	senderId: string;
	listing: ListingData;
	callName: string;
	listingId: string;
	scheduleTime: string;
	recipients?: Recipients;
}
