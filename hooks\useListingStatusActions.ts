import { ReviewerTypeType } from "@/db/schema";
import { updateListingReviewRequestStatus } from "@/db/services/listingReviewRequests.service";
import { ListingData, ListingPartialData } from "@/types/listing";
import { KeyedMutator } from "swr";

export const useListingStatusActions = (mutate?: KeyedMutator<any>) => {
	// Helper function to filter listings by review request ID
	const filterListingsByReviewRequestId = (
		listings: ListingData[],
		listingReviewRequestId: string
	) => {
		return listings.filter(
			(listing: ListingData) =>
				listing.listingReviewRequest?.id !== listingReviewRequestId
		);
	};

	// Helper function to optimistically remove a listing from cache
	const optimisticallyRemoveListing = (listingReviewRequestId: string) => {
		if (!mutate) return;

		mutate(
			(currentData: any) => {
				if (!currentData) return currentData;

				// Handle different data structures (single page or infinite scroll)
				if (Array.isArray(currentData)) {
					// For infinite scroll data structure
					const data = currentData.map((page: any) => ({
						...page,
						listings: filterListingsByReviewRequestId(
							page.listings,
							listingReviewRequestId
						),
					}));
					return data;
				} else if (currentData.listings) {
					// For single page data structure
					return {
						...currentData,
						listings: filterListingsByReviewRequestId(
							currentData.listings,
							listingReviewRequestId
						),
					};
				}
				return currentData;
			},
			false // Don't revalidate immediately to keep optimistic update
		);
	};

	// Helper function to revalidate cache on error
	const revalidateOnError = () => {
		if (!mutate) return;
		mutate(undefined, { revalidate: true });
	};

	// Archive a listing by changing its review status
	const archiveListing = async (
		listingReviewRequestId?: string
	): Promise<void> => {
		if (!listingReviewRequestId) return;

		try {
			// Optimistically update cached data by removing the archived item
			optimisticallyRemoveListing(listingReviewRequestId);

			// Perform the actual API call
			await updateListingReviewRequestStatus({
				id: listingReviewRequestId,
				reviewStatus: "archived",
			});
		} catch (error) {
			console.error("Failed to archive listing:", error);
			// Revalidate on error to restore correct state
			revalidateOnError();
			throw error;
		}
	};

	// Reactivate a listing by changing its review status to pending
	const reactivateListing = async (
		listingReviewRequestId?: string
	): Promise<void> => {
		if (!listingReviewRequestId) return;

		try {
			// Optimistically update cached data by removing the reactivated item from archived list
			optimisticallyRemoveListing(listingReviewRequestId);

			// Perform the actual API call
			await updateListingReviewRequestStatus({
				id: listingReviewRequestId,
				reviewStatus: "pending",
			});
		} catch (error) {
			console.error("Failed to reactivate listing:", error);
			// Revalidate on error to restore correct state
			revalidateOnError();
			throw error;
		}
	};

	// Decline a listing by changing its review status to declined
	const declineListing = async (
		listingReviewRequestId?: string
	): Promise<void> => {
		if (!listingReviewRequestId) return;

		try {
			// Optimistically update cached data by removing the declined item
			optimisticallyRemoveListing(listingReviewRequestId);

			// Perform the actual API call
			await updateListingReviewRequestStatus({
				id: listingReviewRequestId,
				reviewStatus: "declined",
			});
		} catch (error) {
			console.error("Failed to decline listing:", error);
			// Revalidate on error to restore correct state
			revalidateOnError();
			throw error;
		}
	};

	// Approve a listing by changing its review status to approved
	const approveListing = async (
		listingReviewRequestId?: string
	): Promise<void> => {
		if (!listingReviewRequestId) return;

		try {
			// Optimistically update cached data by removing the approved item
			optimisticallyRemoveListing(listingReviewRequestId);

			// Perform the actual API call
			await updateListingReviewRequestStatus({
				id: listingReviewRequestId,
				reviewStatus: "approved",
			});
		} catch (error) {
			console.error("Failed to approve listing:", error);
			// Revalidate on error to restore correct state
			revalidateOnError();
			throw error;
		}
	};

	// Send listing for review by changing its review status to pending
	const sendForReview = async ({
		listingReviewRequestId,
		reviewerType = "admin",
		reviewerId,
	}: {
		listingReviewRequestId?: string;
		reviewerType?: ReviewerTypeType;
		reviewerId?: string;
	}): Promise<void> => {
		if (!listingReviewRequestId) return;

		try {
			// Optimistically update cached data by removing the item sent for review
			optimisticallyRemoveListing(listingReviewRequestId);

			// Perform the actual API call
			await updateListingReviewRequestStatus({
				id: listingReviewRequestId,
				reviewStatus: "pending",
				reviewerType,
				reviewerId,
			});
		} catch (error) {
			console.error("Failed to send listing for review:", error);
			// Revalidate on error to restore correct state
			revalidateOnError();
			throw error;
		}
	};

	// Optimistically update a single listing item in cache by id and partial fields
	const optimisticUpdateItem = (
		listingReviewRequestId: string,
		partial: ListingPartialData
	) => {
		if (!mutate) return;

		mutate(
			(currentData: any) => {
				if (!currentData) return currentData;

				// Handle infinite scroll data structure
				if (Array.isArray(currentData)) {
					const data = currentData.map((page: any) => ({
						...page,
						listings: page.listings.map((listing: ListingData) =>
							listing.listingReviewRequest?.id === listingReviewRequestId
								? { ...listing, ...partial }
								: listing
						),
					}));
					return data;
				} else if (currentData.listings) {
					// Handle single page data structure
					return {
						...currentData,
						listings: currentData.listings.map((listing: ListingData) =>
							listing.listingReviewRequest?.id === listingReviewRequestId
								? { ...listing, ...partial }
								: listing
						),
					};
				}
				return currentData;
			},
			false // Don't revalidate immediately to keep optimistic update
		);
	};

	return {
		archiveListing,
		reactivateListing,
		declineListing,
		approveListing,
		sendForReview,
		optimisticallyRemoveListing,
		optimisticUpdateItem, // Export the new function
	};
};
