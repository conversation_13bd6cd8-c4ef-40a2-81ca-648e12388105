import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { getUserByClerkId } from "./db/services/users.service";
import { notFound } from "next/navigation";
import { NextResponse } from "next/server";
import { getRedirectUrlForRole } from "./lib/redirect-utils";
import { TERMS_COOKIE_NAME } from "./constant/const";

const isNotProtectedRoute = createRouteMatcher([
	"/sign-in(.*)",
	"/sign-up(.*)",
	"/api/webhooks(.*)",
	"/api/test(.*)",
	"/api/internal(.*)", // Add internal API route to unprotected routes
]);

// Define base routes that are common to multiple roles
const baseRoutes = [
	"/api(.*)",
	"/dashboard",
	"/dashboard/inbox(.*)",
	"/dashboard/schedule(.*)",
	"/terms-of-service",
];

const isAdminRoute = createRouteMatcher([
	...baseRoutes,
	"/",
	"/dashboard/user-management(.*)",
	"/dashboard/listing-management(.*)",
	"/dashboard/foundation(.*)",
	"/dashboard/exclusives(.*)",
]);

const isAdvertiserRoute = createRouteMatcher([
	...baseRoutes,
	"/dashboard/listings",
	"/dashboard/profile",
]);

const isBuyerRoute = createRouteMatcher([
	...baseRoutes,
	"/",
	"/dashboard/listings/(.*)",
	"/dashboard/profile",
	"/dashboard/foundation(.*)",
	"/dashboard/exclusives(.*)",
]);

// Use a mapping of roles to their corresponding route matchers
const roleRouteMatchers = {
	admin: isAdminRoute,
	advertiser: isAdvertiserRoute,
	buyer: isBuyerRoute,
	searcher: isBuyerRoute,
	concierge: isBuyerRoute,
	combination: isBuyerRoute,
};

export default clerkMiddleware(async (auth, req) => {
	// console.log(isNotProtectedRoute(req));

	let userId: string | null = null;
	let redirectToSignIn: (() => Response) | null = null;

	try {
		const authResult = await auth();
		userId = authResult.userId;
		redirectToSignIn = authResult.redirectToSignIn;
	} catch (error) {
		// Handle auth service errors gracefully
		console.error("Authentication service error in middleware:", {
			error: error instanceof Error ? error.message : String(error),
			path: req.nextUrl.pathname,
			timestamp: new Date().toISOString(),
		});

		// If auth service fails, allow access to public routes only
		if (isNotProtectedRoute(req)) {
			return NextResponse.next();
		}

		// For protected routes, return a generic error response or redirect to error page
		console.log("Auth service failed, blocking access to protected route");
		return NextResponse.redirect(new URL("/sign-in", req.url));
	}

	if (!userId) {
		if (!isNotProtectedRoute(req)) {
			// Add custom logic to run before redirecting
			return redirectToSignIn
				? redirectToSignIn()
				: NextResponse.redirect(new URL("/sign-in", req.url));
		}

		const response = NextResponse.next();
		response.cookies.delete("nextjs");
		console.log("User not authenticated, redirecting to sign-in");
		return response;
	}

	if (userId && !isNotProtectedRoute(req)) {
		try {
			const user = await getUserByClerkId(userId);
			if (!user) {
				// User not found in the database, handle accordingly
				console.log("User not found in the database");
				return redirectToSignIn
					? redirectToSignIn()
					: NextResponse.redirect(new URL("/sign-in", req.url));
			}

			// Check if user has accepted terms using cookie
			const hasAcceptedTerms =
				req.cookies.get(TERMS_COOKIE_NAME)?.value === "true";

			// If terms not accepted, redirect to terms page
			if (
				!hasAcceptedTerms &&
				!req.nextUrl.pathname.startsWith("/terms-of-service")
			) {
				return NextResponse.redirect(new URL("/terms-of-service", req.url));
			}

			// Skip role-based route checking if on terms page
			if (req.nextUrl.pathname.startsWith("/terms-of-service")) {
				if (hasAcceptedTerms) {
					if (user.role === "advertiser") {
						return NextResponse.redirect(
							new URL("/dashboard/listings", req.url)
						);
					}
					return NextResponse.redirect(new URL("/", req.url));
				} else {
					return NextResponse.next();
				}
			}

			// Get the matcher function for the user's role
			const routeMatcher = roleRouteMatchers[user.role];

			if (routeMatcher && !routeMatcher(req)) {
				if (req.nextUrl.pathname === "/dashboard") {
					const redirectUrl = getRedirectUrlForRole(user.role);

					return redirectUrl
						? NextResponse.redirect(new URL(redirectUrl, req.url))
						: notFound();
				}

				// Special case: redirect buyers from listings to favorites
				if (user.role === "buyer") {
					return NextResponse.redirect(
						new URL("/dashboard/listings/favorites", req.url)
					);
				}

				if (user.role === "advertiser") {
					// Redirect advertisers to their listings if they try to access a non-advertiser route
					return NextResponse.redirect(new URL("/dashboard/listings", req.url));
				}

				// Handle other unauthorized routes
				return notFound();
			}
		} catch (error) {
			// Handle database errors gracefully
			console.error("Database error in middleware when fetching user:", {
				userId,
				error: error instanceof Error ? error.message : String(error),
				path: req.nextUrl.pathname,
				timestamp: new Date().toISOString(),
			});

			// On database error, redirect to sign-in to prevent app from breaking
			// This ensures the middleware doesn't fail completely
			console.log("Redirecting to sign-in due to database error");
			return redirectToSignIn
				? redirectToSignIn()
				: NextResponse.redirect(new URL("/sign-in", req.url));
		}
	}
});

export const config = {
	matcher: [
		// Skip Next.js internals, static files, and .well-known paths
		"/((?!_next|.well-known|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
		// Always run for API routes
		"/(api|trpc)(.*)",
	],
};
