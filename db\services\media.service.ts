"use server";
import { eq, and } from "drizzle-orm";
import { db } from "../index";
import { media } from "../schema/media";
import { sendSQSMessage } from "@/lib/SQS";

export type MediaType = typeof media.$inferInsert;
export type MediaSelectType = typeof media.$inferSelect;

/**
 * Gets all media items for a specific listing
 */
export const getListingMedia = async (
	listingId: string
): Promise<MediaSelectType[]> => {
	try {
		return await db.select().from(media).where(eq(media.listingId, listingId));
	} catch (error) {
		console.error("Error getting listing media:", error);
		return [];
	}
};

/**
 * Gets a single media item by ID
 */
export const getMediaById = async (
	mediaId: string
): Promise<MediaSelectType | null> => {
	try {
		const result = await db.select().from(media).where(eq(media.id, mediaId));

		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting media item:", error);
		return null;
	}
};

/**
 * Creates multiple media items for a listing
 */
export const createListingMedia = async (
	listingId: string,
	mediaItems: Omit<MediaType, "listingId">[]
): Promise<boolean> => {
	try {
		if (mediaItems.length === 0) return true;

		await db.insert(media).values(
			mediaItems.map((item) => ({
				...item,
				listingId,
			}))
		);
		return true;
	} catch (error) {
		console.error("Error creating media items:", error);
		return false;
	}
};

/**
 * Creates a single media item
 */
export const createMediaItem = async (
	mediaItem: MediaType
): Promise<MediaSelectType | null> => {
	try {
		const [newMedia] = await db.insert(media).values(mediaItem).returning();

		// Only send to SQS if the key exists
		if (newMedia.key) {
			await sendSQSMessage([
				{
					key: newMedia.key,
					assetId: newMedia.id,
					format: "ZLGLPhoto",
				},
			]);
		}
		return newMedia;
	} catch (error) {
		console.error("Error creating media item:", error);
		return null;
	}
};

/**
 * Updates an existing media item
 */
export const updateMediaItem = async (
	mediaId: string,
	updates: Partial<Omit<MediaType, "id" | "listingId">>
): Promise<boolean> => {
	try {
		await db
			.update(media)
			.set({
				...updates,
				updatedAt: new Date(),
			})
			.where(eq(media.id, mediaId));
		return true;
	} catch (error) {
		console.error("Error updating media item:", error);
		return false;
	}
};

/**
 * Updates a media item by ID and listing ID
 */
export const updateMediaItemById = async (
	listingId: string,
	mediaId: string,
	updates: Partial<Omit<MediaType, "id" | "listingId">>
): Promise<boolean> => {
	try {
		await db
			.update(media)
			.set({
				...updates,
				updatedAt: new Date(),
			})
			.where(and(eq(media.listingId, listingId), eq(media.id, mediaId)));
		return true;
	} catch (error) {
		console.error("Error updating media item:", error);
		return false;
	}
};

/**
 * Deletes a media item by ID
 */
export const deleteMediaItem = async (mediaId: string): Promise<boolean> => {
	try {
		await db.delete(media).where(eq(media.id, mediaId));
		return true;
	} catch (error) {
		console.error("Error deleting media item:", error);
		return false;
	}
};

/**
 * Deletes all media items associated with a listing
 */
export const deleteListingMedia = async (
	listingId: string
): Promise<boolean> => {
	try {
		await db.delete(media).where(eq(media.listingId, listingId));
		return true;
	} catch (error) {
		console.error("Error deleting listing media:", error);
		return false;
	}
};

/**
 * Synchronizes media items for a listing
 * Handles adding new items, updating existing ones, and deleting removed ones
 * Returns void to match other concurrent operations
 */
export const syncListingMedia = async (
	listingId: string,
	mediaItems: Omit<MediaType, "listingId">[]
): Promise<void> => {
	try {
		// Get existing media
		const existingMedia = await getListingMedia(listingId);
		const existingIds = existingMedia.map((item) => item.id);
		const newItemsWithIds = mediaItems.filter(
			(item) => "id" in item && item.id
		);
		const newIds = newItemsWithIds.map((item) => item.id);

		// Delete items not in the new list
		for (const item of existingMedia) {
			if (!newIds.includes(item.id)) {
				// console.log("Deleting media item not in new list:", item.id);
				await deleteMediaItem(item.id);
			}
		}

		// Add or update items
		for (const item of mediaItems) {
			if ("id" in item && item.id && existingIds.includes(item.id)) {
				// Update existing item sortOrder
				await updateMediaItem(item.id, {
					sortOrder: item.sortOrder,
				});
			} else {
				// Add new item
				await createMediaItem({
					...item,
					listingId,
				});
			}
		}
	} catch (error) {
		console.error("Error synchronizing media items:", error);
		throw new Error(`Failed to sync media items: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
};

/**
 * Gets all media items in the database
 */
export const getAllMedia = async (): Promise<MediaSelectType[]> => {
  try {
    return await db.select().from(media);
  } catch (error) {
    console.error("Error getting all media:", error);
    return [];
  }
};
