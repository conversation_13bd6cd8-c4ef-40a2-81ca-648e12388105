import React from "react";
import Image from "next/image";
import { UsersMap } from "@/hooks/useUsersByIds";

interface ParticipantsListProps {
	participantIds: string[];
	participantDetails: UsersMap;
	currentUserId: string;
}

const ParticipantsList: React.FC<ParticipantsListProps> = ({
	participantIds,
	participantDetails,
	currentUserId,
}) => {
	return (
		<div className="flex flex-col gap-4 pb-7.5">
			<div className="text-lg font-bold leading-7">Participants</div>
			<div className="flex flex-col gap-3">
				{participantIds
					.filter((id) => id !== currentUserId)
					.map((participantId) => {
						const participant = participantDetails[participantId];
						return (
							<div
								key={participantId}
								className="flex flex-row gap-4 place-items-center"
							>
								{participant?.imageUrl && (
									<Image
										alt="participant avatar"
										width={40}
										height={40}
										className="object-cover w-10 h-10 bg-white rounded-full"
										src={participant.imageUrl}
									/>
								)}
								<div className="text-base leading-6">
									{participant?.firstName || participantId}
								</div>
							</div>
						);
					})}
			</div>
		</div>
	);
};

export default ParticipantsList;
