import Image from "next/image";
import { Loader2 } from "lucide-react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../ui/card";
import { TiptapEditor } from "../ui/tiptap-editor";

type CaseCardProps = {
	data: any;
};

const CaseCard = ({ data }: CaseCardProps) => {
	// Check if all media items have status 'completed'
	const allMediaCompleted =
		data.media &&
		data.media.length > 0 &&
		data.media.every((media: any) => media.status === "completed");

	// Get the first media item with preview if all are completed
	const previewMedia =
		data.media &&
		data.media.length > 0 &&
		allMediaCompleted &&
		data.media[0]?.preview
			? `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${data.media[0].preview}`
			: "https://placehold.co/600x400/jpg";

	return (
		<Card className="py-0 bg-transparent border-0 shadow-none">
			<CardContent className="px-0">
				{data.media && data.media.length > 0 && !allMediaCompleted ? (
					<div className="flex flex-col items-center justify-center w-full bg-gray-200 h-96">
						<Loader2 className="w-8 h-8 mb-2 text-gray-500 animate-spin" />
						<p className="font-medium text-gray-600">Processing</p>
					</div>
				) : (
					<Image
						src={previewMedia}
						alt={data.title || "Case image"}
						width={600}
						height={400}
						className="object-cover w-full h-96"
					/>
				)}
			</CardContent>
			<CardHeader className="px-0 space-y-2">
				<CardTitle className="text-2xl font-normal truncate text-slate-900 font-lora">
					{data.title}
				</CardTitle>
				<CardDescription>
					{data.description && (
						<TiptapEditor
							value={data.description}
							className="min-h-0 p-0 text-base font-normal text-slate-900 line-clamp-3 font-inter"
						/>
					)}
				</CardDescription>
			</CardHeader>
		</Card>
	);
};

export default CaseCard;
