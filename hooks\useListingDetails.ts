import useSWR from 'swr';
import { fetcher } from '@/lib/utils';
import { ListingData } from "@/types/listing";

// Hook return type
interface UseListingDetailsReturn {
	listingDetails: ListingData;
	isLoading: boolean;
	isError: any;
}

export const useListingDetails = (
	listingId: string
): UseListingDetailsReturn => {
	const { data, error } = useSWR(
		listingId ? `/api/listings/${listingId}` : null,
		fetcher,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: true,
			dedupingInterval: 60000, // Cache for 1 minute
			refreshInterval: 600000, // Refresh every 10 minutes
			keepPreviousData: true,
		}
	);

	return {
		listingDetails: data,
		isLoading: !error && !data,
		isError: error,
	};
};
