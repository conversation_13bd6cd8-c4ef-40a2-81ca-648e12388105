"use server";
import { clerkClient } from "@clerk/nextjs/server";

// Error handling utilities
type ClerkErrorResponse = {
	status: "error";
	code: string;
	message: string;
	details?: any;
};

type ClerkSuccessResponse<T> = {
	status: "success";
	data: T;
};

type ClerkResponse<T> = ClerkSuccessResponse<T> | ClerkErrorResponse;

function handleClerkError(error: any): ClerkErrorResponse {
	console.error("Clerk API Error:", error.errors);

	// Extract useful information from Clerk error
	const code = error.errors?.[0]?.code || "unknown_error";
	const message =
		error.errors?.[0]?.message || error.message || "An unknown error occurred";
	const details = error.errors || error.details || {};

	return {
		status: "error",
		code,
		message,
		details,
	};
}

type UserDataType = {
	emailAddress: string[];
	firstName: string;
	lastName: string;
	username: string;
	phoneNumber?: string[];
	publicMetadata: Record<string, unknown>;
};

/**
 * Create a new user through Clerk API
 * @param userData User data to create new user
 * @returns The created user object or error response
 */
export async function createClerkUser(
	userData: UserDataType
): Promise<ClerkResponse<any>> {
	// console.log("Creating user with data:", userData);
	try {
		const client = await clerkClient();
		const user = await client.users.createUser(userData);

		const serializedUser = JSON.parse(JSON.stringify(user));
		return {
			status: "success",
			data: serializedUser,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}

/**
 * Get a user by their ID
 * @param userId The ID of the user to fetch
 * @returns User data or error response
 */
export async function getClerkUserById(
	userId: string
): Promise<ClerkResponse<any>> {
	try {
		const client = await clerkClient();
		const user = await client.users.getUser(userId);
		const serializedUser = JSON.parse(JSON.stringify(user));
		return {
			status: "success",
			data: serializedUser,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}

/**
 * List all users with optional filtering
 * @param params Optional parameters for pagination and filtering
 * @returns List of users or error response
 */
export async function listClerkUsers(params?: {
	limit?: number;
	offset?: number;
	query?: string;
}): Promise<ClerkResponse<any>> {
	try {
		const client = await clerkClient();
		const users = await client.users.getUserList(params);
		const serializedUser = JSON.parse(JSON.stringify(users));
		return {
			status: "success",
			data: serializedUser,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}

/**
 * Update user information
 * @param userId ID of the user to update
 * @param userData Updated user data
 * @returns Updated user object or error response
 */
export async function updateClerkUser(
	userId: string,
	userData: {
		firstName?: string;
		lastName?: string;
		emailAddress?: string;
		phoneNumber?: string;
		publicMetadata?: Record<string, unknown>;
		privateMetadata?: Record<string, unknown>;
		// Add other updatable fields as needed
	}
): Promise<ClerkResponse<any>> {
	try {
		const client = await clerkClient();
		const updatedUser = await client.users.updateUser(userId, userData);

		const serializedUser = JSON.parse(JSON.stringify(updatedUser));
		return {
			status: "success",
			data: serializedUser,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}

/**
 * Delete a user
 * @param userId ID of the user to delete
 * @returns true if deletion was successful or error response
 */
export async function deleteClerkUser(
	userId: string
): Promise<ClerkResponse<any>> {
	try {
		const client = await clerkClient();
		await client.users.deleteUser(userId);
		return {
			status: "success",
			data: true,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}

/**
 * Check if an email address is already in use
 * @param email Email address to check
 * @returns true if email is available, false if already in use, or error response
 */
export async function isEmailAvailable(
	email: string
): Promise<ClerkResponse<boolean>> {
	try {
		const client = await clerkClient();
		// Query for users with the given email address
		const users = await client.users.getUserList({
			emailAddress: [email],
		});

		// If the list is empty, the email is available
		const isAvailable = users.data.length === 0;

		return {
			status: "success",
			data: isAvailable,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}

/**
 * Check if an alias/username is already in use
 * @param alias Username to check
 * @returns true if alias is available, false if already in use, or error response
 */
export async function isAliasAvailable(
	alias: string
): Promise<ClerkResponse<boolean>> {
	try {
		const client = await clerkClient();
		// Query for users with the given username
		const users = await client.users.getUserList({
			username: [alias],
		});

		// If the list is empty, the alias is available
		const isAvailable = users.data.length === 0;

		return {
			status: "success",
			data: isAvailable,
		};
	} catch (error: any) {
		return handleClerkError(error);
	}
}
