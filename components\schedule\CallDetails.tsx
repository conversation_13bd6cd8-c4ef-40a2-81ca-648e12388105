import React from "react";
import { format } from "date-fns";
import CallDetailsSkeleton from "./CallDetailsSkeleton";
import ListingPreview from "./ListingPreview";
import ParticipantsList from "./ParticipantsList";
import { ScheduledCall } from "@/types/scheduledCall";
import { UsersMap } from "@/hooks/useUsersByIds";

interface CallDetailsProps {
	selectedCall: ScheduledCall | null;
	isLoading: boolean;
	participantDetails: UsersMap;
	currentUserId: string;
	getSafeImageUrl: (imagePath?: string) => string | null;
	onJoinCall: (call: ScheduledCall) => void;
	isClientReady: boolean;
}

const CallDetails: React.FC<CallDetailsProps> = ({
	selectedCall,
	isLoading,
	participantDetails,
	currentUserId,
	getSafeImageUrl,
	onJoinCall,
	isClientReady,
}) => {
	if (isLoading) {
		return <CallDetailsSkeleton />;
	}

	if (!selectedCall) {
		return (
			<div className="flex items-center justify-center h-64 text-lg text-gray-500">
				Select a call to see details
			</div>
		);
	}

	return (
		<>
			<ListingPreview
				listing={selectedCall.listing}
				getSafeImageUrl={getSafeImageUrl}
			/>

			{/* Call details section */}
			<div className="justify-center w-full pt-11 px-15 font-manrope">
				<div>
					<div className="w-full px-12 bg-white rounded-lg shadow">
						<h3 className="text-[28px] leading-10 pt-8">
							{selectedCall.details}
						</h3>
						<div className="pt-3.5 text-xl">
							<div className="flex flex-col gap-1 text-xl font-bold leading-7">
								<span>{format(selectedCall.datetime, "MMMM d, yyyy")}</span>
								<span>{format(selectedCall.datetime, "HH:mm")}</span>
							</div>
						</div>
						<div className="h-[1px] w-full bg-[#B6B6B6] my-7"></div>
						<ParticipantsList
							participantIds={selectedCall.allIds}
							participantDetails={participantDetails}
							currentUserId={currentUserId}
						/>
					</div>
					<div className="flex flex-row justify-center pt-8">
						<button
							className="px-[14px] py-[7px] font-manrope text-xl font-bold leading-7 text-center bg-[#10162D] w-[229px] text-[#F9F9F8] rounded-[8px] hover:bg-[#10162D]/50 hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
							onClick={() => onJoinCall(selectedCall)}
							disabled={!isClientReady}
						>
							Join call
						</button>
					</div>
				</div>
			</div>
		</>
	);
};

export default CallDetails;
