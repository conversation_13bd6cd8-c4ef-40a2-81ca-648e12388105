import SignInForm from "@/components/auth/SignInForm";
import Logo from "@/components/ui/Logo";
import { auth } from "@clerk/nextjs/server";
import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function Page() {
	const { userId } = await auth();
	if (userId) {
		redirect(`/`);
	}
	return (
		<div className="flex flex-row w-full h-full">
			<div className="flex flex-col items-center justify-start w-full h-full max-w-2xl gap-y-4 z-1">
				<div className="h-1/5"></div>
				<Logo width={400} height={129} className="mb-16" />

				<SignInForm />
				<Link
					href="#"
					className="mt-8 text-base font-normal underline text-slate-900 font-manrope hover:text-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500"
				>
					Need support?
				</Link>
			</div>
			<div className="relative flex-1">
				<Image
					sizes="100vw"
					alt="background"
					src="/images/MG-loginPhoto.png"
					fill
					className="object-cover"
					priority
				/>
			</div>
		</div>
	);
}
