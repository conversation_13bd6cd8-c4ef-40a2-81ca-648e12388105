"use server";
import {
	eq,
	ilike,
	and,
	or,
	desc,
	asc,
	gte,
	lte,
	inArray,
	count,
	sql,
} from "drizzle-orm";
import { db } from "../index";
import { listings } from "../schema/listings";
import { listingReviewRequests } from "../schema/listingReviewRequests";
import { favoritesListings } from "../schema/favoritesListings";
import { locations } from "../schema/locations";
import { users } from "../schema/users"; // Add users import
import {
	PropertyTypeType,
	ReviewStatusType,
	ReviewerTypeType,
	VisibilityType, // Add VisibilityType import
} from "../schema/enums"; // Update import to include types
import { ListingFormValues } from "@/lib/schemas/listing-schema";
import { getCurrentUser } from "./users.service";
import { nanoid } from "nanoid";
import slugify from "slugify";

import {
	upsertVector,
	deleteVector,
	ListingVectorData,
	upsertVectorPartial,
	PartialListingVectorData,
} from "@/lib/vectorHelpers";

import { upsertLocation, LocationType } from "./locations.service";
import {
	upsertRealEstateDetails,
	RealEstateDetailsType,
	getListingIdsByRealEstateFilters,
} from "./realEstateDetails.service";

import { syncListingMedia, MediaType } from "./media.service";
import { upsertListingReviewRequest } from "./listingReviewRequests.service"; // Add upsertListingReviewRequest import

export type ListingType = typeof listings.$inferInsert;
export type ListingSelectType = typeof listings.$inferSelect;

// Add sort options type
export type SortOption = "newest" | "oldest";

/**
 * Helper function to convert any promise to a void promise
 * Reduces boilerplate in batch operations
 */
const toVoidPromise = <T>(promise: Promise<T>): Promise<void> =>
	promise.then(() => {});

/**
 * Converts form values to database format
 */
const formToDbFormat = async (values: ListingFormValues) => {
	const currentUser = await getCurrentUser();
	if (!currentUser) {
		throw new Error("User not authenticated");
	}

	// Generate a unique slug from the title
	const baseSlug = slugify(values.propertyTitle, { lower: true, strict: true });
	const uniqueId = nanoid(6);
	const slug = `${baseSlug}-${uniqueId}`;

	// Create listings record using amenities array directly from form values
	const listingData: ListingType = {
		userId: currentUser.id,
		listingType: "sale", // Default or from form
		title: values.propertyTitle,
		slug,
		description: values.description || "",
		amenities: values.amenities || [], // Use amenities array directly from form
		price: String(values.price.amount),
		currency: values.price.currency,
		isPrimary: values.isPrimary,
		visibility: values.visibility || "private",
		hideStreetAddress: values.hideStreetAddress,
		neighborhood: values.neighborhood || "",
	};

	// Create location record - fix property names to match db schema
	const locationData: Omit<LocationType, "listingId"> = {
		// Change 'street' to 'address' to match the schema
		address: values.streetAddress,
		city: values.city,
		state: values.stateRegion,
		zipCode: values.zipCode,
		country: values.country,
	};

	// Create real estate details record
	const realEstateData: Omit<RealEstateDetailsType, "listingId"> = {
		propertyType: values.propertyType,
		bedrooms: String(values.bedrooms),
		bathrooms: String(values.bathrooms),
		homeSize: String(values.homeSize.value),
		homeSizeUnit: values.homeSize.unit,
		lotSize: String(values.lotSize.value),
		lotSizeUnit: values.lotSize.unit,
		parkingType: values.parkingType,
		parkingSpaces: String(values.parkingSpaces),
		yearBuilt: String(values.yearBuilt),
		historicalDesignation: values.historicalDesignation,
	};

	const mediaItems: Omit<MediaType, "listingId">[] = values.media.map(
		(item, index) => ({
			id: item.id,
			source: item.source,
			type: item.type,
			key: item.key,
			sortOrder: index, // Add sort order based on array index
		})
	);

	return {
		listing: listingData,
		location: locationData,
		realEstate: realEstateData,
		media: mediaItems,
	};
};

/**
 * Converts partial form values to database format for updates
 */
const formToDbFormatPartial = async (values: Partial<ListingFormValues>) => {
	const currentUser = await getCurrentUser();
	if (!currentUser) {
		throw new Error("User not authenticated");
	}

	// Create partial listings record - only include fields that are provided
	const listingData: Partial<ListingType> = {};

	if (values.propertyTitle !== undefined) {
		listingData.title = values.propertyTitle;
	}

	if (values.description !== undefined) {
		listingData.description = values.description || "";
	}

	if (values.amenities !== undefined) {
		listingData.amenities = values.amenities || [];
	}

	if (values.price?.amount !== undefined) {
		listingData.price = String(values.price.amount);
	}

	if (values.price?.currency !== undefined) {
		listingData.currency = values.price.currency;
	}

	if (values.isPrimary !== undefined) {
		listingData.isPrimary = values.isPrimary;
	}

	if (values.visibility !== undefined) {
		listingData.visibility = values.visibility || "private";
	}

	if (values.hideStreetAddress !== undefined) {
		listingData.hideStreetAddress = values.hideStreetAddress;
	}

	if (values.neighborhood !== undefined) {
		listingData.neighborhood = values.neighborhood || "";
	}

	// Create partial location record - only include fields that are provided
	const locationData: Partial<Omit<LocationType, "listingId">> = {};

	if (values.streetAddress !== undefined) {
		locationData.address = values.streetAddress;
	}

	if (values.city !== undefined) {
		locationData.city = values.city;
	}

	if (values.stateRegion !== undefined) {
		locationData.state = values.stateRegion;
	}

	if (values.zipCode !== undefined) {
		locationData.zipCode = values.zipCode;
	}

	if (values.country !== undefined) {
		locationData.country = values.country;
	}

	// Create partial real estate details record - only include fields that are provided
	const realEstateData: Partial<Omit<RealEstateDetailsType, "listingId">> = {};

	if (values.propertyType !== undefined) {
		realEstateData.propertyType = values.propertyType;
	}

	if (values.bedrooms !== undefined) {
		realEstateData.bedrooms = String(values.bedrooms);
	}

	if (values.bathrooms !== undefined) {
		realEstateData.bathrooms = String(values.bathrooms);
	}

	if (values.homeSize?.value !== undefined) {
		realEstateData.homeSize = String(values.homeSize.value);
	}

	if (values.homeSize?.unit !== undefined) {
		realEstateData.homeSizeUnit = values.homeSize.unit;
	}

	if (values.lotSize?.value !== undefined) {
		realEstateData.lotSize = String(values.lotSize.value);
	}

	if (values.lotSize?.unit !== undefined) {
		realEstateData.lotSizeUnit = values.lotSize.unit;
	}

	if (values.parkingType !== undefined) {
		realEstateData.parkingType = values.parkingType;
	}

	if (values.parkingSpaces !== undefined) {
		realEstateData.parkingSpaces = String(values.parkingSpaces);
	}

	if (values.yearBuilt !== undefined) {
		realEstateData.yearBuilt = String(values.yearBuilt);
	}

	if (values.historicalDesignation !== undefined) {
		realEstateData.historicalDesignation = values.historicalDesignation;
	}

	// Handle media items if provided
	const mediaItems: Omit<MediaType, "listingId">[] = values.media
		? values.media.map((item, index) => ({
				id: item.id,
				source: item.source,
				type: item.type,
				key: item.key,
				sortOrder: index, // Add sort order based on array index
			}))
		: [];

	return {
		listing: listingData,
		location: locationData,
		realEstate: realEstateData,
		media: mediaItems,
	};
};

const initVectorData = (options: {
	id: string;
	listing: ListingType;
	location: Omit<LocationType, "listingId">;
	realEstate: Omit<RealEstateDetailsType, "listingId">;
	reviewStatus?: ReviewStatusType;
}) => {
	const {
		id,
		listing,
		location,
		realEstate,
		reviewStatus = "drafts",
	} = options;

	return {
		id,
		title: listing.title,
		description: listing.description,
		listingType: listing.listingType,
		price: Number(listing.price),
		currency: listing.currency,
		amenities: listing.amenities,
		neighborhood: listing.neighborhood,
		visibility: listing.visibility,
		// Location fields
		address: location.address,
		city: location.city,
		state: location.state,
		country: location.country,
		zipCode: location.zipCode,
		schoolDistrict: location.schoolDistrict,
		// Real Estate Details fields
		propertyType: realEstate.propertyType,
		bedrooms: Number(realEstate.bedrooms),
		bathrooms: Number(realEstate.bathrooms),
		homeSize: Number(realEstate.homeSize),
		homeSizeUnit: realEstate.homeSizeUnit,
		lotSize: realEstate.lotSize ? Number(realEstate.lotSize) : undefined,
		lotSizeUnit: realEstate.lotSizeUnit,
		parkingType: realEstate.parkingType,
		parkingSpaces: realEstate.parkingSpaces
			? Number(realEstate.parkingSpaces)
			: undefined,
		yearBuilt: realEstate.yearBuilt ? Number(realEstate.yearBuilt) : undefined,
		historicalDesignation: realEstate.historicalDesignation,
		architectName: realEstate.architectName,
		interiorDesigner: realEstate.interiorDesigner,
		views: realEstate.views,
		historicDetails: realEstate.historicDetails,
		hoaFees: realEstate.hoaFees ? Number(realEstate.hoaFees) : undefined,
		taxes: realEstate.taxes ? Number(realEstate.taxes) : undefined,
		reviewStatus,
	} as ListingVectorData;
};

/**
 * Creates a new listing with all related data
 * Optimized for better performance with concurrent operations
 */
export const createListing = async (
	values: ListingFormValues,
	reviewStatus: ReviewStatusType = "drafts"
) => {
	try {
		const {
			listing,
			location,
			realEstate,
			media: mediaItems,
		} = await formToDbFormat(values);

		// Insert listing first (this must be done first to get the ID)
		const [newListing] = await db.insert(listings).values(listing).returning();

		// Prepare all dependent operations that can run concurrently
		// Use void functions to ensure consistent return types in Promise.all
		const concurrentOperations: Promise<void>[] = [
			// Create the associated listingReviewRequest
			toVoidPromise(
				upsertListingReviewRequest({
					userId: newListing.userId!,
					listingId: newListing.id,
					reviewStatus, // Pass the reviewStatus parameter
				})
			),

			// Insert location with the listing ID
			toVoidPromise(upsertLocation(newListing.id, location)),

			// Insert real estate details with the listing ID
			toVoidPromise(upsertRealEstateDetails(newListing.id, realEstate)),
		];

		// Add media sync operation only if there are media items
		if (mediaItems.length > 0) {
			concurrentOperations.push(syncListingMedia(newListing.id, mediaItems));
		}

		// Execute all dependent operations concurrently
		await Promise.all(concurrentOperations);

		// Execute vector update asynchronously to not block the response
		// since vector search is not critical for the immediate listing creation response
		const vectorData = initVectorData({
			id: newListing.id,
			listing,
			location,
			realEstate,
			reviewStatus,
		});
		upsertVector(newListing.id, vectorData).catch((error) => {
			console.error("Vector update failed during listing creation:", error);
		});

		return newListing;
	} catch (error) {
		console.error("Database error in createListing:", error);
		// Add specific handling for Neon connection errors
		if (error instanceof Error && error.message.includes("connection")) {
			throw new Error("Database connection error. Please try again.");
		}
		throw new Error(
			`Failed to create listing: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Gets a listing by ID with all related data including review status
 */
export const getListingById = async (id: string) => {
	try {
		const listing = await db.query.listings.findFirst({
			where: eq(listings.id, id),
			with: {
				location: true,
				realEstateDetails: true,
				media: {
					orderBy: (media, { asc }) => [asc(media.sortOrder)],
				},
				// user: true,
				listingReviewRequest: true, // Include the review request to get status
			},
		});

		return listing;
	} catch (error) {
		console.error("Database error in getListingById:", error);
		return null;
	}
};

/**
 * Gets a listing by slug with all related data including review status
 */
export const getListingBySlug = async (slug: string) => {
	try {
		const listing = await db.query.listings.findFirst({
			where: eq(listings.slug, slug),
			with: {
				location: true,
				realEstateDetails: true,
				media: {
					orderBy: (media, { asc }) => [asc(media.sortOrder)],
				},
				// user: true,
				listingReviewRequest: true, // Include the review request to get status
			},
		});

		return listing;
	} catch (error) {
		console.error("Database error in getListingBySlug:", error);
		return null;
	}
};

/**
 * Advanced function to get filtered listings with complete data and pagination
 * Supports comprehensive filtering including status, visibility, search, price, and real estate details
 * Returns both listings and pagination information in one call
 */
export const getFilteredListingsWithCompleteData = async (
	options: {
		status?: ReviewStatusType | ReviewStatusType[];
		visibility?: VisibilityType | VisibilityType[]; // Add visibility parameter
		userId?: string;
		reviewerType?: ReviewerTypeType;
		searchTerm?: string;
		minPrice?: number;
		maxPrice?: number;
		bedrooms?: number;
		bathrooms?: number;
		propertyType?: PropertyTypeType;
		location?: string[];
		amenities?: string[];
		sortBy?: SortOption;
		limit?: number;
		offset?: number;
		page?: number;
		pageSize?: number;
	} = {}
) => {
	const {
		status,
		visibility, // Add visibility destructuring
		userId,
		reviewerType,
		searchTerm,
		minPrice,
		maxPrice,
		bedrooms,
		bathrooms,
		propertyType,
		location,
		amenities,
		sortBy = "newest",
		limit,
		offset,
		page = 1,
		pageSize = 10,
	} = options;

	// Calculate offset from page and pageSize if not provided directly
	const finalOffset = offset !== undefined ? offset : (page - 1) * pageSize;
	const finalLimit = limit !== undefined ? limit : pageSize;

	// Step 1: Handle search term filtering first if provided
	let searchFilteredListingIds: string[] | null = null;
	if (searchTerm && searchTerm.trim().length > 0) {
		const searchData = buildSearchConditions(searchTerm);

		if (!searchData) {
			// If no valid search terms after parsing, return empty result
			return {
				listings: [],
				pagination: {
					total: 0,
					pageSize: finalLimit,
					currentPage: page,
					totalPages: 0,
				},
			};
		}

		// Search with DISTINCT to avoid duplicates and require ALL terms to match
		const searchResults = await db
			.selectDistinct({
				listingId: listings.id,
			})
			.from(listings)
			.leftJoin(locations, eq(listings.id, locations.listingId))
			.leftJoin(users, eq(listings.userId, users.id))
			.where(and(...searchData.conditions)); // Use AND to require all terms to match

		searchFilteredListingIds = searchResults.map((result) => result.listingId);

		// If no search results found, return empty result early
		if (searchFilteredListingIds.length === 0) {
			return {
				listings: [],
				pagination: {
					total: 0,
					pageSize: finalLimit,
					currentPage: page,
					totalPages: 0,
				},
			};
		}
	}

	// Step 2: Get total count and listing IDs with status/user/visibility filtering
	let totalCount = 0;
	let listingIds: string[] = [];

	if (status) {
		// Build conditions array with status condition (single or multiple)
		const conditions = [];

		if (Array.isArray(status)) {
			conditions.push(inArray(listingReviewRequests.reviewStatus, status));
		} else {
			conditions.push(eq(listingReviewRequests.reviewStatus, status));
		}

		// Add user filter if provided
		if (userId) {
			conditions.push(eq(listingReviewRequests.userId, userId));
		}

		// Add reviewerType filter if provided
		if (reviewerType) {
			conditions.push(eq(listingReviewRequests.reviewerType, reviewerType));
		}

		// Add visibility filter if provided
		if (visibility) {
			conditions.push(buildVisibilityFilter(visibility));
		}

		// Add search filter if provided
		if (searchFilteredListingIds) {
			conditions.push(inArray(listings.id, searchFilteredListingIds));
		}

		// Combine all conditions with AND
		const whereClause =
			conditions.length === 1 ? conditions[0] : and(...conditions);

		// Get total count using Drizzle's count method for better performance
		const [{ count: totalCountValue }] = await db
			.select({ count: count() })
			.from(listingReviewRequests)
			.leftJoin(listings, eq(listingReviewRequests.listingId, listings.id))
			.where(
				and(whereClause, eq(listings.id, listingReviewRequests.listingId))
			);

		totalCount = totalCountValue;

		// Build order by clause based on sortBy option
		let orderBy;
		switch (sortBy) {
			case "oldest":
				orderBy = [asc(listingReviewRequests.requestDate)];
				break;
			case "newest":
			default:
				orderBy = [desc(listingReviewRequests.requestDate)];
				break;
		}

		// Get paginated review requests with the criteria using select instead of findMany
		const reviewRequestsWithCriteria = await db
			.select({
				listingId: listingReviewRequests.listingId,
				requestDate: listingReviewRequests.requestDate,
				price: listings.price,
			})
			.from(listingReviewRequests)
			.leftJoin(listings, eq(listingReviewRequests.listingId, listings.id))
			.where(and(whereClause, eq(listings.id, listingReviewRequests.listingId)))
			.orderBy(...orderBy)
			.limit(finalLimit)
			.offset(finalOffset);

		// Extract listing IDs in the order returned by the database query
		listingIds = reviewRequestsWithCriteria
			.filter((item) => item.listingId)
			.map((item) => item.listingId);
	} else {
		// No status filter - get all listings with basic sorting and pagination
		let orderBy;
		switch (sortBy) {
			case "oldest":
				orderBy = [asc(listings.createdAt)];
				break;
			case "newest":
			default:
				orderBy = [desc(listings.createdAt)];
				break;
		}

		// Build conditions for filters
		const conditions = [];
		if (userId) {
			conditions.push(eq(listings.userId, userId));
		}
		if (visibility) {
			conditions.push(buildVisibilityFilter(visibility));
		}
		if (searchFilteredListingIds) {
			conditions.push(inArray(listings.id, searchFilteredListingIds));
		}

		const whereClause =
			conditions.length > 0
				? conditions.length === 1
					? conditions[0]
					: and(...conditions)
				: undefined;

		// Get total count using Drizzle's count method for better performance
		const [{ count: totalCountValue }] = await db
			.select({ count: count() })
			.from(listings)
			.where(whereClause);

		totalCount = totalCountValue;

		// Get paginated listings
		const allListings = await db.query.listings.findMany({
			where: whereClause,
			orderBy: orderBy,
			limit: finalLimit,
			offset: finalOffset,
			columns: { id: true },
		});

		listingIds = allListings.map((listing) => listing.id);
	}

	// Return empty result if no listing IDs found
	if (listingIds.length === 0) {
		return {
			listings: [],
			pagination: {
				total: totalCount,
				pageSize: finalLimit,
				currentPage: page,
				totalPages: Math.ceil(totalCount / finalLimit),
			},
		};
	}

	// Step 3: Apply real estate details filtering if any real estate filters are provided
	let filteredListingIds = listingIds;
	if (bedrooms || bathrooms || propertyType) {
		const realEstateFilteredIds = await getListingIdsByRealEstateFilters({
			bedrooms,
			bathrooms,
			propertyType,
		});

		// Get intersection of original listing IDs and real estate filtered IDs
		const realEstateFilterSet = new Set(realEstateFilteredIds);
		filteredListingIds = listingIds.filter((id) => realEstateFilterSet.has(id));

		// Adjust total count based on real estate filtering
		if (realEstateFilteredIds.length > 0) {
			// Estimate filtered total by proportion
			const filterRatio = filteredListingIds.length / listingIds.length;
			totalCount = Math.round(totalCount * filterRatio);
		} else {
			totalCount = 0;
		}

		// Return empty result if no listings match real estate criteria
		if (filteredListingIds.length === 0) {
			return {
				listings: [],
				pagination: {
					total: totalCount,
					pageSize: finalLimit,
					currentPage: page,
					totalPages: 0,
				},
			};
		}
	}

	// Step 4: Apply location filtering if provided
	if (location && location.length > 0) {
		// Get listing IDs that match location criteria
		const locationResults = await db
			.select({ listingId: locations.listingId })
			.from(locations)
			.where(
				or(
					...location.map((loc) =>
						or(
							ilike(locations.city, `%${loc}%`),
							ilike(locations.state, `%${loc}%`),
							ilike(locations.country, `%${loc}%`),
							ilike(locations.address, `%${loc}%`)
						)
					)
				)
			);

		const locationFilteredIds = locationResults.map(
			(result) => result.listingId
		);

		// Get intersection with current filtered listing IDs
		const locationFilterSet = new Set(locationFilteredIds);
		filteredListingIds = filteredListingIds.filter((id) =>
			locationFilterSet.has(id)
		);

		// Adjust total count based on location filtering
		if (locationFilteredIds.length > 0) {
			const filterRatio = filteredListingIds.length / listingIds.length;
			totalCount = Math.round(totalCount * filterRatio);
		} else {
			totalCount = 0;
		}

		// Return empty result if no listings match location criteria
		if (filteredListingIds.length === 0) {
			return {
				listings: [],
				pagination: {
					total: totalCount,
					pageSize: finalLimit,
					currentPage: page,
					totalPages: 0,
				},
			};
		}
	}

	// Step 5: Build final listing query conditions with price and amenities filters
	const conditions = [inArray(listings.id, filteredListingIds)];

	// Add price filters if provided
	if (minPrice) {
		conditions.push(gte(listings.price, String(minPrice)));
	}
	if (maxPrice) {
		conditions.push(lte(listings.price, String(maxPrice)));
	}

	// Add amenities filter if provided
	if (amenities && amenities.length > 0) {
		// Filter listings that contain ALL specified amenities (AND logic)
		// Each amenity must exist in the listing's amenities array
		amenities.forEach((amenity) => {
			conditions.push(
				sql`${listings.amenities} @> ARRAY[${amenity}]::varchar[]`
			);
		});
	}

	// Combine all conditions with AND
	const whereClause =
		conditions.length === 1 ? conditions[0] : and(...conditions);

	// Step 6: Get complete listings data with all relations
	const enrichedListings = await db.query.listings.findMany({
		where: whereClause,
		with: {
			location: true,
			realEstateDetails: true,
			media: {
				orderBy: (media, { asc }) => [asc(media.sortOrder)],
			},
			user: {
				columns: { id: true, userName: true },
			},
			listingReviewRequest: true,
		},
	});

	// Create a map for quick lookup by ID
	const listingMap = new Map(
		enrichedListings.map((listing) => [listing.id, listing])
	);

	// Return listings in the same order as the input listingIds array
	const finalListings = filteredListingIds
		.map((id) => listingMap.get(id))
		.filter((listing): listing is NonNullable<typeof listing> =>
			Boolean(listing)
		);

	// Adjust total count if price filters further reduced results
	if (finalListings.length < filteredListingIds.length) {
		const filterRatio = finalListings.length / filteredListingIds.length;
		totalCount = Math.round(totalCount * filterRatio);
	}

	return {
		listings: finalListings,
		pagination: {
			total: totalCount,
			pageSize: finalLimit,
			currentPage: page,
			totalPages: Math.ceil(totalCount / finalLimit),
		},
	};
};

/**
 * Simple function to enrich listings with complete data
 * Takes an array of listing IDs and returns them with all related data
 * Optimized for cases where you already have the listing IDs
 */
export const enrichListingsWithCompleteData = async (listingIds: string[]) => {
	// Return empty array if no listing IDs provided
	if (!listingIds || listingIds.length === 0) {
		return [];
	}

	// Get complete listings data with all relations using batch query
	const enrichedListings = await db.query.listings.findMany({
		where: inArray(listings.id, listingIds),
		with: {
			location: true,
			realEstateDetails: true,
			media: {
				orderBy: (media, { asc }) => [asc(media.sortOrder)],
			},
			user: {
				columns: {
					id: true,
					userName: true,
				},
			},
			listingReviewRequest: true,
		},
	});

	// Create a map for quick lookup by ID
	const listingMap = new Map(
		enrichedListings.map((listing) => [listing.id, listing])
	);

	// Return listings in the same order as the input listingIds array
	return listingIds
		.map((id) => listingMap.get(id))
		.filter((listing): listing is NonNullable<typeof listing> =>
			Boolean(listing)
		);
};

/**
 * Helper function to add isFavored status to listings
 * Optimized to avoid N+1 queries by batch-fetching favorites
 */
const addFavoredStatusToListings = async (listings: any[]) => {
	try {
		const currentUser = await getCurrentUser();

		if (!currentUser) {
			// If no user is logged in, all listings are not favored
			return listings.map((listing) => ({
				...listing,
				isFavored: false,
			}));
		}

		// Extract all listing IDs from the listings array
		const listingIds = listings.map((listing) => listing.id);

		// Batch-fetch all favorite listing IDs for the current user in one query
		const userFavorites = await db.query.favoritesListings.findMany({
			where: and(
				eq(favoritesListings.userId, currentUser.id),
				inArray(favoritesListings.listingId, listingIds)
			),
			columns: {
				listingId: true,
			},
		});

		// Create a Set for O(1) lookup performance
		const favoriteListingIds = new Set(
			userFavorites.map((fav) => fav.listingId)
		);

		// Map listings with isFavored status based on the Set lookup
		return listings.map((listing) => ({
			...listing,
			isFavored: favoriteListingIds.has(listing.id),
		}));
	} catch (error) {
		console.error("Error adding favored status:", error);
		// Return listings without favored status if there's an error
		return listings.map((listing) => ({
			...listing,
			isFavored: false,
		}));
	}
};

/**
 * Helper function to filter listings by review status
 * Extracted to avoid code duplication
 */
const filterListingsByStatus = (
	listings: any[],
	status: ReviewStatusType | ReviewStatusType[]
) => {
	return listings.filter((listing) => {
		if (listing && listing.listingReviewRequest) {
			const listingStatus = listing.listingReviewRequest.reviewStatus;
			if (Array.isArray(status)) {
				return status.includes(listingStatus);
			} else {
				return listingStatus === status;
			}
		}
		return false;
	});
};

/**
 * Helper function to get listings with review status using batch query
 * Optimized to reduce code duplication
 */
const getListingsWithReviewStatus = async (listingIds: string[]) => {
	if (!listingIds || listingIds.length === 0) {
		return [];
	}

	return await db.query.listings.findMany({
		where: inArray(listings.id, listingIds),
		with: {
			listingReviewRequest: true,
		},
	});
};

/**
 * Helper function to build visibility filter conditions
 * Handles both single visibility value and array of visibility values
 */
const buildVisibilityFilter = (
	visibility: VisibilityType | VisibilityType[]
) => {
	if (Array.isArray(visibility)) {
		return inArray(listings.visibility, visibility);
	} else {
		return eq(listings.visibility, visibility);
	}
};

/**
 * Helper function to filter listings by visibility (for manual filtering)
 * Used when filtering already fetched listings
 */
const filterListingsByVisibility = (
	listings: any[],
	visibility: VisibilityType | VisibilityType[]
) => {
	const visibilityArray = Array.isArray(visibility) ? visibility : [visibility];
	return listings.filter((listing) => {
		return listing && visibilityArray.includes(listing.visibility);
	});
};

/**
 * Gets all listings with pagination, filtered by review status and visibility
 * Updated to use the enhanced getFilteredListingsWithCompleteData function
 */
export const getAllListings = async (
	options: {
		page?: number;
		pageSize?: number;
		status?: ReviewStatusType | ReviewStatusType[];
		sortBy?: SortOption;
		reviewerType?: ReviewerTypeType;
		visibility?: VisibilityType | VisibilityType[];
	} = {}
) => {
	const {
		page = 1,
		pageSize = 10,
		status,
		sortBy = "newest",
		reviewerType,
		visibility,
	} = options;

	try {
		// Get complete data with pagination handled in getFilteredListingsWithCompleteData
		const result = await getFilteredListingsWithCompleteData({
			status,
			visibility, // Pass visibility parameter
			reviewerType,
			sortBy,
			page,
			pageSize,
		});

		// Add favored status to listings
		const listingsWithFavoredStatus = await addFavoredStatusToListings(
			result.listings
		);

		return {
			listings: listingsWithFavoredStatus,
			pagination: result.pagination,
		};
	} catch (error) {
		console.error("Database error in getAllListings:", error);
		return {
			listings: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

/**
 * Gets listings for the current user
 * Updated to use the enhanced getFilteredListingsWithCompleteData function
 */
export const getCurrentUserListings = async (
	options: {
		page?: number;
		pageSize?: number;
		status?: ReviewStatusType | ReviewStatusType[];
		sortBy?: SortOption;
		reviewerType?: ReviewerTypeType;
		visibility?: VisibilityType | VisibilityType[];
	} = {}
) => {
	const {
		page = 1,
		pageSize = 10,
		status,
		sortBy = "newest",
		reviewerType,
		visibility,
	} = options;

	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		// Get complete data with pagination handled in getFilteredListingsWithCompleteData
		const result = await getFilteredListingsWithCompleteData({
			status,
			visibility, // Pass visibility parameter
			userId: currentUser.id,
			reviewerType,
			sortBy,
			page,
			pageSize,
		});

		return {
			listings: result.listings,
			pagination: result.pagination,
		};
	} catch (error) {
		console.error("Database error in getCurrentUserListings:", error);
		return {
			listings: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

/**
 * Gets favorite listings for the current user with pagination
 * Updated to support sorting, visibility filtering and use the enhanced getFilteredListingsWithCompleteData function
 */
export const getCurrentUserFavoriteListings = async (
	options: {
		page?: number;
		pageSize?: number;
		status?: ReviewStatusType | ReviewStatusType[];
		sortBy?: SortOption;
		visibility?: VisibilityType | VisibilityType[];
	} = {}
) => {
	const {
		page = 1,
		pageSize = 10,
		status = "approved",
		sortBy = "newest",
		visibility,
	} = options;

	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		const offset = (page - 1) * pageSize;

		// Build order by clause for favorites based on sortBy option
		let favoritesOrderBy;
		switch (sortBy) {
			case "oldest":
				favoritesOrderBy = [asc(favoritesListings.createdAt)];
				break;
			case "newest":
			default:
				favoritesOrderBy = [desc(favoritesListings.createdAt)];
				break;
		}

		// Get user's favorites with pagination, ordered by the specified sort option
		const userFavorites = await db.query.favoritesListings.findMany({
			where: eq(favoritesListings.userId, currentUser.id),
			limit: pageSize * 3,
			offset: offset,
			orderBy: favoritesOrderBy,
			columns: {
				listingId: true,
			},
		});

		// Extract listing IDs
		const listingIds = userFavorites.map((fav) => fav.listingId);

		// If no favorites found, return empty result
		if (listingIds.length === 0) {
			return {
				listings: [],
				pagination: {
					total: 0,
					pageSize,
					currentPage: page,
					totalPages: 0,
				},
			};
		}

		// Get complete listing data with status and visibility filtering using enrichListingsWithCompleteData
		const enrichedListings = await enrichListingsWithCompleteData(listingIds);

		// Filter listings by status first
		let filteredListings = filterListingsByStatus(enrichedListings, status);

		// Filter listings by visibility if provided using the helper function
		if (visibility) {
			filteredListings = filterListingsByVisibility(
				filteredListings,
				visibility
			);
		}

		// Apply final pagination after filtering
		const paginatedListings = filteredListings.slice(0, pageSize);

		// Add isFavored property to each listing since they are all favorites
		const listingsWithFavoredStatus = paginatedListings.map((listing) => ({
			...listing,
			isFavored: true,
		}));

		// Get total count of user's favorites that match the status and visibility
		const allUserFavorites = await db.query.favoritesListings.findMany({
			where: eq(favoritesListings.userId, currentUser.id),
			columns: {
				listingId: true,
			},
		});

		const allFavoriteListingIds = allUserFavorites.map((fav) => fav.listingId);
		const allFavoriteListings = await getListingsWithReviewStatus(
			allFavoriteListingIds
		);

		// Apply both status and visibility filters to total count calculation
		let totalMatchingFavorites = filterListingsByStatus(
			allFavoriteListings,
			status
		);
		if (visibility) {
			totalMatchingFavorites = filterListingsByVisibility(
				totalMatchingFavorites,
				visibility
			);
		}

		return {
			listings: listingsWithFavoredStatus,
			pagination: {
				total: totalMatchingFavorites.length,
				pageSize,
				currentPage: page,
				totalPages: Math.ceil(totalMatchingFavorites.length / pageSize),
			},
		};
	} catch (error) {
		console.error("Database error in getCurrentUserFavoriteListings:", error);
		return {
			listings: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

/**
 * Updates an existing listing with partial data support
 * Optimized to reduce database queries for better performance
 */
export const updateListing = async (
	id: string,
	values: Partial<ListingFormValues>,
	reviewStatus?: ReviewStatusType
) => {
	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		// Use lightweight query for ownership check instead of full listing data
		const existingListing = await getListingForOwnershipCheck(id);

		if (!existingListing) {
			throw new Error("Listing not found");
		}

		// Check if current user is owner or admin
		if (
			existingListing.userId !== currentUser.id &&
			currentUser.role !== "admin"
		) {
			throw new Error("Not authorized to update this listing");
		}

		const {
			listing,
			location,
			realEstate,
			media: mediaItems,
		} = await formToDbFormatPartial(values);

		const userId =
			currentUser.role === "admin" ? existingListing.userId! : currentUser.id;

		// Batch all database operations for better performance
		// Use void functions to ensure consistent return types in Promise.all
		const updatePromises: Promise<void>[] = [];

		// Only update listing if there are fields to update
		if (Object.keys(listing).length > 0) {
			updatePromises.push(
				toVoidPromise(
					db
						.update(listings)
						.set({
							...listing,
							updatedAt: new Date(),
						})
						.where(eq(listings.id, id))
				)
			);
		}

		// Update the review status if provided
		if (reviewStatus) {
			updatePromises.push(
				toVoidPromise(
					upsertListingReviewRequest({
						userId: userId,
						listingId: id,
						reviewStatus,
					})
				)
			);
		}

		// Update location only if there are fields to update
		if (Object.keys(location).length > 0) {
			updatePromises.push(toVoidPromise(upsertLocation(id, location)));
		}

		// Update real estate details only if there are fields to update
		if (Object.keys(realEstate).length > 0) {
			updatePromises.push(
				toVoidPromise(upsertRealEstateDetails(id, realEstate))
			);
		}

		// Handle media updates only if media is provided
		if (values.media !== undefined) {
			updatePromises.push(syncListingMedia(id, mediaItems));
		}

		// Execute all updates concurrently
		await Promise.all(updatePromises);

		// Check if any fields that affect vector search were updated or if reviewStatus changed
		const hasVectorRelevantUpdates =
			Object.keys(listing).length > 0 ||
			Object.keys(location).length > 0 ||
			Object.keys(realEstate).length > 0 ||
			reviewStatus !== undefined;

		if (hasVectorRelevantUpdates) {
			// Use partial vector update for better performance
			const partialVectorData = initPartialVectorData({
				id,
				listingUpdates: listing,
				locationUpdates: location,
				realEstateUpdates: realEstate,
				reviewStatus,
			});

			// Execute vector update asynchronously to not block the response
			upsertVectorPartial(id, partialVectorData).catch((error) => {
				console.error("Partial vector update failed:", error);
			});
		}

		// Only fetch complete listing data at the end for return value
		const updatedListing = await getListingById(id);
		return updatedListing;
	} catch (error) {
		console.error("Database error in updateListing:", error);
		// Add specific handling for Neon connection errors
		if (error instanceof Error && error.message.includes("connection")) {
			throw new Error("Database connection error. Please try again.");
		}
		throw new Error(
			`Failed to update listing: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Deletes a listing and all related data
 */
export const deleteListing = async (id: string) => {
	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		// Get the existing listing to check ownership
		const existingListing = await db.query.listings.findFirst({
			where: eq(listings.id, id),
		});

		if (!existingListing) {
			throw new Error("Listing not found");
		}

		// Check if current user is owner or admin
		if (
			existingListing.userId !== currentUser.id &&
			currentUser.role !== "admin"
		) {
			throw new Error("Not authorized to delete this listing");
		}

		// Thanks to cascade delete in the schema, we only need to delete the listing
		await db.delete(listings).where(eq(listings.id, id));
		await deleteVector(id);
		return { success: true };
	} catch (error) {
		console.error("Database error in deleteListing:", error);
		throw new Error(
			`Failed to delete listing: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Increments the view count for a listing
 */
export const incrementViewCount = async (id: string) => {
	try {
		// Get current listing to get the current view count
		const listing = await db.query.listings.findFirst({
			where: eq(listings.id, id),
			columns: { viewCount: true },
		});

		if (!listing) {
			throw new Error("Listing not found");
		}

		// Increment the view count by 1
		const currentCount = listing.viewCount || 0;
		const newCount = currentCount + 1;

		// Update the view count in the database
		await db
			.update(listings)
			.set({ viewCount: newCount })
			.where(eq(listings.id, id));

		return { success: true, newViewCount: newCount };
	} catch (error) {
		console.error("Database error in incrementViewCount:", error);
		throw new Error(
			`Failed to update view count: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Updates the favorite count for a listing
 * @param id - The listing ID
 * @param increment - True to increment, false to decrement
 */
export const updateFavoriteCount = async (id: string, increment: boolean) => {
	try {
		// Get current listing to get the current favorite count
		const listing = await db.query.listings.findFirst({
			where: eq(listings.id, id),
			columns: { favoriteCount: true },
		});

		if (!listing) {
			throw new Error("Listing not found");
		}

		// Calculate the new count
		const currentCount = listing.favoriteCount || 0;
		const newCount = increment
			? currentCount + 1
			: Math.max(0, currentCount - 1);

		// Update the favorite count in the database
		await db
			.update(listings)
			.set({ favoriteCount: newCount })
			.where(eq(listings.id, id));

		return { success: true, newFavoriteCount: newCount };
	} catch (error) {
		console.error("Database error in updateFavoriteCount:", error);
		throw new Error(
			`Failed to update favorite count: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Searches listings based on review status and visibility
 * Updated to use the enhanced getFilteredListingsWithCompleteData function
 */
export const searchListings = async (options: {
	searchTerm?: string;
	page?: number;
	pageSize?: number;
	filters?: {
		minPrice?: number;
		maxPrice?: number;
		bedrooms?: number;
		bathrooms?: number;
		propertyType?: PropertyTypeType;
		location?: string[];
		amenities?: string[];
		status?: ReviewStatusType | ReviewStatusType[];
		visibility?: VisibilityType | VisibilityType[]; // Add visibility filter
		reviewerType?: ReviewerTypeType;
	};
	sortBy?: SortOption;
}) => {
	const {
		searchTerm,
		page = 1,
		pageSize = 10,
		filters = {},
		sortBy = "newest",
	} = options;

	try {
		const targetStatus = filters.status || "approved";

		// Get complete data with pagination handled in getFilteredListingsWithCompleteData
		const result = await getFilteredListingsWithCompleteData({
			status: targetStatus,
			visibility: filters.visibility,
			reviewerType: filters.reviewerType,
			searchTerm,
			minPrice: filters.minPrice,
			maxPrice: filters.maxPrice,
			bedrooms: filters.bedrooms,
			bathrooms: filters.bathrooms,
			propertyType: filters.propertyType,
			location: filters.location,
			amenities: filters.amenities,
			sortBy,
			page,
			pageSize,
		});
		return {
			listings: result.listings,
			pagination: result.pagination,
		};
	} catch (error) {
		console.error("Database error in searchListings:", error);
		return {
			listings: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

/**
 * Gets basic listing info for ownership/existence check (lightweight query)
 */
const getListingForOwnershipCheck = async (id: string) => {
	try {
		return await db.query.listings.findFirst({
			where: eq(listings.id, id),
			columns: {
				id: true,
				userId: true,
			},
		});
	} catch (error) {
		console.error("Database error in getListingForOwnershipCheck:", error);
		return null;
	}
};

/**
 * Helper function to parse search terms and build search conditions
 * Supports comma, space, and plus separators with AND logic
 * @param searchTerm - The raw search string from user input
 * @returns Object containing parsed terms and conditions, or null if no valid terms
 */
const buildSearchConditions = (searchTerm: string) => {
	if (!searchTerm || searchTerm.trim().length === 0) {
		return null;
	}

	// Parse search terms - support comma, space, and plus separators
	const searchTerms = searchTerm
		.split(/[,\s+]+/)
		.map((term) => term.trim())
		.filter((term) => term.length > 0);

	if (searchTerms.length === 0) {
		return null;
	}

	// Build search conditions for all terms (AND logic)
	// Each term must match at least one field
	const searchConditions = searchTerms.map((term) =>
		or(
			// Search in listings table
			ilike(listings.title, `%${term}%`),
			ilike(listings.id, `%${term}%`),

			// Search in locations table
			ilike(locations.address, `%${term}%`),
			ilike(locations.city, `%${term}%`),
			ilike(locations.state, `%${term}%`),
			ilike(locations.country, `%${term}%`),

			// Search in users table
			ilike(users.userName, `%${term}%`)
		)
	);

	return {
		terms: searchTerms,
		conditions: searchConditions,
	};
};

/**
 * Helper function to safely add field to vector data if it exists in updates
 * Handles null/empty string conversion to undefined for optional fields
 */
const addFieldToVectorData = <T>(
	vectorData: PartialListingVectorData,
	fieldName: keyof PartialListingVectorData,
	value: T | undefined,
	transform?: (val: T) => any
): void => {
	if (value !== undefined) {
		if (transform) {
			(vectorData as any)[fieldName] = transform(value);
		} else {
			// Convert empty strings to undefined for optional fields
			(vectorData as any)[fieldName] = value === "" ? undefined : value;
		}
	}
};

/**
 * Creates partial vector data from update objects
 * Optimized to reduce code duplication and improve maintainability
 */
const initPartialVectorData = (options: {
	id: string;
	listingUpdates: Partial<ListingType>;
	locationUpdates: Partial<Omit<LocationType, "listingId">>;
	realEstateUpdates: Partial<Omit<RealEstateDetailsType, "listingId">>;
	reviewStatus?: ReviewStatusType;
}): PartialListingVectorData => {
	const {
		id,
		listingUpdates,
		locationUpdates,
		realEstateUpdates,
		reviewStatus,
	} = options;
	const vectorData: PartialListingVectorData = { id };

	// Add reviewStatus to vector data if provided
	if (reviewStatus !== undefined) {
		vectorData.reviewStatus = reviewStatus;
	}

	// Define field mappings with proper typing for transform function
	type FieldMapping = {
		source: string;
		target: keyof PartialListingVectorData;
		transform?: (val: any) => any;
	};

	const listingFields: FieldMapping[] = [
		{ source: "title", target: "title" },
		{ source: "description", target: "description" },
		{ source: "listingType", target: "listingType" },
		{ source: "price", target: "price", transform: Number },
		{ source: "currency", target: "currency" },
		{ source: "amenities", target: "amenities" },
		{ source: "neighborhood", target: "neighborhood" },
		{ source: "visibility", target: "visibility" },
	];

	const locationFields: FieldMapping[] = [
		{ source: "address", target: "address" },
		{ source: "city", target: "city" },
		{ source: "state", target: "state" },
		{ source: "country", target: "country" },
		{ source: "zipCode", target: "zipCode" },
		{ source: "schoolDistrict", target: "schoolDistrict" },
	];

	const realEstateFields: FieldMapping[] = [
		{ source: "propertyType", target: "propertyType" },
		{ source: "bedrooms", target: "bedrooms", transform: Number },
		{ source: "bathrooms", target: "bathrooms", transform: Number },
		{ source: "homeSize", target: "homeSize", transform: Number },
		{ source: "homeSizeUnit", target: "homeSizeUnit" },
		{
			source: "lotSize",
			target: "lotSize",
			transform: (val: string) => (val ? Number(val) : undefined),
		},
		{ source: "lotSizeUnit", target: "lotSizeUnit" },
		{ source: "parkingType", target: "parkingType" },
		{
			source: "parkingSpaces",
			target: "parkingSpaces",
			transform: (val: string) => (val ? Number(val) : undefined),
		},
		{
			source: "yearBuilt",
			target: "yearBuilt",
			transform: (val: string) => (val ? Number(val) : undefined),
		},
		{ source: "historicalDesignation", target: "historicalDesignation" },
		{ source: "architectName", target: "architectName" },
		{ source: "interiorDesigner", target: "interiorDesigner" },
		{ source: "views", target: "views" },
		{ source: "historicDetails", target: "historicDetails" },
		{
			source: "hoaFees",
			target: "hoaFees",
			transform: (val: string) => (val ? Number(val) : undefined),
		},
		{
			source: "taxes",
			target: "taxes",
			transform: (val: string) => (val ? Number(val) : undefined),
		},
	];

	// Process listing fields
	listingFields.forEach(({ source, target, transform }) => {
		addFieldToVectorData(
			vectorData,
			target,
			(listingUpdates as any)[source],
			transform
		);
	});

	// Process location fields
	locationFields.forEach(({ source, target, transform }) => {
		addFieldToVectorData(
			vectorData,
			target,
			(locationUpdates as any)[source],
			transform
		);
	});

	// Process real estate fields
	realEstateFields.forEach(({ source, target, transform }) => {
		addFieldToVectorData(
			vectorData,
			target,
			(realEstateUpdates as any)[source],
			transform
		);
	});

	return vectorData;
};
