import { useState } from "react";
import { toast } from "sonner";
import { ListingData } from "@/types/listing";
import { ListingFormValues } from "@/lib/schemas/listing-schema";
import { ReviewStatusType } from "@/db/schema/enums";
import { mutate as globalMutate } from "swr";

export const useListingSubmit = () => {
	const [createDialogOpen, setCreateDialogOpen] = useState(false);
	// Edit dialog state
	const [editDialogOpen, setEditDialogOpen] = useState(false);
	const [editingListing, setEditingListing] = useState<ListingData | null>(
		null
	);

	// Handle edit button click
	const handleEdit = (listingId: string, listings: ListingData[]) => {
		const listing = listings.find((item) => item.id === listingId);
		if (listing) {
			setEditingListing(listing);
			setEditDialogOpen(true);
		}
	};

	const handleCreateSubmit = async (values: {
		data: ListingFormValues;
		reviewStatus: ReviewStatusType;
	}) => {
		if (editingListing) return;

		try {
			const payload = {
				data: values.data,
				reviewStatus: values.reviewStatus,
			};

			const response = await fetch(`/api/listings`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || "Failed to create listing");
			}

			await response.json();

			// Close dialog and show success message
			setCreateDialogOpen(false);

			toast.success("Listing created successfully", {
				description: "The listing has been created",
			});

			// Mutate all listings API cache to refresh data
			globalMutate(
				(key) => typeof key === "string" && key.startsWith("/api/listings")
			);
		} catch (error: any) {
			console.error("Error creating listing:", error);

			toast.error("Failed to create listing", {
				description: error?.message || "An unexpected error occurred",
			});
		}
	};

	// Handle form submission for editing
	const handleEditSubmit = async (values: {
		data: Partial<ListingFormValues>;
		reviewStatus: ReviewStatusType;
	}) => {
		if (!editingListing) return;

		try {
			// Get only the changed fields
			const changedFields = values.data;

			// If no changes detected, show message and return
			if (Object.keys(changedFields).length === 0) {
				toast.info("No changes detected", {
					description: "The listing data hasn't been modified",
				});
				return;
			}

			// Prepare payload with only changed data and review status
			const payload = {
				data: changedFields,
				reviewStatus: values.reviewStatus,
			};

			// console.log("Sending only changed fields:", payload);

			const response = await fetch(`/api/listings/${editingListing.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || "Failed to update listing");
			}

			await response.json();

			// Close dialog and show success message
			setEditDialogOpen(false);
			setEditingListing(null);
			toast.success("Listing updated successfully", {
				description: "The listing has been updated",
			});
		} catch (error: any) {
			console.error("Error updating listing:", error);

			toast.error("Failed to update listing", {
				description: error?.message || "An unexpected error occurred",
			});
		}
	};

	// Handle dialog close
	const handleDialogClose = () => {
		setEditDialogOpen(false);
		setEditingListing(null);
	};

	return {
		createDialogOpen,
		setCreateDialogOpen,
		editDialogOpen,
		setEditDialogOpen,
		editingListing,
		handleEdit,
		handleEditSubmit,
		handleCreateSubmit,
		handleDialogClose,
	};
};
