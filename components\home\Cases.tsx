"use client";
import CaseCard from "./CaseCard";
import CaseDialog from "../global/CaseDialog";
import { useListings } from "@/hooks/useListings";
import CasesSkeleton from "./CasesSkeleton";

const Cases = () => {
	// Use infinite scroll for listings
	const { listings, isLoading, error } = useListings({
		status: "approved",
		pageSize: 5,
	});

	// Handle initial loading state
	if (isLoading) {
		return <CasesSkeleton />;
	}

	// Handle error state
	if (error) {
		return (
			<div className="grid w-full grid-cols-2 px-4">
				<div className="col-span-2 text-center text-red-500">
					Error loading cases. Please try again later.
				</div>
			</div>
		);
	}

	return (
		<div className="grid w-full grid-cols-2 px-4 gap-x-4 gap-y-10">
			{listings.map((item, i) => (
				<CaseDialog key={item.id} data={item} ind={i} backPage={"home"}>
					<CaseCard data={item} />
				</CaseDialog>
			))}
		</div>
	);
};

export default Cases;
