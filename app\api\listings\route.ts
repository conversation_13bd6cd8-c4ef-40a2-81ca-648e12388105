import { NextRequest, NextResponse } from "next/server";
import {
	createListing,
	getAllListings,
	getCurrentUserListings,
	getCurrentUserFavoriteListings,
	searchListings,
	SortOption,
} from "@/db/services/listings.service";
import { auth } from "@clerk/nextjs/server";
import { listingSchema } from "@/lib/schemas/listing-schema";
import {
	reviewStatusEnum,
	reviewerTypeEnum,
	ReviewStatusType,
	ReviewerTypeType,
	VisibilityType,
	visibilityEnum,
} from "@/db/schema/enums";

/**
 * GET handler - Fetch listings
 */
export async function GET(req: NextRequest) {
	try {
		const { searchParams } = new URL(req.url);
		const page = parseInt(searchParams.get("page") || "1");
		const pageSize = parseInt(searchParams.get("pageSize") || "10");
		const statusParam = searchParams.get("status") || undefined;
		const sortBy = (searchParams.get("sortBy") || "newest") as SortOption;
		const reviewerTypeParam = searchParams.get("reviewerType") || undefined; // Add reviewerType parameter
		const visibilityParam = searchParams.get("visibility") || undefined; // Add visibility parameter

		// Handle multiple statuses separated by comma
		let status: ReviewStatusType | ReviewStatusType[] | undefined;
		if (statusParam) {
			const statusArray = statusParam.split(",") as ReviewStatusType[];
			// Validate that all statuses are valid review status values
			const validStatuses = statusArray.filter((s) =>
				reviewStatusEnum.enumValues.includes(s)
			);

			if (validStatuses.length > 0) {
				status = validStatuses.length === 1 ? validStatuses[0] : validStatuses;
			}
		}

		// Handle reviewerType parameter
		let reviewerType: ReviewerTypeType | undefined;
		if (
			reviewerTypeParam &&
			reviewerTypeEnum.enumValues.includes(
				reviewerTypeParam as ReviewerTypeType
			)
		) {
			reviewerType = reviewerTypeParam as ReviewerTypeType;
		}

		// Handle visibility parameter
		let visibility: VisibilityType | undefined;
		if (
			visibilityParam &&
			visibilityEnum.enumValues.includes(visibilityParam as VisibilityType)
		) {
			visibility = visibilityParam as VisibilityType;
		}

		const userOnly = searchParams.get("userOnly") === "true";
		const favoritesOnly = searchParams.get("favoritesOnly") === "true";
		const search = searchParams.get("search") || "";

		// Parse filters
		const filters: any = {};
		if (searchParams.has("minPrice"))
			filters.minPrice = parseFloat(searchParams.get("minPrice") || "0");
		if (searchParams.has("maxPrice"))
			filters.maxPrice = parseFloat(searchParams.get("maxPrice") || "0");
		if (searchParams.has("bedrooms"))
			filters.bedrooms = parseInt(searchParams.get("bedrooms") || "0");
		if (searchParams.has("bathrooms"))
			filters.bathrooms = parseInt(searchParams.get("bathrooms") || "0");
		if (searchParams.has("propertyType"))
			filters.propertyType = searchParams.get("propertyType");
		if (status) filters.status = status;
		if (reviewerType) filters.reviewerType = reviewerType;
		if (visibility) filters.visibility = visibility;

		// Check authentication for user-specific requests
		if (userOnly || favoritesOnly) {
			const { userId } = await auth();
			if (!userId) {
				return NextResponse.json(
					{ error: "Authentication required" },
					{ status: 401 }
				);
			}

			// Handle favorites only request (highest priority)
			if (favoritesOnly) {
				const result = await getCurrentUserFavoriteListings({
					page,
					pageSize,
					status,
					sortBy,
					visibility,
				});
				return NextResponse.json(result);
			}

			// Handle user only request (only when favoritesOnly is not set)
			if (userOnly) {
				const result = await getCurrentUserListings({
					page,
					pageSize,
					status,
					sortBy,
					reviewerType,
					visibility,
				});
				return NextResponse.json(result);
			}
		}

		// Handle search if search parameter is provided
		if (search) {
			const result = await searchListings({
				searchTerm: search,
				page,
				pageSize,
				filters,
				sortBy,
			});
			return NextResponse.json(result);
		}

		// Default to getting all listings
		const result = await getAllListings({
			page,
			pageSize,
			status,
			sortBy,
			reviewerType,
		});
		return NextResponse.json(result);
	} catch (error) {
		console.error("Error fetching listings:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}

/**
 * POST handler - Create a new listing
 * Optimized error handling to match POST request pattern
 */
export async function POST(req: NextRequest) {
	try {
		const { userId } = await auth();
		if (!userId) {
			return NextResponse.json(
				{ error: "Authentication required" },
				{ status: 401 }
			);
		}

		const { data, reviewStatus } = await req.json();

		// Validate the data
		const validationResult = listingSchema.safeParse(data);
		if (!validationResult.success) {
			return NextResponse.json(
				{ error: "Invalid data", details: validationResult.error.format() },
				{ status: 400 }
			);
		}

		const newListing = await createListing(validationResult.data, reviewStatus);
		return NextResponse.json(newListing, { status: 201 });
	} catch (error) {
		console.error("Error creating listing:", error);
		return NextResponse.json(
			{
				error: "Failed to create listing",
				message: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
