import { enrichListingsWithCompleteData } from "@/db/services/listings.service";
import { queryVector } from "@/lib/vectorHelpers";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
	try {
		// Extract query parameter from URL
		const { searchParams } = new URL(req.url);
		const query = searchParams.get("query");
		const visibility = searchParams.get("visibility");
		const reviewStatus = searchParams.get("reviewStatus");

		// Validate query parameter
		if (!query || query.trim() === "") {
			return NextResponse.json(
				{ error: "Query parameter is required" },
				{ status: 400 }
			);
		}

		// Execute vector search with visibility filter
		const { matchedIds, recommendedIds } = await queryVector(
			query,
			10,
			0.7,
			`visibility = "${visibility || "public"}" AND (reviewStatus = "${reviewStatus || "approved"}" OR HAS NOT FIELD reviewStatus)`
		);

		console.log(
			"Search results - Matched:",
			matchedIds.length,
			"Recommended:",
			recommendedIds.length
		);

		// Enrich results with complete data
		const matchedResults = await enrichListingsWithCompleteData(matchedIds);
		const recommendedResults =
			await enrichListingsWithCompleteData(recommendedIds);

		return NextResponse.json({ matchedResults, recommendedResults });
	} catch (error) {
		console.error("Search query error:", error);
		return NextResponse.json(
			{ error: "Internal server error occurred while processing search query" },
			{ status: 500 }
		);
	}
}
