CREATE TYPE "public"."historical_designation" AS ENUM('None', 'National Register', 'State Register', 'Local Landmark', 'Heritage Site', 'Other');--> statement-breakpoint
CREATE TYPE "public"."parking_type" AS ENUM('Garage', 'Carport', 'Street', 'Driveway', 'None', 'Other');--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "parking_type" "parking_type" DEFAULT 'None';--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "parking_spaces" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "historical_designation" "historical_designation" DEFAULT 'None';