import { create } from "zustand";
import { ListingData } from "@/types/listing";
interface SearchStore {
	searchValue: string;
	userSearch: string;
	results: ListingData[];
	loading: boolean;
	error: string | null;
	showResults: boolean;
	initialQuery: string;
	hasSearched: boolean;
	isTop: boolean;
	bodyScrollYPosition: number;
	isNewSearch: boolean;
	setBodyScrollYPosition: (value: number) => void;
	setSearchValue: (value: string) => void;
	setUserSearch: (value: string) => void;
	setResults: (results: ListingData[]) => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string | null) => void;
	setShowResults: (show: boolean) => void;
	setInitialQuery: (query: string) => void;
	setHasSearched: (hasSearched: boolean) => void;
	setIsTop: (isTop: boolean) => void;
	setIsNewSearch: (isNewSearch: boolean) => void;
	resetSearch: () => void;
	resetUserSearch: () => void;
	resetResults: () => void;
}

const useSearchStore = create<SearchStore>((set) => ({
	searchValue: "",
	userSearch: "",
	results: [],
	loading: false,
	error: null,
	showResults: false,
	initialQuery: "",
	hasSearched: false,
	isTop: true,
	bodyScrollYPosition: 0,
	isNewSearch: false,
	setSearchValue: (value: string) => set({ searchValue: value }),
	setUserSearch: (value: string) => set({ userSearch: value }),
	setResults: (results: ListingData[]) => set({ results }),
	setLoading: (loading: boolean) => set({ loading }),
	setError: (error: string | null) => set({ error }),
	setShowResults: (show: boolean) => set({ showResults: show }),
	setInitialQuery: (query: string) => set({ initialQuery: query }),
	setHasSearched: (hasSearched: boolean) => set({ hasSearched }),
	setIsTop: (isTop: boolean) => set({ isTop }),
	setBodyScrollYPosition: (bodyScrollYPosition: number) =>
		set({ bodyScrollYPosition }),
	setIsNewSearch: (isNewSearch: boolean) => set({ isNewSearch }),
	resetSearch: () => set({ 
		results: [],
		isNewSearch: false,
		hasSearched: false,
		searchValue: ""
	 }),
	resetUserSearch: () => set({ userSearch: "" }),
	resetResults: () =>
		set({
			results: [],
			loading: false,
			error: null,
			showResults: false,
			initialQuery: "",
			hasSearched: false,
			isTop: true,
			isNewSearch: false,
		}),
}));

export default useSearchStore;
