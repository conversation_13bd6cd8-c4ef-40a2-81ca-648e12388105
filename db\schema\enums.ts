import { pgEnum } from "drizzle-orm/pg-core";

// Define the role enum
export const roleEnum = pgEnum("role", [
	"admin",
	"buyer",
	"searcher",
	"advertiser",
	"concierge",
	"combination", // Added combo role for users that are both searcher and advertiser
]);

// Define the site access enum
export const siteAccessEnum = pgEnum("site_access", [
	"real_estate",
	"aviation",
	"yachts",
	"automobiles",
	"fine_art_collectibles",
]);

// Define the review status enum (for listing review requests) - this is the only status system now
export const reviewStatusEnum = pgEnum("review_status", [
	"approved",
	"pending",
	"drafts",
	"archived",
	"declined",
]);

// Define the two factor auth enum
export const twoFactorAuthEnum = pgEnum("two_factor_auth", [
	"disabled",
	"sms",
	"authenticator",
]);

export const reviewerTypeEnum = pgEnum("reviewer_type", [
	"advertiser",
	"admin",
]);

// Define the visibility enum for listings
export const visibilityEnum = pgEnum("visibility", [
	"private",
	"public", 
	"off-market",
]);

// Define the property type enum
export const propertyTypeEnum = pgEnum("property_type", [
	"house",
	"penthouse", 
	"apartment",
	"condo",
	"townhouse",
	"villa",
	"land",
	"commercial",
	"other"
]);

// Define the parking type enum
export const parkingTypeEnum = pgEnum("parking_type", [
	"Garage",
	"Carport",
	"Street",
	"Driveway",
	"None",
	"Other",
]);

// Define the historical designation enum
export const historicalDesignationEnum = pgEnum("historical_designation", [
	"None",
	"National Register",
	"State Register",
	"Local Landmark",
	"Heritage Site",
	"Other",
]);

// Export enum types for TypeScript usage
export type RoleType = (typeof roleEnum.enumValues)[number];
export type SiteAccessType = (typeof siteAccessEnum.enumValues)[number];
export type ReviewStatusType = (typeof reviewStatusEnum.enumValues)[number];
export type TwoFactorAuthType = (typeof twoFactorAuthEnum.enumValues)[number];
export type ReviewerTypeType = (typeof reviewerTypeEnum.enumValues)[number];
export type VisibilityType = (typeof visibilityEnum.enumValues)[number];
export type PropertyTypeType = (typeof propertyTypeEnum.enumValues)[number];
export type ParkingTypeType = (typeof parkingTypeEnum.enumValues)[number];
export type HistoricalDesignationType = (typeof historicalDesignationEnum.enumValues)[number];
