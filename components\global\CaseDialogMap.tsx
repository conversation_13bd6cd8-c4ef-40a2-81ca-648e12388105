"use client";
import { useState, useLayoutEffect, useRef, useEffect } from "react";
import mapboxgl from "mapbox-gl";

// You need to set your Mapbox access token
// You can store this in your env variables
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || "";

interface CaseDialogMapProps {
	address?: string;
	city?: string;
	state?: string;
	country?: string;
	hideStreetAddress?: boolean;
}

const CaseDialogMap = ({
	address,
	city,
	state,
	country,
	hideStreetAddress,
}: CaseDialogMapProps) => {
	const mapRef = useRef<HTMLDivElement>(null);
	const mapInstanceRef = useRef<mapboxgl.Map | null>(null);
	const [error, setError] = useState<string | null>(null);

	useLayoutEffect(() => {
		// Define initializeMap with an explicit return type
		const initializeMap = async () => {
			setError(null);

			try {
				// Build a search query from available address parts
				const searchParts = [
					...(address && !hideStreetAddress ? [address] : []),
					...(city ? [city] : []),
					...(state ? [state] : []),
					...(country ? [country] : []),
				];

				// If we don't have enough location data, show an error
				if (searchParts.length === 0) {
					setError("Not enough location information available");
					return;
				}

				const searchQuery = searchParts.join(", ");

				// Use Mapbox Geocoding API to get coordinates from address
				const response = await fetch(
					`https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
						searchQuery
					)}.json?access_token=${mapboxgl.accessToken}&limit=1`
				);

				if (!response.ok) throw new Error("Failed to geocode address");

				const data = await response.json();

				if (!data.features || data.features.length === 0) {
					throw new Error("Location not found");
				}

				const [longitude, latitude] = data.features[0].center;

				// Create map with improved options to minimize network activity
				const map = new mapboxgl.Map({
					container: mapRef.current!,
					style: "mapbox://styles/mapbox/streets-v11",
					center: [longitude, latitude],
					zoom: hideStreetAddress || !address ? 11 : 14,
					trackResize: false,
					attributionControl: false,
					interactive: true,
					fadeDuration: 0,
					refreshExpiredTiles: false,
					performanceMetricsCollection: false,
				});

				// Add marker and controls after map is fully loaded
				map.on("load", () => {
					const radiusInKm = 50; // Circle radius in kilometers

					if (hideStreetAddress) {
						// Add point data source for circle center
						map.addSource("circle-center", {
							type: "geojson",
							data: {
								type: "Point",
								coordinates: [longitude, latitude],
							},
						});

						// Add circle layer using circle paint properties
						map.addLayer({
							id: "circle-layer",
							type: "circle",
							source: "circle-center",
							paint: {
								"circle-radius": {
									stops: [
										[0, 0],
										[20, radiusInKm * 1000], // Convert km to meters for pixel radius
									],
									base: 2,
								},
								"circle-color": "#808080",
								"circle-opacity": 0.3,
								"circle-stroke-width": 0,
								"circle-stroke-color": "#ffffff",
							},
						});
					}

					// Add marker at the found location
					// ...existing code...
					// Add marker at the found location with custom SVG icon
					const markerElement = document.createElement("div");
					markerElement.className = "custom-marker";
					markerElement.style.width = "56px";
					markerElement.style.height = "56px";
					markerElement.style.backgroundSize = "contain";
					markerElement.style.backgroundRepeat = "no-repeat";
					markerElement.style.backgroundPosition = "center";
					markerElement.style.backgroundImage = 'url("/icons/MapHome.svg")';
					markerElement.style.cursor = "pointer";

					new mapboxgl.Marker({ element: markerElement })
						.setLngLat([longitude, latitude])
						.addTo(map);
					// ...existing code...

					// Add minimal controls only
					map.addControl(
						new mapboxgl.NavigationControl({
							showCompass: false,
							visualizePitch: false,
						}),
						"top-right"
					);
					// Store the map instance for cleanup
					mapInstanceRef.current = map;
				});
			} catch (err) {
				console.error("Error initializing map:", err);
				setError(
					`Failed to load map: ${err instanceof Error ? err.message : "Unknown error"}`
				);
			}
		};

		if (mapboxgl.supported() && mapRef.current && mapRef.current.isConnected) {
			initializeMap();
		}
	}, [address, city, state, country, hideStreetAddress]);

	useEffect(() => {
		return () => {
			// Check if map instance exists and has remove method before calling remove
			const map = mapInstanceRef.current;
			if (map && typeof map.remove === "function") {
				try {
					map.remove(); // Safe remove
				} catch (e) {
					// Catch and log any error to avoid breaking unmount
					console.warn("Mapbox map remove error:", e);
				}
				mapInstanceRef.current = null;
			}
		};
	}, []);

	if (error) {
		return <p className="py-4 text-center text-red-500">{error}</p>;
	}

	return (
		<div className="my-4 overflow-hidden border rounded-md">
			<div
				ref={mapRef}
				className="w-full h-[400px] relative"
				aria-label="Property location map"
			/>
		</div>
	);
};

export default CaseDialogMap;
