import { CardHeader } from "../../ui/card";
import { ListingData } from "@/types/listing";
import BaseListingCard from "./BaseListingCard";
import EditButton from "../../ui/EditButton";
import IdDisplay from "../../ui/IdDisplay";

interface DraftListingCardProps {
	item: ListingData;
	onEdit?: (listingId: string) => void;
}

const DraftListingCard = ({ item, onEdit }: DraftListingCardProps) => {
	const handleEditClick = () => {
		if (onEdit) {
			onEdit(item.id);
		}
	};

	const isDraft = item.listingReviewRequest?.reviewStatus === "drafts";

	return (
		<BaseListingCard item={item}>
			<CardHeader className="pt-2 pb-4">
				<div className="flex flex-row items-center justify-between">
					<IdDisplay
						id={item.id}
						className="text-lg font-normal text-neutral-500 font-manrope"
					/>
					<EditButton
						variant="default"
						onEdit={handleEditClick}
						showAlert={!isDraft}
						alertText="Needs review"
					/>
				</div>
			</CardHeader>
		</BaseListingCard>
	);
};

export default DraftListingCard;
