import { useChannelStateContext } from "stream-chat-react";
import { useState, useMemo } from "react";
import { useConnections } from "@/hooks/useConnections";
import { useCreateChatClient } from "stream-chat-react";
import useGetStreamToken from "@/hooks/useGetStreamToken";
import { useListingDetails } from "@/hooks/useListingDetails";
import { CustomChannelData } from "@/types/channel";
import CustomerHeaderSkeleton from "./CustomerHeaderSkeleton";
import PropertyInfo from "./PropertyInfo";
import ScheduleMeetingDialog from "./ScheduleMeetingDialog";
import { MeetingFormData } from "./MeetingForm";
import { UserSelectType } from "@/db/services/users.service";
import { deduplicateAndSortUsers } from "@/utils/userUtils";

type Attachment = {
	type: string;
	text: string;
	receiver: string;
	sender?: string;
	datetime?: Date;
	enddatetime?: Date;
	timezone: string;
	meetingTitle?: string;
	meetingId?: string;
};

const CustomerHeader = ({ me }: { me: UserSelectType }) => {
	const { channel } = useChannelStateContext();

	const { image, title, listingId } = channel.data as CustomChannelData;
	const [open, setOpen] = useState(false);

	const { connections } = useConnections({ userId: me.id });
	const { token } = useGetStreamToken(me?.id);
	const { isLoading, isError, listingDetails } = useListingDetails(listingId);

	const client = useCreateChatClient({
		apiKey: process.env.NEXT_PUBLIC_GETSTREAM_API_KEY!,
		tokenOrProvider: token,
		userData: {
			id: me?.id ?? "",
			name: me?.userName,
			image: me?.imageUrl || undefined,
		},
	});

	const scheduleMeeting = async (sendData: any) => {
		const response = await fetch(`/api/schedules`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(sendData),
		});

		if (!response.ok) {
			throw new Error("Failed to schedule meeting");
		}

		return await response.json();
	};

	const availableUsers = useMemo(() => {
		const members = channel?.state?.members;
		const memberList = Object.values(members || {}).map((m) => ({
			id: m.user?.id,
			name: m.user?.name,
		}));
		const otherMembers = memberList.filter((m) => m.id !== me.id);

		const connectionsUsers = connections.map((conn) => {
			const other = conn.buyerId === me.id ? conn.provider : conn.buyer;
			return {
				id: other.id,
				name: other.userName || other.id,
			};
		});

		// Use the utility function to deduplicate and sort users
		const allUsers = [...otherMembers, ...connectionsUsers];
		return deduplicateAndSortUsers(allUsers);
	}, [connections, me, channel?.state?.members]);

	const handleCalendarClick = () => setOpen(true);

	const handleSchedule = async (formData: MeetingFormData) => {
		const members = Object.values(channel.state.members || {});
		const meId = channel.getClient().userID;
		const receiver = members.find(
			(m: any) => (m as any).user_id !== meId
		) as any;
		const receiverUser = receiver?.user;
		const receiverName = receiverUser?.name || receiverUser?.id || "Unknown";

		// Create date objects with the selected date and times
		const startDateTime = formData.selectedDate;
		const [startHours, startMinutes] = formData.startTime
			.split(":")
			.map(Number);
		startDateTime.setHours(startHours, startMinutes);

		const endDateTime = new Date(formData.selectedDate);
		const [endHours, endMinutes] = formData.endTime.split(":").map(Number);
		endDateTime.setHours(endHours, endMinutes);

		// Updated to use recipients object with boolean values
		const sendData = {
			senderId: meId,
			recipients: formData.selectedAttendees.reduce(
				(obj, key) => ({ ...obj, [key]: false }), // Set all recipients to false (pending)
				{} as Record<string, boolean>
			),
			scheduleTime: startDateTime,
			listingId,
			callName: formData.meetingTitle,
			endTime: endDateTime,
			timezone: formData.timezone,
		};

		try {
			const scheduledMeeting = await scheduleMeeting(sendData);
			const meetingId = scheduledMeeting.id; // Get ID from API response
			console.log("Meeting scheduled successfully with ID:", meetingId);

			// Send messages to all selected attendees
			formData.selectedAttendees.map(async (receiverId) => {
				if (!client || !meId) return;

				const channel = client?.channel(
					"messaging",
					`${meId && meId.localeCompare(receiverId) > 0 ? `${meId}-${listingId}-${receiverId}` : `${receiverId}-${listingId}-${meId}`}`,
					{
						members: [meId ?? "", receiverId],
						image: image || "",
						title: title || "Untitled Property",
					} as CustomChannelData
				);

				if (channel) {
					try {
						await channel.create();
						await channel.sendMessage({
							text: `You've scheduled a meeting with ${receiverName} at ${startDateTime.toLocaleString()}`,
							type: "regular",
							attachments: [
								{
									type: "schedule-meeting",
									text: `${receiverName} has sent a schedule request at ${startDateTime.toLocaleString()}`,
									sender: meId || "",
									receiver: receiverId,
									datetime: startDateTime,
									enddatetime: endDateTime,
									timezone: formData.timezone,
									meetingTitle: formData.meetingTitle,
									meetingId: meetingId,
								} as Attachment,
							],
						});
					} catch (error) {
						console.error("Error creating channel or sending message:", error);
					}
				}
			});

			setOpen(false);
		} catch (error) {
			console.error("Error scheduling meeting:", error);
			// Optionally show error message to user
		}
	};

	if (isLoading) return <CustomerHeaderSkeleton />;
	if (isError) return <div>Error loading listing details.</div>;

	return (
		<>
			<PropertyInfo
				listingDetails={listingDetails}
				onCalendarClick={handleCalendarClick}
			/>
			<ScheduleMeetingDialog
				open={open}
				onOpenChange={setOpen}
				onSchedule={handleSchedule}
				availableUsers={availableUsers}
			/>
		</>
	);
};

export default CustomerHeader;
