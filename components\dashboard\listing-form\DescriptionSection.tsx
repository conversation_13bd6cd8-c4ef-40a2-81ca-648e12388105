"use client";
import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { TiptapEditor } from "@/components/ui/tiptap-editor";
import { ListingFormValues } from "@/lib/schemas/listing-schema";

type DescriptionSectionProps = {
	form: UseFormReturn<ListingFormValues>;
};

const DescriptionSection = ({ form }: DescriptionSectionProps) => {
	return (
		<div className="space-y-6">
			<FormField
				control={form.control}
				name="description"
				render={({ field }) => (
					<FormItem>
						<FormLabel className="text-3xl font-normal font-lora">
							Description
						</FormLabel>
						<FormControl>
							<TiptapEditor
								value={field.value}
								onChange={field.onChange}
								className="relative min-h-32"
								editable
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
};

export default DescriptionSection;
