import React from "react";
import {
	StreamVideoClient,
	Call,
	StreamVideo,
	StreamCall,
	StreamTheme,
	CallControls,
	SpeakerLayout,
} from "@stream-io/video-react-sdk";

interface CallVideoOverlayProps {
	streamCall: Call;
	client: StreamVideoClient;
	onLeave: () => void;
}

const CallVideoOverlay: React.FC<CallVideoOverlayProps> = ({
	streamCall,
	client,
	onLeave,
}) => {
	return (
		<div className="fixed top-0 left-0 z-50 flex flex-col items-center justify-center w-full h-full bg-gray-700">
			<StreamVideo client={client}>
				<StreamCall call={streamCall}>
					<StreamTheme>
						<SpeakerLayout />
						<CallControls onLeave={onLeave} />
					</StreamTheme>
				</StreamCall>
			</StreamVideo>
		</div>
	);
};

export default CallVideoOverlay;
