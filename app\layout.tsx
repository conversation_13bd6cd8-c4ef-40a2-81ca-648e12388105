import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"],
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"],
});

const manrope = Manrope({
	subsets: ["latin"],
	display: "swap",
	variable: "--font-manrope",
});

const lora = Lora({
	subsets: ["latin"],
	display: "swap",
	variable: "--font-lora",
});

const inter = Inter({
	subsets: ["latin"],
	display: "swap",
	variable: "--font-inter",
});

export const metadata: Metadata = {
	title: process.env.NEXT_PUBLIC_COMPANY_NAME!,
	description: "Generated by ZOOMLOOK LLC",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<ClerkProvider>
			<html lang="en" className="h-full">
				<body
					className={`${geistSans.variable} ${geistMono.variable} ${manrope.variable} ${lora.variable} ${inter.variable} antialiased h-full bg-[#F9F9F9]`}
				>
					{children}
					<Toaster
						richColors
						toastOptions={{
							duration: 6000,
						}}
					/>
				</body>
			</html>
		</ClerkProvider>
	);
}
