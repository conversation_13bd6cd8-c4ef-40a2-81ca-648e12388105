import React from "react";
import Image from "next/image";
import { ListingData } from "@/types/listing";

interface ListingPreviewProps {
	listing: ListingData;
	getSafeImageUrl: (imagePath?: string) => string | null;
}

const ListingPreview: React.FC<ListingPreviewProps> = ({
	listing,
	getSafeImageUrl,
}) => {
	return (
		<div className="flex flex-row w-full bg-[#EFEFF1] rounded-t-[11px] shadow-blue-100">
			<div className="w-[262px] self-stretch rounded-tl-[11px] overflow-hidden">
				{listing?.media?.[0]?.preview && (
					<Image
						src={getSafeImageUrl(listing.media[0].preview) || ""}
						alt="Listing preview"
						width={262}
						height={183}
						className="object-cover w-full h-full"
					/>
				)}
			</div>
			<div className="flex flex-col w-full gap-4 p-5 shadow-lg">
				<div className="flex flex-col gap-1">
					<div className="flex flex-row justify-between">
						<span className="text-base font-normal leading-6 font-manrope">
							{listing?.title || "N/A"}
						</span>
						<span className="font-manrope text-lg leading-7 text-[#727272] font-bold">
							ID#{listing?.id || "N/A"}
						</span>
					</div>
					<span className="text-2xl leading-7 font-lora">
						{listing?.location?.address || "N/A"}
					</span>
					<span className="text-xl font-bold leading-7 font-manrope">
						{listing?.price && listing?.currency
							? `${listing.price} ${listing.currency}`
							: "N/A"}
					</span>
				</div>
				<div className="flex flex-row justify-between">
					<div className="flex flex-row gap-4 leading-6 font-manrope font-base">
						<span>{listing?.realEstateDetails?.bedrooms || 0} bedrooms</span>
						<span>{listing?.realEstateDetails?.bathrooms || 0} bathrooms</span>
						<span>{listing?.realEstateDetails?.homeSize || 0} sqf</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ListingPreview;
