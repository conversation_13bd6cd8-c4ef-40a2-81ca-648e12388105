import React from "react";
import {
	AttachmentPreviewList,
	LinkPreviewList,
	QuotedMessagePreview,
	TextareaComposer,
} from "stream-chat-react";
import CustomAttachmentButton from "./CustomAttachmentButton";
import SendButtonWithCooldown from "./SendButtonWithCooldown";

const CustomMessageInput = () => (
	<div className="w-full message-input">
		<div className="w-full">
			<div className="central-container">
				<QuotedMessagePreview />
				<LinkPreviewList />
				<AttachmentPreviewList />
				<div className="w-full">
					<TextareaComposer
						placeholder="Type your message here"
						className="w-full resize-none h-[66px] border-none my-5 px-5 scrollable outline-none placeholder:text-[#4A4A4A] hover:border-none hover:outline-none focus:border-none focus:outline-none"
					/>
				</div>
			</div>
		</div>
		<div className="flex flex-row w-full left-container">
			<div className="w-[calc(100%-40px)]">
				<CustomAttachmentButton />
			</div>
			<div className="w-5">
				<SendButtonWithCooldown />
			</div>
		</div>
	</div>
);

export default CustomMessageInput;
