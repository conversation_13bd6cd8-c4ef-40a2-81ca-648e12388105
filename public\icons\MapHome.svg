<?xml version="1.0" encoding="UTF-8"?>
<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_d)">
    <circle cx="28" cy="28" r="24" fill="#151515"></circle>
    <circle cx="28" cy="28" r="23" stroke="white" stroke-width="2"></circle>
  </g>
  <path d="M37 37H30.0769V30.7692H25.9231V37H19V25.9231L28 19L37 25.9231V37Z" fill="white"></path>
  <defs>
    <filter id="filter0_d" x="0" y="0" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
      <feOffset></feOffset>
      <feGaussianBlur stdDeviation="2"></feGaussianBlur>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"></feColorMatrix>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"></feBlend>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feBlend>
    </filter>
  </defs>
</svg>
