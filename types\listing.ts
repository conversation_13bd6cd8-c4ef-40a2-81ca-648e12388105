import { type InferSelectModel } from "drizzle-orm";
import { type users } from "@/db/schema/users";
import { type listings } from "@/db/schema/listings";
import { type locations } from "@/db/schema/locations";
import { type realEstateDetails } from "@/db/schema/realEstateDetails";
import { type listingReviewRequests } from "@/db/schema/listingReviewRequests";

// Define composite type from schema types
export type User = InferSelectModel<typeof users>;
export type Listing = InferSelectModel<typeof listings>;
export type Location = InferSelectModel<typeof locations>;
export type RealEstateDetails = InferSelectModel<typeof realEstateDetails>;
export type ListingReviewRequest = InferSelectModel<
	typeof listingReviewRequests
>;

// Combined listing data interface using schema types
export interface ListingData extends Listing {
	location: Location; // Make all properties of location optional
	realEstateDetails: RealEstateDetails;
	media: any[];
	amenities: string[];
	user?: User;
	listingReviewRequest?: ListingReviewRequest;
	isFavored?: boolean;
}

export interface ListingPartialData extends Partial<Listing> {
	location?: Partial<Location>; // Make location itself optional
	realEstateDetails?: Partial<RealEstateDetails>; // Make realEstateDetails itself optional
	media?: any[];
	amenities?: string[];
	user?: User;
	listingReviewRequest?: ListingReviewRequest;
	isFavored?: boolean;
}
