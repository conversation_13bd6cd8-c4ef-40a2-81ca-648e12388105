"use client";
import { useState } from "react";
import AddNewAccount from "@/components/user-management/AddNewAccount";
import UserManagementLabel from "@/components/user-management/UserManagementLabel";
import { UserSearcher } from "@/components/user-management/UserSearcher";
import UsersTable from "@/components/user-management/UsersTable";
import { RoleType } from "@/db/schema/enums";
import {
	USER_MANAGEMENT_PATH_MAP,
	UserManagementPathMapKey,
} from "@/constant/const";

interface UserManagementClientProps {
	slug: UserManagementPathMapKey;
}

const UserManagementClient = ({ slug }: UserManagementClientProps) => {
	const [mutateUsers, setMutateUsers] = useState<(() => void) | null>(null);

	const handleMutateReady = (mutate: () => void) => {
		setMutateUsers(() => mutate);
	};

	return (
		<div className="flex flex-col h-full gap-8 scrollable ">
			<UserManagementLabel slug={slug} />
			<div className="flex flex-row items-center justify-between gap-8 p-2">
				<UserSearcher />
				<AddNewAccount
					role={USER_MANAGEMENT_PATH_MAP[slug].role as RoleType}
					onAccountCreated={mutateUsers}
				/>
			</div>
			<UsersTable
				roleFilter={USER_MANAGEMENT_PATH_MAP[slug].role as RoleType}
				onMutateReady={handleMutateReady}
			/>
		</div>
	);
};

export default UserManagementClient;
