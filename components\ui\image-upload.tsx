"use client";

import { useState, useRef, use<PERSON>allback, useEffect, useMemo } from "react";
import { Progress } from "@/components/ui/progress";
import { X, Upload, Move, Trash2, Eye } from "lucide-react";
import Image from "next/image";
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
	DragEndEvent,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
	rectSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import React from "react";

// Interfaces for component props and state
export interface ImageFile {
	id: string;
	file?: File;
	type?: string;
	sortOrder?: number; // Optional sort order for media items
	preview: string;
	progress: number;
	uploaded: boolean;
	key?: string; // S3 object key after upload
	url?: string; // Public URL or signed URL
}

export interface ImageUploadProps {
	// Function to upload file and return a promise with the URL and object key
	uploadFile?: (
		file: File,
		onProgress?: (progress: number) => void
	) => Promise<{ url: string; key: string }>;
	// Function to delete file from S3 by key
	deleteFile?: (key: string) => Promise<void>;
	// Initial images if needed
	initialImages?: ImageFile[];
	// Maximum number of images allowed
	maxImages?: number;
	// Callback when images change
	onChange?: (images: ImageFile[]) => void;
}

// Optimized preview creation using createObjectURL for better performance
const createOptimizedPreview = (file: File): string => {
	// For better performance, use createObjectURL instead of FileReader for previews
	// This is much faster and doesn't require async processing
	return URL.createObjectURL(file);
};

// Compress image if needed (only for large images)
const compressImageIfNeeded = async (
	file: File,
	maxSize = 1024
): Promise<File> => {
	// Skip compression for small files or non-images
	if (!file.type.startsWith("image/") || file.size <= 1024 * 1024) {
		return file;
	}

	return new Promise((resolve) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);

		reader.onload = (event) => {
			const img = new window.Image();
			img.src = event.target?.result as string;

			img.onload = () => {
				const canvas = document.createElement("canvas");
				let width = img.width;
				let height = img.height;

				// Calculate new dimensions while maintaining aspect ratio
				if (width > height) {
					if (width > maxSize) {
						height = Math.round((height * maxSize) / width);
						width = maxSize;
					}
				} else {
					if (height > maxSize) {
						width = Math.round((width * maxSize) / height);
						height = maxSize;
					}
				}

				canvas.width = width;
				canvas.height = height;

				const ctx = canvas.getContext("2d");
				ctx?.drawImage(img, 0, 0, width, height);

				// Convert to blob and create new file
				canvas.toBlob(
					(blob) => {
						if (blob) {
							const compressedFile = new File([blob], file.name, {
								type: "image/jpeg",
								lastModified: Date.now(),
							});
							resolve(compressedFile);
						} else {
							resolve(file);
						}
					},
					"image/jpeg",
					0.7
				);
			};
		};
	});
};

// Memoized sortable image component for better performance
const SortableImage = React.memo(
	({
		image,
		onRemove,
	}: {
		image: ImageFile;
		onRemove: (id: string) => void;
	}) => {
		const { attributes, listeners, setNodeRef, transform, transition } =
			useSortable({ id: image.id });

		const style = {
			transform: CSS.Transform.toString(transform),
			transition,
		};

		// State for enlarged preview
		const [showEnlarged, setShowEnlarged] = useState(false);

		// Memoized handlers to prevent unnecessary re-renders
		const handlePreviewClick = useCallback((e: React.MouseEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setShowEnlarged(true);
		}, []);

		const handleClosePreview = useCallback((e: React.MouseEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setShowEnlarged(false);
		}, []);

		const handleRemove = useCallback(() => {
			onRemove(image.id);
		}, [onRemove, image.id]);

		return (
			<div
				ref={setNodeRef}
				style={style}
				{...attributes}
				className="relative overflow-hidden border rounded-md aspect-square group"
			>
				{image.uploaded ? (
					<Image
						src={image.preview}
						alt="Uploaded"
						fill
						sizes="(max-width: 768px) 100vw, 200px"
						className="object-cover"
					/>
				) : (
					<div className="flex flex-col items-center justify-center h-full p-4">
						<Progress value={image.progress} className="w-full mb-2" />
						<span className="text-sm text-gray-500">{image.progress}%</span>
					</div>
				)}

				{/* Center control panel on hover */}
				{image.uploaded && (
					<div className="absolute inset-0 flex items-center justify-center transition-opacity opacity-0 group-hover:opacity-100 bg-black/40">
						<div className="flex gap-2">
							<button
								type="button"
								className="p-2 text-gray-700 bg-white rounded-full hover:text-blue-600"
								onClick={handlePreviewClick}
							>
								<Eye className="w-4 h-4" />
							</button>
							<button
								type="button"
								className="p-2 text-gray-700 bg-white rounded-full hover:text-red-600"
								onClick={handleRemove}
							>
								<Trash2 className="w-4 h-4" />
							</button>
							<button
								type="button"
								className="p-2 text-gray-700 bg-white rounded-full cursor-grab hover:text-yellow-600"
								{...listeners}
							>
								<Move className="w-4 h-4" />
							</button>
						</div>
					</div>
				)}

				{/* Enlarged preview overlay */}
				{showEnlarged && (
					<div
						className="fixed inset-0 z-50 flex items-center justify-center bg-white"
						onClick={handleClosePreview}
						onMouseDown={(e) => e.stopPropagation()}
					>
						<div className="relative w-full h-full m-8">
							<Image
								src={image.preview}
								alt="Preview"
								fill
								sizes="(max-width: 1280px) 100vw, 80vw"
								className="object-contain"
							/>
						</div>
						<button
							type="button"
							className="absolute p-2 bg-white rounded-full top-1 right-1"
							onClick={handleClosePreview}
						>
							<X className="w-6 h-6" />
						</button>
					</div>
				)}
			</div>
		);
	}
);

SortableImage.displayName = "SortableImage";

export function ImageUpload({
	uploadFile,
	deleteFile,
	initialImages = [],
	maxImages = 20,
	onChange,
}: ImageUploadProps) {
	const [images, setImages] = useState<ImageFile[]>(initialImages);
	const fileInputRef = useRef<HTMLInputElement>(null);

	// Track object URLs for cleanup
	const objectUrlsRef = useRef<Set<string>>(new Set());

	// Memoized onChange callback to prevent unnecessary re-renders
	const handleOnChange = useCallback(
		(updatedImages: ImageFile[]) => {
			onChange?.(updatedImages);
		},
		[onChange]
	);

	// Effect to notify parent component when images change
	useEffect(() => {
		handleOnChange(images);
	}, [images, handleOnChange]);

	// Setup DnD sensors with memoization
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 5,
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	// Memoized drag end handler
	const handleDragEnd = useCallback((event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			setImages((items) => {
				const oldIndex = items.findIndex((item) => item.id === active.id);
				const newIndex = items.findIndex((item) => item.id === over.id);
				return arrayMove(items, oldIndex, newIndex);
			});
		}
	}, []);

	// Memoized click handler
	const handleClick = useCallback(() => {
		if (images.length >= maxImages) {
			alert(`Maximum of ${maxImages} images allowed`);
			return;
		}
		fileInputRef.current?.click();
	}, [images.length, maxImages]);

	// Function to upload image to S3 using the provided uploadFile function
	const uploadImageToS3 = useCallback(
		async (image: ImageFile) => {
			if (!uploadFile || !image.file) {
				console.warn("No uploadFile function provided or file missing");
				return;
			}

			try {
				// Upload to S3 using the provided function
				const { url, key } = await uploadFile(image.file, (progress) => {
					setImages((prev) =>
						prev.map((img) =>
							img.id === image.id ? { ...img, progress } : img
						)
					);
				});

				// Update image with upload result
				setImages((prev) =>
					prev.map((img) =>
						img.id === image.id
							? {
									...img,
									progress: 100,
									uploaded: true,
									key,
									url,
								}
							: img
					)
				);
			} catch (error) {
				console.error("Error uploading image:", error);
				setImages((prev) =>
					prev.map((img) =>
						img.id === image.id ? { ...img, progress: 0 } : img
					)
				);
				alert(`Failed to upload image: ${image.file.name}`);
			}
		},
		[uploadFile]
	);

	// Optimized batch upload with better concurrency control
	const uploadImagesWithConcurrency = useCallback(
		async (imagesToUpload: ImageFile[], concurrencyLimit: number = 3) => {
			const uploadQueue = [...imagesToUpload];
			const uploadPromises: Promise<void>[] = [];

			// Create worker function for uploading
			const uploadWorker = async () => {
				while (uploadQueue.length > 0) {
					const image = uploadQueue.shift();
					if (image) {
						await uploadImageToS3(image);
					}
				}
			};

			// Start concurrent workers
			for (
				let i = 0;
				i < Math.min(concurrencyLimit, imagesToUpload.length);
				i++
			) {
				uploadPromises.push(uploadWorker());
			}

			await Promise.all(uploadPromises);
		},
		[uploadImageToS3]
	);

	const handleFileChange = useCallback(
		async (e: React.ChangeEvent<HTMLInputElement>) => {
			const files = e.target.files;
			if (!files || files.length === 0) return;

			// Check if adding these files would exceed the max
			if (images.length + files.length > maxImages) {
				alert(`You can only upload ${maxImages} images in total`);
				e.target.value = "";
				return;
			}

			const fileArray = Array.from(files);

			// Create immediate placeholder images and add to state first
			const newImagesPlaceholders = fileArray.map((file) => ({
				id: crypto.randomUUID(),
				file,
				preview: "", // Empty preview initially
				progress: 0,
				uploaded: false,
			}));

			// Add placeholder images to state immediately for better UX
			setImages((prev) => [...prev, ...newImagesPlaceholders]);

			// Process files asynchronously and update previews
			const processImagePromises = newImagesPlaceholders.map(
				async (placeholderImage) => {
					try {
						// Compress file if needed
						const processedFile = await compressImageIfNeeded(
							placeholderImage.file
						);
						const preview = createOptimizedPreview(processedFile);

						// Track object URL for cleanup
						objectUrlsRef.current.add(preview);

						// Update the specific image with preview and processed file
						setImages((prev) =>
							prev.map((img) =>
								img.id === placeholderImage.id
									? {
											...img,
											preview,
										}
									: img
							)
						);

						return {
							...placeholderImage,
							preview,
						};
					} catch (error) {
						console.error("Error processing image:", error);
						// Remove failed image from state
						setImages((prev) =>
							prev.filter((img) => img.id !== placeholderImage.id)
						);
						return null;
					}
				}
			);

			// Wait for all images to be processed, then start upload
			const processedImages = await Promise.all(processImagePromises);
			const validImages = processedImages.filter(Boolean) as ImageFile[];

			// Start upload process for successfully processed images
			if (validImages.length > 0) {
				uploadImagesWithConcurrency(validImages);
			}

			// Reset input
			e.target.value = "";
		},
		[images.length, maxImages, uploadImagesWithConcurrency]
	);

	// Optimized remove image function
	const removeImage = useCallback(
		async (id: string) => {
			const imageToRemove = images.find((img) => img.id === id);

			// Remove from state first for responsive UI
			setImages((prev) => prev.filter((img) => img.id !== id));

			// Cleanup object URL immediately
			if (imageToRemove?.preview) {
				if (objectUrlsRef.current.has(imageToRemove.preview)) {
					URL.revokeObjectURL(imageToRemove.preview);
					objectUrlsRef.current.delete(imageToRemove.preview);
				}
			}

			// Delete from S3 if needed (non-blocking)
			if (imageToRemove?.key && deleteFile) {
				try {
					await deleteFile(imageToRemove.key);
				} catch (error) {
					console.error("Error deleting image from S3:", error);
				}
			}
		},
		[images, deleteFile]
	);

	// Memoize sortable items for better performance
	const sortableItems = useMemo(
		() => images.map((image) => image.id),
		[images]
	);

	// Cleanup function - improved memory management
	useEffect(() => {
		// Copy the current ref value to a local variable
		const currentObjectUrls = objectUrlsRef.current;

		return () => {
			// Use the local variable in cleanup function
			currentObjectUrls.forEach((url) => {
				URL.revokeObjectURL(url);
			});
			currentObjectUrls.clear();
		};
	}, []);

	return (
		<div className="w-full">
			<DndContext
				sensors={sensors}
				collisionDetection={closestCenter}
				onDragEnd={handleDragEnd}
			>
				<SortableContext items={sortableItems} strategy={rectSortingStrategy}>
					<div className="flex flex-wrap gap-4">
						{images.map((image) => (
							<div
								key={image.id}
								className="w-[150px] sm:w-[180px] md:w-[200px] flex-grow-0 flex-shrink-0"
							>
								<SortableImage image={image} onRemove={removeImage} />
							</div>
						))}

						{images.length < maxImages && (
							<div className="w-[150px] sm:w-[180px] md:w-[200px] flex-grow-0 flex-shrink-0">
								<div
									onClick={handleClick}
									className="flex items-center justify-center h-full transition-colors border-2 border-dashed rounded-md cursor-pointer aspect-square hover:bg-gray-50"
								>
									<div className="flex flex-col items-center gap-2">
										<Upload className="w-6 h-6 text-gray-400" />
										<span className="text-sm text-gray-500">Upload</span>
									</div>
									<input
										type="file"
										ref={fileInputRef}
										onChange={handleFileChange}
										multiple
										accept="image/*"
										className="hidden"
									/>
								</div>
							</div>
						)}
					</div>
				</SortableContext>
			</DndContext>
		</div>
	);
}
