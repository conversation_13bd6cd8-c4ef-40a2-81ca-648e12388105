import { useState, useEffect, MouseE<PERSON> } from "react";
import use<PERSON><PERSON> from "swr";
import { fetcher } from "@/lib/utils";
import { UserSelectType } from "@/db/services/users.service";
import { RoleType } from "@/db/schema/enums";

interface PaginationType {
	total: number;
	pageSize: number;
	currentPage: number;
	totalPages: number;
}

interface UseUserSearchResult {
	users: UserSelectType[];
	pagination: PaginationType;
	isLoading: boolean;
	error: any;
	searchTerm: string;
	setSearchTerm: (term: string) => void;
	currentPage: number;
	setCurrentPage: (page: number) => void;
	getPageNumbers: () => number[];
	handlePrevious: (e: MouseEvent<HTMLAnchorElement>) => void;
	handleNext: (e: MouseEvent<HTMLAnchorElement>) => void;
	handlePageClick: (e: MouseEvent<HTMLAnchorElement>, page: number) => void;
	mutate: () => Promise<any>; // Add mutate function to the return type
}

interface UseUserSearchParams {
	roleFilter?: RoleType | RoleType[];
	pageSize?: number;
	externalSearchTerm?: string;
	excludeUserId?: string; // Add parameter to exclude specific user
}

/**
 * Custom hook for user search functionality with pagination
 * @param params Object containing search parameters
 * @param params.roleFilter User role filter - can be a single role or array of roles (optional)
 * @param params.pageSize Number of items per page (default: 10)
 * @param params.externalSearchTerm Optional external search term (for controlled search from parent component)
 * @param params.excludeUserId Optional user ID to exclude from results (typically current user)
 * @returns Object with users data, pagination controls and search functionality
 */
export function useUserSearch({
	roleFilter,
	pageSize = 10,
	externalSearchTerm,
	excludeUserId,
}: UseUserSearchParams = {}): UseUserSearchResult {
	// If external search term is provided, use it, otherwise use internal state
	const [internalSearchTerm, setInternalSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);

	// Determine which search term to use - external or internal
	const searchTerm =
		externalSearchTerm !== undefined ? externalSearchTerm : internalSearchTerm;

	// Create URL with query parameters
	const roleParam = Array.isArray(roleFilter)
		? roleFilter.map((r) => `roleFilter=${r}`).join("&")
		: roleFilter
			? `roleFilter=${roleFilter}`
			: "";

	const excludeParam = excludeUserId ? `&excludeUserId=${excludeUserId}` : "";

	const apiUrl = `/api/users?search=${encodeURIComponent(
		searchTerm || ""
	)}&page=${currentPage}&pageSize=${pageSize}${
		roleParam ? `&${roleParam}` : ""
	}${excludeParam}`;

	// Use SWR to fetch data
	const { data, error, isLoading, mutate } = useSWR(apiUrl, fetcher, {
		revalidateOnFocus: false,
		revalidateOnReconnect: true,
		refreshInterval: 600000, // Refresh every 10 minutes
		keepPreviousData: true,
	});



	// Extract users and pagination from data
	const users = data?.users || [];
	const pagination = data?.pagination || {
		total: 0,
		pageSize: 10,
		currentPage: 1,
		totalPages: 0,
	};

	// Set search term with proper handling for external/internal state
	const setSearchTerm = (term: string) => {
		if (externalSearchTerm === undefined) {
			setInternalSearchTerm(term);
		}
		// If external search term is provided, this function becomes a no-op
		// since the parent component controls the search term
	};

	// Reset to first page when search term changes
	useEffect(() => {
		const timer = setTimeout(() => {
			setCurrentPage(1);
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Pagination helpers
	const getPageNumbers = () => {
		const { currentPage, totalPages } = pagination;
		const pageNumbers = [];

		// Always include first page
		pageNumbers.push(1);

		// Add pages around current page
		for (
			let i = Math.max(2, currentPage - 1);
			i <= Math.min(totalPages - 1, currentPage + 1);
			i++
		) {
			pageNumbers.push(i);
		}

		// Always include last page if more than one page
		if (totalPages > 1) {
			pageNumbers.push(totalPages);
		}

		// Make sure array is unique and sorted
		return [...new Set(pageNumbers)].sort((a, b) => a - b);
	};

	// Event handlers
	const handlePrevious = (e: MouseEvent<HTMLAnchorElement>) => {
		e.preventDefault();
		if (pagination.currentPage > 1) {
			setCurrentPage((prev) => prev - 1);
		}
	};

	const handleNext = (e: MouseEvent<HTMLAnchorElement>) => {
		e.preventDefault();
		if (pagination.currentPage < pagination.totalPages) {
			setCurrentPage((prev) => prev + 1);
		}
	};

	const handlePageClick = (e: MouseEvent<HTMLAnchorElement>, page: number) => {
		e.preventDefault();
		if (page !== pagination.currentPage) {
			setCurrentPage(page);
		}
	};

	return {
		users,
		pagination,
		isLoading,
		error,
		searchTerm,
		setSearchTerm,
		currentPage,
		setCurrentPage,
		getPageNumbers,
		handlePrevious,
		handleNext,
		handlePageClick,
		mutate,
	};
}
