import { NextResponse } from "next/server";
import OpenAI from "openai";
import { stateMappings } from "../../../../constant/location";

const openai = new OpenAI({
  apiKey: process.env.OPEN_API_KEY!,
});

interface ProcessRequest {
  query: string;
  facet: string;
}

interface ProcessResponse {
  value: string | number | string[] | null;
}

// Function to normalize location names
function normalizeLocation(location: string): string {
  const normalized = location.toLowerCase().trim();
  return stateMappings[normalized] || normalized;
}

export async function POST(req: Request) {
  try {
    const { query, facet } = await req.json() as ProcessRequest;
    
    if (!query || !facet) {
      return NextResponse.json(
        { error: "Query and facet are required" },
        { status: 400 }
      );
    }

    // More natural prompt that handles both direct numbers and natural language
    const prompt = `Extract the ${facet} value from the following response. 
    For numeric values (bedrooms, bathrooms, homeSize, parkingSpaces):
    - Extract only the numbers whether written as digits or words (e.g., "one" → "1", "two" → "2", "three" → "3", "four" → "4", "five" → "5", "six" → "6", "seven" → "7", "eight" → "8", "nine" → "9", "ten" → "10", "eleven" → "11", "twelve" → "12", "thirteen" → "13", "fourteen" → "14", "fifteen" → "15", "sixteen" → "16", "seventeen" → "17", "eighteen" → "18", "nineteen" → "19", "twenty" → "20")
    - For ranges, take the lower number (e.g., "3-4" → "3")
    - For "studio" or "0" → return "0"
    - For written numbers like "three" or "four", convert to digits (e.g., "three" → "3", "four" → "4")
    - For compound numbers like "twenty one" or "twenty-two", convert to digits (e.g., "twenty one" → "21", "twenty-two" → "22")
    
    For location:
    - If a valid state abbreviation is found (e.g., "NY", "CA"), convert to lowercase (e.g., "ny", "ca")
    - If a full state name is found (e.g., "New York", "California"), convert to lowercase abbreviation (e.g., "ny", "ca")
    - For cities, convert to lowercase (e.g., "New York" → "new york")
    - Handle common state name variations (e.g., "Cali" → "ca", "Mass" → "ma", "Penn" → "pa")
    
    For amenities:
    - Extract all mentioned amenities as an array
    - Convert to lowercase
    - Handle common synonyms (e.g., "garage" → "garage", "parking" → "garage")
    - Return as a comma-separated list
    
    For parkingType:
    - Extract the parking type (e.g., "garage", "street", "lot", "covered", "underground")
    - Convert to lowercase
    - Handle common synonyms (e.g., "parking garage" → "garage", "carport" → "covered")
    - Return the standardized parking type
    
    For other fields (propertyType, saleType):
    - Extract the relevant term
    - Convert to lowercase
    - Handle common synonyms (e.g., "apt" → "apartment", "condo" → "condominium")
    
    Return only the value, nothing else. If no clear value is found, return null.
    
    Examples:
    - "I need five" → "5"
    - "I want four" → "4"
    - "3-4" → "3"
    - "in New York" → "ny"
    - "in NY" → "ny"
    - "in Cali" → "ca"
    - "in Mass" → "ma"
    - "studio" → "0"
    - "2000 sq ft" → "2000"
    - "apt for rent" → "apartment"
    - "three" → "3"
    - "four" → "4"
    - "twenty one" → "21"
    - "twenty-two" → "22"
    - "with pool and garage" → "pool,garage"
    - "2 parking spaces" → "2"
    - "underground parking" → "underground"
    - "street parking" → "street"
    
    Response: "${query}"`;

    const completion = await openai.chat.completions.create({
      messages: [{ role: "user", content: prompt }],
      model: "gpt-4-turbo-preview",
      temperature: 0,
    });

    const extractedValue = completion.choices[0]?.message?.content?.trim() || null;
    
    // Convert to appropriate type based on facet
    let processedValue: string | number | string[] | null = extractedValue;
    if (extractedValue !== null) {
      switch (facet) {
        case "bedrooms":
        case "bathrooms":
        case "homeSize":
        case "parkingSpaces":
          // Extract just the number if it's a sentence
          const numericMatch = extractedValue.match(/\d+/);
          processedValue = numericMatch ? parseInt(numericMatch[0], 10) : parseInt(extractedValue, 10);
          break;
        case "propertyType":
        case "propertyTypes":
        case "saleType":
        case "parkingType":
          processedValue = extractedValue.toLowerCase();
          break;
        case "location":
          // Normalize location using state mappings and return as array
          const normalizedLocation = normalizeLocation(extractedValue.replace(/['"]/g, '').trim());
          processedValue = [normalizedLocation];
          break;
        case "amenities":
          // Split comma-separated list into array and trim each item
          processedValue = extractedValue.split(',').map(item => item.trim().toLowerCase());
          break;
        default:
          processedValue = extractedValue;
      }
    }

    const response: ProcessResponse = { value: processedValue };
    return NextResponse.json(response);
  } catch (error) {
    console.error("NLP processing error:", error);
    return NextResponse.json(
      { error: "Failed to process natural language response" },
      { status: 500 }
    );
  }
}
