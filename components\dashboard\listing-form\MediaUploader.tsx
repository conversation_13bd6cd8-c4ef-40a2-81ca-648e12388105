"use client";
import { useState, useCallback, useEffect } from "react";
import { ImageUpload, ImageFile } from "@/components/ui/image-upload";
import mime from "mime-types";
import dayjs from "dayjs";
import axios from "axios";
import {
	deleteDirectory,
	getSignedUrlForUpload,
	publicObject,
} from "@/lib/AWS-S3";
import { useUser } from "@clerk/nextjs";
import { useUploadStore } from "@/stores/upload-store";

// Define interface for image info that includes key
export interface ImageInfo {
	url: string;
	key: string;
	id: string;
}

type MediaUploaderProps = {
	onImagesUpdate?: (imageInfos: ImageInfo[]) => void;
	initialImages?: ImageFile[];
};

const MediaUploader = ({
	onImagesUpdate,
	initialImages = [],
}: MediaUploaderProps) => {
	const { user } = useUser();
	const [uploadedImages, setUploadedImages] =
		useState<ImageFile[]>(initialImages);
	const { incrementActiveUploads, decrementActiveUploads, resetActiveUploads } =
		useUploadStore();

	// Reset active uploads counter when component mounts and unmounts
	useEffect(() => {
		// Reset on mount to ensure clean state
		resetActiveUploads();

		// Reset on unmount to prevent lingering states
		return () => {
			resetActiveUploads();
		};
	}, [resetActiveUploads]);

	useEffect(() => {
		if (uploadedImages.length > 0) {
			// Extract URLs and keys from uploaded images and filter out any undefined values
			const imageInfos = uploadedImages

				.map((image) => {
					if ((image.url || image.preview) && image.key) {
						return { id: image.id, url: image.url, key: image.key };
					}
					return null;
				})
				.filter((info): info is ImageInfo => info !== null);
			onImagesUpdate?.(imageInfos);
		} else if (uploadedImages.length === 0 && onImagesUpdate) {
			// If all images are removed, update with empty array
			onImagesUpdate([]);
		}
	}, [uploadedImages, onImagesUpdate]);

	// Upload handler for S3
	const handleUpload = async (
		file: File,
		onProgress?: (progress: number) => void
	) => {
		// Increment active uploads counter when upload starts
		incrementActiveUploads();

		try {
			// Generate unique filename
			const timestamp = Date.now();
			const randomString = Math.random().toString(36).substring(2, 10);
			const fileExtension = file.name.split(".").pop() || "";

			// Use dayjs to format the date in YYYY-MM-DD format
			const dateString = dayjs().format("YYYY-MM-DD");

			// Check if user exists before accessing id
			if (!user) {
				throw new Error("User is not signed in");
			}

			// Create filename with date included
			const fileName = `uploads/${user.id}/${dateString}/${timestamp}-${randomString}.${fileExtension}`;

			// Get the file's content type
			const contentType = mime.lookup(file.name) || "application/octet-stream";

			// Get presigned URL from API
			const { signedUrl, objectKey } = await getSignedUrlForUpload(
				fileName,
				contentType
			);

			// Upload to S3 using axios instead of fetch to track progress
			await axios.put(signedUrl, file, {
				headers: {
					"Content-Type": contentType,
				},
				onUploadProgress: (progressEvent) => {
					// Calculate and report progress percentage
					if (progressEvent.total) {
						const percentCompleted = Math.round(
							(progressEvent.loaded * 100) / progressEvent.total
						);
						// Call the progress callback if provided
						onProgress?.(Math.min(percentCompleted, 95));
					}
				},
			});
			onProgress?.(100);

			publicObject(objectKey);
			// Return the public URL and object key
			const publicUrl = `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${objectKey}`;

			// Decrement active uploads counter when upload completes
			decrementActiveUploads();
			return { url: publicUrl, key: objectKey };
		} catch (error) {
			// Decrement active uploads counter if there's an error
			decrementActiveUploads();
			throw error;
		}
	};

	const deleteS3File = async (key: string) => {
		deleteDirectory(key);
	};

	// Update state when images change - use useCallback to prevent unnecessary recreation
	const handleImagesChange = useCallback((images: ImageFile[]) => {
		setUploadedImages(images);
	}, []);

	return (
		<ImageUpload
			initialImages={initialImages}
			uploadFile={handleUpload}
			deleteFile={deleteS3File}
			onChange={handleImagesChange}
			maxImages={150}
		/>
	);
};

export default MediaUploader;
