"use client";

import { useClerk } from "@clerk/nextjs";
import {
	EmailLinkErrorCodeStatus,
	isEmailLinkError,
} from "@clerk/nextjs/errors";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { setTermsAcceptanceCookie } from "@/lib/serverUtils";
import Logo from "@/components/ui/Logo";

export default function VerifyEmailLink() {
	const [verificationStatus, setVerificationStatus] = useState("loading");
	const router = useRouter();

	const { handleEmailLinkVerification, loaded } = useClerk();

	useEffect(() => {
		if (!loaded) return;

		async function verify() {
			try {
				// Dynamically set the host domain for dev and prod
				// You could instead use an environment variable or other source for the host domain
				const protocol = window.location.protocol;
				const host = window.location.host;

				await handleEmailLinkVerification({
					// URL to navigate to if sign-in flow needs more requirements, such as MFA
					redirectUrl: `${protocol}//${host}/sign-in`,
				});

				// If not redirected at this point,
				// the flow has completed
				setVerificationStatus("verified");
			} catch (err) {
				let status = "failed";
				// @ts-expect-error unknown error
				if (isEmailLinkError(err)) {
					// If link expired, set status to expired
					if (err.code === EmailLinkErrorCodeStatus.Expired) {
						status = "expired";
					} else if (err.code === EmailLinkErrorCodeStatus.ClientMismatch) {
						// OPTIONAL: This check is only required if you have
						// the 'Require the same device and browser' setting
						// enabled in the Clerk Dashboard
						status = "client_mismatch";
					}
				}

				setVerificationStatus(status);
				return;
			}
		}
		verify();
	}, [handleEmailLinkVerification, loaded]);

	// Add redirect effect when verified
	useEffect(() => {
		if (verificationStatus === "verified") {
			// Call async function to set terms acceptance cookie and redirect
			const handleVerified = async () => {
				await setTermsAcceptanceCookie();
				router.push("/terms-of-service");
			};
			handleVerified();
		}
	}, [verificationStatus, router]);

	if (verificationStatus === "failed") {
		return (
			<div className="flex flex-col items-center justify-center h-screen gap-4">
				<h1>Verify your email</h1>
				<p>The email link verification failed.</p>
				<Link href="/sign-in">
					<Button className="mt-8">Sign in</Button>
				</Link>
			</div>
		);
	}

	if (verificationStatus === "expired") {
		return (
			<div className="flex flex-col items-center justify-center h-screen gap-4">
				<h1>Verify your email</h1>
				<p>The email link has expired.</p>
				<Link href="/sign-in">
					<Button className="mt-8">Sign in</Button>
				</Link>
			</div>
		);
	}

	// OPTIONAL: This check is only required if you have
	// the 'Require the same device and browser' setting
	// enabled in the Clerk Dashboard
	if (verificationStatus === "client_mismatch") {
		return (
			<div className="flex flex-col items-center justify-center h-screen gap-4">
				<h1>Verify your email</h1>
				<p>
					You must complete the email link sign-in on the same device and
					browser as you started it on.
				</p>
				<Link href="/sign-in">
					<Button className="mt-8">Sign in</Button>
				</Link>
			</div>
		);
	}

	return (
		<div className="flex flex-col items-center justify-center h-screen gap-4">
			<Logo width={280} height={91} />
		</div>
	);
}
