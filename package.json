{"name": "maison-global", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:prod": "dotenv -e .env.production -- next dev", "build": "next build", "build:prod": "dotenv -e .env.production -- next build", "start": "next start", "start:prod": "dotenv -e .env.production -- next start", "lint": "next lint", "prettier": "prettier --write .", "db:generate": "drizzle-kit generate", "db:migrate:dev": "dotenv -e .env.local -- drizzle-kit migrate", "db:migrate:prod": "dotenv -e .env.production -- drizzle-kit migrate"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/client-ses": "^3.826.0", "@aws-sdk/client-sqs": "^3.758.0", "@aws-sdk/lib-storage": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@clerk/elements": "^0.23.1", "@clerk/nextjs": "^6.12.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.27.8", "@hookform/resolvers": "^4.1.3", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@stream-io/video-react-sdk": "^1.18.1", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@upstash/vector": "^1.2.1", "algoliasearch": "^5.20.3", "async-retry": "^1.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "countries-list": "^3.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.5.2", "fs-extra": "^11.3.0", "lucide-react": "^0.477.0", "mapbox-gl": "^3.12.0", "mime-types": "^3.0.1", "moment": "^2.30.1", "next": "^15.3.1", "next-themes": "^0.4.6", "openai": "^4.103.0", "react": "^19.1.0", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-instantsearch": "^7.15.3", "react-intersection-observer": "^9.16.0", "react-masonry-css": "^1.0.16", "react-responsive-masonry": "^2.7.1", "react-spinners-kit": "^1.9.1", "slugify": "^1.6.6", "sonner": "^2.0.1", "stream-chat": "^9.1.1", "stream-chat-react": "^13.0.1", "svix": "^1.61.2", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/async-retry": "^1.4.9", "@types/fs-extra": "^11.0.4", "@types/mime-types": "^2.1.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-responsive-masonry": "^2.6.0", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.2.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.88.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4", "typescript": "^5"}}