import { useEffect, useState } from "react";
import { useFetchScheduledCalls } from "./useFetchScheduledCalls";
import { useCreateChatClient } from "stream-chat-react";
import { UserSelectType } from "@/db/services/users.service";
import { useSendEmail } from "./useSendEmail";
import { getPendingRecipients } from "@/db/services/schedules.service";

export const useNotifications = (
	me: UserSelectType | null,
	token: string | null
) => {
	const [unreadMessages, setUnreadMessages] = useState(0);
	const [pendingMeetings, setPendingMeetings] = useState(0);

	const { sendEmail } = useSendEmail();

	const { data: scheduledCalls } = useFetchScheduledCalls(me?.id || "");

	const client = useCreateChatClient({
		apiKey: process.env.NEXT_PUBLIC_GETSTREAM_API_KEY!,
		tokenOrProvider: token ?? "",
		userData: {
			id: me?.id ?? "",
			name: me?.userName ?? "",
			image: me?.imageUrl || undefined,
		},
	});

	useEffect(() => {
		if (!client || !me?.id) return;

		const handleEvent = async (event: {
			total_unread_count?: number;
			message?: any;
		}) => {
			if (event.total_unread_count !== undefined) {
				setUnreadMessages(event.total_unread_count);
			}
			if (
				event.message !== undefined &&
				event?.message.attachments.length > 0 &&
				event.message.attachments[0]?.type === "schedule-meeting"
			) {
				console.log(event);
				if (me?.id === event.message.attachments[0]?.receiver) {
					console.log("send email");
					try {
						await sendEmail({
							to: me?.email,
							subject: event.message.attachments[0]?.meetingTitle,
							body: event.message.attachments[0]?.text,
						});
						console.log("Email sent successfully");
					} catch (error) {
						console.error("Failed to send email:", error);
					}
				}
			}
		};

		client.on("notification.message_new", handleEvent);
		client.on("notification.mark_read", handleEvent);
		client.on("message.new", handleEvent);
		client.on("message.read", handleEvent);

		client.getUnreadCount().then((response) => {
			setUnreadMessages(response.total_unread_count || 0);
		});

		return () => {
			client.off("notification.message_new", handleEvent);
			client.off("notification.mark_read", handleEvent);
			client.off("message.new", handleEvent);
			client.off("message.read", handleEvent);
		};
	}, [client, me, sendEmail]);

	useEffect(() => {
		if (!scheduledCalls || !me?.id) return;

		const pending = scheduledCalls.filter((call: any) => {
			try {
				// Use helper function to get pending recipients
				const pendingRecipients = getPendingRecipients(call.recipients || {});
				return pendingRecipients.includes(me.id);
			} catch (error) {
				console.error("Error processing recipients:", error);
				return false;
			}
		});

		setPendingMeetings(pending.length);
	}, [scheduledCalls, me?.id]);

	return {
		unreadMessages,
		pendingMeetings,
	};
};
