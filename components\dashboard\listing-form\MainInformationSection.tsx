"use client";
import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
	ListingFormValues,
	countryOptions,
	currencyOptions,
	visibilityOptions,
} from "@/lib/schemas/listing-schema";
import InputWithUnit from "./InputWithUnit";
import { SearchableSelect } from "@/components/ui/searchable-select";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

type MainInformationSectionProps = {
	form: UseFormReturn<ListingFormValues>;
};

const MainInformationSection = ({ form }: MainInformationSectionProps) => {
	return (
		<div className="space-y-4">
			<h2 className="text-3xl font-normal font-lora">Main Information</h2>

			<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
				<FormField
					control={form.control}
					name="propertyTitle"
					render={({ field }) => (
						<FormItem className="flex flex-col">
							<FormLabel>Property Title</FormLabel>
							<FormControl>
								<Input placeholder="Enter property title" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormItem className="flex flex-col space-y-1.5">
					<InputWithUnit
						form={form}
						fieldName="price"
						valueName="amount"
						unitName="currency"
						label="Price"
						placeholder="Enter price"
						units={Array.from(currencyOptions)}
						defaultUnit="USD"
					/>
					<FormMessage />
				</FormItem>
			</div>

			<div className="grid gap-3 grid-c:gr-1 md:grid-cols-3">
				<div className="pr-2 md:col-span-2">
					<FormField
						control={form.control}
						name="streetAddress"
						render={({ field }) => (
							<FormItem className="flex flex-col justify-center h-full">
								<FormLabel>Street Address</FormLabel>
								<FormControl>
									<Input placeholder="Enter street address" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<FormField
					control={form.control}
					name="hideStreetAddress"
					render={({ field }) => (
						<FormItem className="flex flex-row items-start justify-start h-full mt-8 space-x-2">
							<FormControl>
								<Checkbox
									checked={field.value}
									onCheckedChange={field.onChange}
								/>
							</FormControl>
							<FormLabel className="text-sm font-normal">
								Hide street address
							</FormLabel>
						</FormItem>
					)}
				/>
			</div>

			<div className="grid grid-cols-1 gap-3 md:grid-cols-3">
				<FormField
					control={form.control}
					name="city"
					render={({ field }) => (
						<FormItem className="flex flex-col h-full">
							<FormLabel>City</FormLabel>
							<FormControl>
								<Input placeholder="Enter city" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="stateRegion"
					render={({ field }) => (
						<FormItem className="flex flex-col h-full">
							<FormLabel>State / Region / Province</FormLabel>
							<FormControl>
								<Input placeholder="Enter state or region" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="zipCode"
					render={({ field }) => (
						<FormItem className="flex flex-col h-full">
							<FormLabel>Zip / Postal Code</FormLabel>
							<FormControl>
								<Input placeholder="Enter zip code" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<div className="grid grid-cols-1 gap-3 md:grid-cols-3">
				<FormField
					control={form.control}
					name="country"
					render={({ field }) => (
						<FormItem className="flex flex-col">
							<FormLabel>Country</FormLabel>
							<FormControl>
								<SearchableSelect
									options={countryOptions}
									value={field.value || "US"}
									onValueChange={field.onChange}
									placeholder="Select a country"
									emptyMessage="No country found."
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="neighborhood"
					render={({ field }) => (
						<FormItem className="flex flex-col">
							<FormLabel>Neighborhood / Region / Area</FormLabel>
							<FormControl>
								<Input placeholder="Enter neighborhood" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="visibility"
					render={({ field }) => (
						<FormItem className="flex flex-col">
							<FormLabel>Visibility</FormLabel>
							<FormControl>
								<Select
									defaultValue={field.value || "private"}
									onValueChange={field.onChange}
								>
									<SelectTrigger className="capitalize">
										<SelectValue placeholder="Select visibility" />
									</SelectTrigger>
									<SelectContent>
										{visibilityOptions.map((item) => (
											<SelectItem
												key={item}
												value={item}
												className="capitalize"
											>
												{item}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
};

export default MainInformationSection;
