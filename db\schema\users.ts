import { varchar, boolean, index, uniqueIndex } from "drizzle-orm/pg-core";
import { pgTable } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { timestamps, simplifyUuidField } from "../columns.helpers";
import { roleEnum, twoFactorAuthEnum } from "./enums";
import { listings } from "./listings";
import { inquiries } from "./inquiries";
import { favoritesListings } from "./favoritesListings";
import { listingReviewRequests } from "./listingReviewRequests";
import { listingRecommendations } from "./recommendations";
import { searcherAreas } from "./geographicAreas";
import { userConnections } from "./userConnections";
import { userSubscriptions } from "./userSubscriptions";
import { userSiteAccess } from "./userSiteAccess";
import { userGeographicAccess } from "./userGeographicAccess";
import { paymentHistory } from "./paymentHistory";
import { schedules } from "./schedules";

export const users = pgTable(
	"users",
	{
		id: simplifyUuid<PERSON>ield(),
		clerkId: varchar("clerk_id", { length: 64 }),
		role: roleEnum("role").notNull().default("buyer"),
		subCategory: varchar("sub_category", { length: 50 }),
		firstName: varchar("first_name", { length: 50 }),
		lastName: varchar("last_name", { length: 50 }),
		userName: varchar("user_name", { length: 50 }).notNull().unique(),
		companyName: varchar("company_name", { length: 100 }),
		phoneNumber: varchar("phone_number", { length: 15 }),
		email: varchar("email", { length: 255 }).notNull().unique(),
		emailVerified: boolean("email_verified").default(false),
		imageUrl: varchar("image_url", { length: 255 }),
		mgExclusives: boolean("mg_exclusives").default(false),
		twoFactorAuth: twoFactorAuthEnum("two_factor_auth").default("disabled"),
		communicationType: varchar("communication_type", { length: 50 }),
		...timestamps,
	},
	(table) => [
		uniqueIndex("username_idx").on(table.userName),
		uniqueIndex("email_idx").on(table.email),
		index("role_idx").on(table.role),
		index("subcategory_idx").on(table.subCategory),
	]
);

// User relationships
export const userRelations = relations(users, ({ many, one }) => ({
	listings: many(listings),
	inquiries: many(inquiries),
	favoritesListings: many(favoritesListings),
	recommendationsReceived: many(listingRecommendations, {
		relationName: "buyer",
	}),
	recommendationsSent: many(listingRecommendations, {
		relationName: "searcher",
	}),
	areas: many(searcherAreas),
	clientConnections: many(userConnections, { relationName: "asClient" }),
	providerConnections: many(userConnections, { relationName: "asProvider" }),
	subscription: one(userSubscriptions),
	siteAccesses: many(userSiteAccess),
	geographicAccesses: many(userGeographicAccess),
	paymentHistory: one(paymentHistory),
	listingReviewRequests: many(listingReviewRequests),
	sentSchedules: many(schedules, { relationName: "scheduleSender" }),
	receivedSchedules: many(schedules, { relationName: "scheduleReceiver" }),
}));
