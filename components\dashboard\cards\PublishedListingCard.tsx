import { Card<PERSON>eader, CardTitle } from "../../ui/card";
import { formatSimplePrice } from "@/lib/utils";
import { ListingData } from "@/types/listing";
import BaseListingCard from "./BaseListingCard";
import FavoriteButton from "./FavoriteButton";
import { toggleFavorite } from "@/db/services/favoritesListings.service";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import useFavoritesStore from "@/stores/favorites";
import IdDisplay from "../../ui/IdDisplay";

interface PublishedListingCardProps {
	item: ListingData;
	isFavored?: boolean;
}

const PublishedListingCard = ({
	item,
	isFavored = false,
}: PublishedListingCardProps) => {
	const { user: me } = useCurrentUser();
	const { setFavorite } = useFavoritesStore();

	const handleFavoriteToggle = async (newFavoriteState: boolean) => {
		const listingId = item.id;
		if (listingId && me) {
			await toggleFavorite(me.id, listingId);
			// Update store to sync with other components
			setFavorite(listingId, newFavoriteState);
		}
	};

	return (
		<BaseListingCard item={item}>
			<CardHeader className="py-2">
				{item.location && (
					<div>
						<div className="flex flex-row items-center justify-between">
							<span className="text-base font-normal font-manrope text-slate-900">
								{item.hideStreetAddress
									? "Address hidden"
									: item.location.address}
							</span>

							{me?.role === "buyer" && typeof isFavored === "boolean" && (
								<FavoriteButton
									variant={"ghost"}
									listingId={item.id}
									isFavored={isFavored}
									onToggleFavorite={handleFavoriteToggle}
								/>
							)}
						</div>
						<CardTitle className="text-2xl font-normal text-slate-900 font-lora">
							{`${item.location.city}, ${item.location.state}`}
						</CardTitle>
					</div>
				)}
				<div className="flex flex-row justify-between gap-4 my-2 text-lg font-manrope">
					<span className="text-xl font-bold text-slate-900 font-manrope">
						{formatSimplePrice(Number(item.price))}
					</span>
					<IdDisplay
						id={item.id}
						className="text-lg font-normal font-manrope"
					/>
				</div>
				<div className="flex flex-row items-center justify-between gap-4">
					{item.realEstateDetails && (
						<div className="flex flex-row gap-5 font-manrope">
							<div className="flex flex-col items-center gap-1">
								<span className="text-base font-normal text-gray-600 capitalize">
									bathrooms
								</span>
								<span className="text-base font-bold text-slate-900">
									{item.realEstateDetails.bathrooms}
								</span>
							</div>
							<div className="flex flex-col items-center gap-1">
								<span className="text-base font-normal text-gray-600 capitalize">
									bedrooms
								</span>
								<span className="text-base font-bold text-slate-900">
									{item.realEstateDetails.bedrooms}
								</span>
							</div>
							<div className="flex flex-col items-center gap-1">
								<span className="text-base font-normal text-gray-600 capitalize">
									sqft
								</span>
								<span className="text-base font-bold text-slate-900">
									{item.realEstateDetails.homeSize}
								</span>
							</div>
						</div>
					)}
					<div>
						<span className="text-lg font-bold text-black capitalize font-manrope">
							{item.visibility}
						</span>
					</div>
				</div>
			</CardHeader>
		</BaseListingCard>
	);
};

export default PublishedListingCard;
