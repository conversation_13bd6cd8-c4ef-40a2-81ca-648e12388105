"use client";
import React, { useState, useEffect } from "react";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { ListingFormValues } from "@/lib/schemas/listing-schema";

type Unit = {
	value: string;
	label: string;
};

type InputWithUnitProps = {
	form: UseFormReturn<ListingFormValues>;
	fieldName: string;
	valueName: string;
	unitName: string;
	label: string;
	description?: string;
	placeholder?: string;
	units: Unit[];
	defaultUnit?: string;
};

// Format number to display with commas
const formatNumberDisplay = (value: string | number): string => {
	if (value === "" || value === null || value === undefined) return "";
	const num = typeof value === "string" ? parseFloat(value) : value;
	if (isNaN(num)) return "";
	return num.toLocaleString("en-US");
};

// Parse formatted display back to number
const parseDisplayValue = (displayValue: string): string => {
	if (displayValue === "") return "";
	const cleaned = displayValue.replace(/,/g, "");
	const num = parseFloat(cleaned);
	return isNaN(num) ? "" : num.toString();
};

// Custom component for input fields with units (like price, size, etc.)
const InputWithUnit = ({
	form,
	fieldName,
	valueName,
	unitName,
	label,
	placeholder = "0",
	units,
	defaultUnit,
}: InputWithUnitProps) => {
	const fieldPath = `${fieldName}.${valueName}`;
	const fieldValue = form.watch(fieldPath as any);
	const [displayValue, setDisplayValue] = useState("");

	// Update display value when field value changes
	useEffect(() => {
		setDisplayValue(formatNumberDisplay(fieldValue || ""));
	}, [fieldValue]);

	return (
		<div className="flex flex-row gap-2">
			<FormField
				control={form.control}
				name={`${fieldName}.${valueName}` as any}
				render={({ field }) => (
					<FormItem className="flex flex-col flex-grow">
						<FormLabel>{label}</FormLabel>
						<FormControl>
							<Input
								type="text"
								placeholder={placeholder}
								value={displayValue}
								onChange={(e) => {
									const inputValue = e.target.value;
									setDisplayValue(inputValue);

									// Parse and validate the input
									const numericValue = parseDisplayValue(inputValue);
									if (numericValue === "") {
										field.onChange("");
									} else {
										const num = parseFloat(numericValue);
										if (!isNaN(num) && num >= 0) {
											field.onChange(num);
										}
									}
								}}
								onBlur={() => {
									// Format the display value on blur
									if (
										field.value !== "" &&
										field.value !== null &&
										field.value !== undefined
									) {
										setDisplayValue(formatNumberDisplay(field.value));
									}
								}}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>

			<FormField
				control={form.control}
				name={`${fieldName}.${unitName}` as any}
				defaultValue={defaultUnit || units[0].value}
				render={({ field }) => (
					<FormItem className="flex flex-col flex-shrink-0 w-20">
						<FormLabel>&nbsp;</FormLabel>
						<FormControl>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<SelectTrigger className="h-9">
									<SelectValue placeholder={units[0].label} />
								</SelectTrigger>
								<SelectContent>
									{units.map((unit) => (
										<SelectItem key={unit.value} value={unit.value}>
											{unit.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</FormControl>
					</FormItem>
				)}
			/>
		</div>
	);
};

export default InputWithUnit;
