import {
	varchar,
	text,
	numeric,
	index,
	uniqueIndex,
	boolean,
	integer,
} from "drizzle-orm/pg-core";
import { pgTable } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { simplifyUuidField, timestamps } from "../columns.helpers";
import { users } from "./users";
import { locations } from "./locations";
import { realEstateDetails } from "./realEstateDetails";
import { media } from "./media";
import { inquiries } from "./inquiries";
import { listingReviewRequests } from "./listingReviewRequests";
import { favoritesListings } from "./favoritesListings";
import { schedules } from "./schedules";
import {
	visibilityEnum,
	parkingTypeEnum,
	historicalDesignationEnum,
} from "./enums";

export const listings = pgTable(
	"listings",
	{
		id: simplifyUuidField(),
		userId: text("user_id").references(() => users.id, {
			onDelete: "set null",
		}),
		listingType: varchar("listing_type", { length: 30 }).notNull(),
		title: text("title").notNull(),
		slug: varchar("slug", { length: 100 }).notNull(),
		description: text("description"),
		amenities: text("amenities").array(),
		price: numeric("price", { precision: 15, scale: 2 }).notNull(),
		currency: varchar("currency", { length: 3 }).default("USD").notNull(),
		isPrimary: boolean("is_primary").default(false).notNull(),
		hideStreetAddress: boolean("hide_street_address").default(false).notNull(),
		viewCount: integer("view_count").default(0).notNull(),
		favoriteCount: integer("favorite_count").default(0).notNull(),
		neighborhood: varchar("neighborhood", { length: 100 }),
		visibility: visibilityEnum("visibility").default("private").notNull(),
		parkingType: parkingTypeEnum("parking_type").default("None"), // Use enum for parking type
		parkingSpaces: integer("parking_spaces").default(0), // Number of parking spaces
		historicalDesignation: historicalDesignationEnum(
			"historical_designation"
		).default("None"), // Use enum for historical designation
		...timestamps,
	},
	(table) => [
		uniqueIndex("slug_idx").on(table.slug),
		index("listing_user_id_idx").on(table.userId),
		index("listing_type_idx").on(table.listingType),
		index("listing_visibility_idx").on(table.visibility),
	]
);

export const listingRelations = relations(listings, ({ one, many }) => ({
	user: one(users, {
		fields: [listings.userId],
		references: [users.id],
	}),
	location: one(locations, {
		fields: [listings.id],
		references: [locations.listingId],
	}),
	realEstateDetails: one(realEstateDetails, {
		fields: [listings.id],
		references: [realEstateDetails.listingId],
	}),
	media: many(media),
	inquiries: many(inquiries),
	listingReviewRequest: one(listingReviewRequests, {
		fields: [listings.id],
		references: [listingReviewRequests.listingId],
	}),
	favoritesListings: many(favoritesListings), // Add favorites relationship
	schedules: many(schedules),
}));
