import useSWR from "swr";
import { fetcher } from "@/lib/utils";
import { User } from "./useUsersByIds";

interface UsersResponse {
  users: User[];
  pagination: {
    total: number;
    pageSize: number;
    currentPage: number;
    totalPages: number;
  };
}

export function useAllUsers(
	page: number = 1,
	pageSize: number = 20,
	search: string = ""
) {
	const params = new URLSearchParams();
	params.append("page", String(page));
	params.append("pageSize", String(pageSize));
	if (search) params.append("search", search);

	const { data, error, isLoading, mutate } = useSWR<UsersResponse>(
		`/api/users?${params.toString()}`,
		fetcher,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: true,
			dedupingInterval: 60000, // Cache for 1 minute
			keepPreviousData: true,
		}
	);

	return {
		users: data?.users || [],
		pagination: data?.pagination,
		isLoading,
		error,
		mutate,
	};
}
