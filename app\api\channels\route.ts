import { NextRequest, NextResponse } from "next/server";
import { getAllChannels, deleteAllChannels } from "@/utils/stream-client";

/**
 * GET handler for channels API
 * Retrieve all channels, optionally filtered by user
 */
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const userId = searchParams.get("userId");

		// Get all channels, optionally filtered by user
		const result = await getAllChannels(userId || undefined);

		if (result.success) {
			return NextResponse.json({
				success: true,
				data: result.channels,
				count: result.channels?.length || 0,
			});
		} else {
			return NextResponse.json(
				{
					success: false,
					error: result.error,
				},
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error("API Error:", error);
		return NextResponse.json(
			{
				success: false,
				error: "Internal server error",
			},
			{ status: 500 }
		);
	}
}

/**
 * DELETE handler for channels API
 * Delete all channels, optionally filtered by user
 */
export async function DELETE(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const userId = searchParams.get("userId");

		// Delete all channels, optionally filtered by user
		const result = await deleteAllChannels(userId || undefined);

		if (result.success) {
			return NextResponse.json({
				success: true,
				message: "All channels deleted successfully",
			});
		} else {
			return NextResponse.json(
				{
					success: false,
					error: result.error,
				},
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error("API Error:", error);
		return NextResponse.json(
			{
				success: false,
				error: "Internal server error",
			},
			{ status: 500 }
		);
	}
}
