import { IoPaperPlaneOutline, IoSearchOutline } from "react-icons/io5";
import { Loader2 } from "lucide-react";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import useSearchStore from "@/stores/search";
import { fieldNameMappings } from "@/lib/fieldMappings";

interface Message {
  type: 'user' | 'assistant';
  content: string;
}

const FollowupSearchPanel = ({
	initialQuery,
	resetState,
	loading,
	missingFacets,
	setIsNewSearch,
}: {
	initialQuery: string;
	resetState: () => void;
	loading: boolean;
	handleSearch: () => void;
	setIsNewSearch: (query: boolean) => void;
	missingFacets: string[];
	facets: Record<string, any>;
	setFacets: (facets: Record<string, any>) => void;
}) => {
	const [messages, setMessages] = useState<Message[]>([]);
	const [remainingFacets, setRemainingFacets] = useState<string[]>([]);
	const [conversationContext, setConversationContext] = useState<string[]>([]);
	const [sub_query, setSubQuery] = useState(""); // Move to internal state
	const [customHeight, setCustomHeight] = useState<number>(0);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const { setSearchValue, bodyScrollYPosition } = useSearchStore();

	useEffect(() => {
		// Calculate height based on scroll position, ensuring minimum value of 0
		setCustomHeight(Math.max(184 - bodyScrollYPosition, 0));
	}, [bodyScrollYPosition]);
	// Update remaining facets when missingFacets changes
	useEffect(() => {
		if (missingFacets.length > 0) {
			const initialMessage = {
				type: "assistant" as const,
				content: `I see you're looking for ${initialQuery}. Feel free to tell me more about what you're looking for.`,
			};
			setMessages([initialMessage]);
			setRemainingFacets(missingFacets);
		}
	}, [missingFacets, initialQuery]);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	};

	useEffect(() => {
		scrollToBottom();
	}, [messages]);

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleSendMessage();
		}
	};

	const handleSendMessage = async () => {
		if (!sub_query.trim()) return;

		// Add user message to chat
		setMessages((prev) => [...prev, { type: "user", content: sub_query }]);

		// Add to conversation context
		const newContext = [...conversationContext, sub_query];
		setConversationContext(newContext);

		// Combine initial query with conversation context for comprehensive search
		const combinedQuery = `${initialQuery} ${newContext.join(" ")}`;

		// Update search value with combined query to trigger new search
		setSearchValue(combinedQuery);

		// Generate dynamic assistant message based on the query content and actual facets
		const generateAssistantMessage = (userQuery: string) => {
			const query = userQuery.toLowerCase();

			// Check for specific criteria mentions based on actual facets
			if (query.includes("bedroom") || query.includes("bed")) {
				return "Got it! I've updated your search to include the bedroom requirements. Anything else about the layout?";
			} else if (query.includes("bathroom") || query.includes("bath")) {
				return "Perfect! I've refined the search with your bathroom preferences. Any other requirements?";
			} else if (
				query.includes("price") ||
				query.includes("budget") ||
				query.includes("$") ||
				query.includes("cost")
			) {
				return "Great! I've incorporated your budget preferences. What other features are important?";
			} else if (
				query.includes("location") ||
				query.includes("area") ||
				query.includes("neighborhood") ||
				query.includes("district") ||
				query.includes("city")
			) {
				return "Excellent! I've narrowed down the search to your preferred area. What other features matter to you?";
			} else if (
				query.includes("size") ||
				query.includes("sqft") ||
				query.includes("square") ||
				query.includes("footage")
			) {
				return "Understood! I've updated the search with your size requirements. What else would you like to specify?";
			} else if (
				query.includes("parking") ||
				query.includes("garage") ||
				query.includes("space")
			) {
				return "Perfect! I've included your parking preferences in the search. Any other requirements?";
			} else if (
				query.includes("property type") ||
				query.includes("house") ||
				query.includes("apartment") ||
				query.includes("condo") ||
				query.includes("townhouse")
			) {
				return "Great! I've filtered for your preferred property type. What other criteria are important?";
			} else if (
				query.includes("sale") ||
				query.includes("rent") ||
				query.includes("lease") ||
				query.includes("buy")
			) {
				return "Perfect! I've updated the search with your preferred transaction type. Anything else?";
			} else if (
				query.includes("amenity") ||
				query.includes("amenities") ||
				query.includes("pool") ||
				query.includes("gym") ||
				query.includes("facility")
			) {
				return "Excellent! I've added your amenity preferences to the search. Any other must-haves?";
			} else {
				// Generic responses for other queries
				const responses = [
					"Thanks for the additional details! I've refined your search accordingly.",
					"I've updated the search with your new criteria. Feel free to add more specifics.",
					"Perfect! Your search has been enhanced with these details. Anything else?",
					"Great input! I've incorporated this into your search. What else can I help refine?",
				];
				return responses[Math.floor(Math.random() * responses.length)];
			}
		};

		const assistantMessage = generateAssistantMessage(sub_query);
		setMessages((prev) => [
			...prev,
			{ type: "assistant", content: assistantMessage },
		]);

		// Clear input
		setSubQuery("");
	};

	const allFacetsAnswered = remainingFacets.length === 0;

	return (
		<div
			className="z-[9999] rounded-lg relative bg-[var(--search-panel)] flex flex-col min-h-12"
			style={{
				height: bodyScrollYPosition > 0 ? `${customHeight}px` : "14rem",
			}}
		>
			<div
				className={`flex-1 scrollable px-4 ${customHeight < 0 ? "hidden" : ""}`}
			>
				<div className="flex justify-end px-0 pt-3">
					{initialQuery && (
						<button
							onClick={() => {
								resetState();
								setIsNewSearch(false);
							}}
							className="px-4 py-1 bg-primary text-base !font-normal border-none rounded-lg text-gray-200 hover:bg-gray-700 transition-colors"
						>
							{initialQuery}
						</button>
					)}
				</div>
				<div className="flex items-center gap-2 pl-2">
					<Image
						src="/images/MG Mark.png"
						alt="logo"
						width={24}
						height={24}
						className="w-6 h-6 bg-white rounded-full p-0.5"
					/>
				</div>
				{messages.map((message, index) => (
					<div
						key={index}
						className={`flex mb-2 ${message.type === "assistant" ? "justify-start" : "justify-end"}`}
					>
						<div
							className={`max-w-[80%] rounded-lg px-3 pt-2 pb-2 text-sm font-light font-manrope sm:text-base leading-normal ${
								message.type === "assistant"
									? "text-black sm:text-white bg-[var(--search-panel-placeholder)] sm:bg-[var(--search-panel)]"
									: "text-white bg-primary"
							}`}
						>
							<div className="whitespace-pre-line">{message.content}</div>
						</div>
					</div>
				))}
				<div ref={messagesEndRef} />
				<div className="mb-1">Suggested Search Items:</div>
				{!allFacetsAnswered && remainingFacets.length > 0 && (
					<div className="flex flex-wrap gap-2 pl-2 mt-1 mb-1">
						{remainingFacets.map((facet) => (
							<div
								key={facet}
								className="px-2 py-1 border border-neutral-30 rounded-lg text-xs md:text-sm text-neutral-300 font-light font-manrope leading-normal text-[var(--search-panel-placeholder)]"
							>
								{fieldNameMappings[facet] || facet}
							</div>
						))}
					</div>
				)}
			</div>

			<div className="h-12 bg-[var(--search-panel-foreground)] flex items-center px-4 rounded-lg">
				<div className="relative flex items-center w-full rounded-lg bg-[var(--search-panel-foreground)] border-none">
					<IoSearchOutline className="text-xl text-white" />
					<Input
						type="text"
						placeholder="Tell me more about what you're looking for..."
						disabled={loading}
						value={sub_query}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
							setSubQuery(e.target.value)
						}
						onKeyDown={handleKeyDown}
						className="w-full h-8 px-2 bg-[var(--search-panel-foreground)] focus-visible:ring-0 focus-visible:ring-offset-0 leading-normal text:sm md:text-base font-manrope !font-normal placeholder:text-gray-600 text-white placeholder-gray-600 !border-none selection:bg-blue-200 selection:text-black"
					/>
					{loading ? (
						<div className="absolute right-2">
							<Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
						</div>
					) : (
						<Button
							onClick={handleSendMessage}
							className="absolute right-0 bg-[var(--search-panel-foreground)] hover:bg-primary-dark text-white p-1 rounded"
						>
							<IoPaperPlaneOutline className="text-lg text-white" />
						</Button>
					)}
				</div>
			</div>
		</div>
	);
};

export default FollowupSearchPanel;
