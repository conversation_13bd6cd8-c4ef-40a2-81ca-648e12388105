"use client";
import { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { IoMdAdd } from "react-icons/io";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "../ui/dialog";
import AddNewAccountDialogHeader from "./AddNewAccountDialogHeader";
import AddNewAccountForm, {
	useFormMethods,
	AccountFormValues,
} from "./AddNewAccountForm";
import { toast } from "sonner";
import { createClerkUser } from "@/lib/clerk";
import { RoleType, SiteAccessType } from "@/db/schema/enums";
import { createAccount } from "@/db/services/accounts.service";

// Add onAccountCreated callback prop
const AddNewAccount = ({
	role,
	onAccountCreated,
}: {
	role: RoleType;
	onAccountCreated?: (() => void) | null;
}) => {
	const [open, setOpen] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const formMethods = useFormMethods(role);

	const handleSubmit = async (values: AccountFormValues) => {
		// Prevent multiple submissions
		if (isSubmitting) return;

		setIsSubmitting(true);

		try {
			// 1. Create the user in our database
			// Convert form siteAccess string to array format
			const siteAccess = values.siteAccess
				? [values.siteAccess as SiteAccessType]
				: [];

			const dbResult = await createAccount({
				firstName: values.firstName,
				lastName: values.lastName,
				userName: values.alias,
				imageUrl:
					"https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18ydE0wTmlPQXhZUnBqaVNvVEV4Q3BoRXhUVW8iLCJyaWQiOiJ1c2VyXzJ0dHVremNpbWRGVXFPZDR0MEJjOXROSHJQMCJ9",
				email: values.emailAddress,
				role: values.userType as RoleType,
				subCategory: values.subCategory,
				companyName: values.companyName,
				mgExclusives: !!values.mgExclusives,
				communicationType: values.communicationType,
				membershipPlan: values.membershipPlan,
				startDate: values.startDate,
				endDate: values.endDate,
				siteAccess: siteAccess,
				geographicAccess: values.geographicAccess,
				// geographicAreaIds: [...],
				linkAccountIds: values.linkAccountIds || [], // Changed from linkAccountId to linkAccountIds
			});

			// Check if the database account creation was successful
			if (!dbResult.success) {
				console.error("Database creation error:", dbResult.error);

				// Check for account already exists messages
				if (dbResult.error?.includes("already exists")) {
					// This is an account existence error
					toast.error("Account creation failed", {
						description: dbResult.error,
					});

					// Handle errors in the form
					if (dbResult.error.includes("email")) {
						formMethods.setError("emailAddress", {
							type: "manual",
							message: "This email is already registered",
						});
					}

					if (dbResult.error.includes("username")) {
						formMethods.setError("alias", {
							type: "manual",
							message: "This username is already taken",
						});
					}

					return;
				}

				// Other database errors
				toast.error("Failed to create account", {
					description:
						dbResult.error ||
						"An error occurred while creating the account in the database",
				});
				return;
			}

			// 2. Create the user in Clerk
			const clerkResult = await createClerkUser({
				emailAddress: [values.emailAddress],
				firstName: values.firstName,
				lastName: values.lastName,
				username: values.alias,
				publicMetadata: {
					role: values.userType,
				},
			});

			// Check if the Clerk user creation failed
			if (clerkResult.status === "error") {
				console.log("Clerk creation error:", clerkResult);

				let errorMessage =
					clerkResult.message ||
					"There was an error creating the account in Clerk. Please try again.";

				if (clerkResult.code === "form_identifier_exists") {
					errorMessage = `${values.emailAddress} is already registered. Please use a different email address.`;

					// Set form error for email field
					formMethods.setError("emailAddress", {
						type: "manual",
						message:
							"This email is already registered in the authentication system",
					});
				}

				toast.error("Account creation failed", {
					description: errorMessage,
				});
				return;
			}

			// If we got here, both operations were successful
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Show success toast
			toast.success("Account created successfully", {
				description: "New user account has been added to the system",
			});

			// Close dialog
			setOpen(false);

			// Reset form after successful submission
			formMethods.reset();

			// Use the passed mutate function instead of global mutate
			if (onAccountCreated) {
				onAccountCreated();
			}
		} catch (error: any) {
			// This catch block handles unexpected errors
			console.error("Unexpected error:", error);

			toast.error("Account creation failed", {
				description: "An unexpected error occurred. Please try again.",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleFormSubmit = () => {
		// Prevent submission if already submitting
		if (isSubmitting) return;

		// Add a try-catch to detect any errors
		try {
			// console.log("Form state before submit:", formMethods.getValues());
			// console.log("Form errors:", formMethods.formState.errors);

			// Check if the form is valid before submitting
			if (Object.keys(formMethods.formState.errors).length > 0) {
				console.log("Form has validation errors");
				toast.error("Please fix the form errors before submitting");
				return;
			}

			formMethods.handleSubmit((data) => {
				// console.log("Form submitted with data:", data);
				handleSubmit(data);
			})();
		} catch (error) {
			console.error("Error during form submission:", error);
			setIsSubmitting(false);
		}
	};

	useEffect(() => {
		if (!open) {
			// Reset form and submission state after dialog is closed
			formMethods.reset();
			setIsSubmitting(false);
		}
	}, [open, formMethods]);

	return (
		<div>
			<Button
				className="text-lg font-bold font-manrope"
				onClick={() => setOpen(true)}
			>
				<IoMdAdd className="size-6" />
				Add New Account
			</Button>
			<Dialog open={open} onOpenChange={setOpen}>
				<DialogContent
					closeButton={false}
					className="sm:max-w-2xl md:max-w-4xl lg:max-w-7xl max-h-[90dvh] overflow-hidden flex flex-col"
				>
					<DialogTitle className="sr-only ">Add New Account</DialogTitle>
					<DialogDescription className="sr-only">
						Please fill out the form below to add a new user account
					</DialogDescription>
					<DialogHeader>
						<AddNewAccountDialogHeader
							closeDialog={() => setOpen(false)}
							onSubmit={handleFormSubmit}
							isSubmitting={isSubmitting}
						/>
					</DialogHeader>
					<AddNewAccountForm formMethods={formMethods} role={role} />
				</DialogContent>
			</Dialog>
		</div>
	);
};

export default AddNewAccount;
