"use client";
import { Fragment, useEffect } from "react";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { UserSelectType } from "@/db/services/users.service";
import { Button } from "../ui/button";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import {
	Pagination,
	PaginationContent,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
	PaginationEllipsis,
} from "@/components/ui/pagination";
import useSearchStore from "@/stores/search";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useUserSearch } from "@/hooks/useUserSearch";
import { RoleType } from "@/db/schema/enums";

const UsersTable = ({
	roleFilter,
	onMutateReady,
}: {
	roleFilter?: RoleType;
	onMutateReady?: (mutate: () => void) => void;
}) => {
	const pathname = usePathname();
	const { userSearch } = useSearchStore();

	// Use the custom hook for user search functionality
	const {
		users,
		pagination,
		isLoading,
		error,
		setCurrentPage,
		getPageNumbers,
		handlePrevious,
		handleNext,
		handlePageClick,
		mutate,
	} = useUserSearch({
		roleFilter,
		pageSize: 10,
		externalSearchTerm: userSearch,
	});

	// Expose mutate function to parent component
	useEffect(() => {
		if (onMutateReady && mutate) {
			onMutateReady(mutate);
		}
	}, [onMutateReady, mutate]);

	// Effect to reset page when search term changes
	useEffect(() => {
		setCurrentPage(1);
	}, [userSearch, setCurrentPage]);

	return (
		<div>
			<Table className="font-manrope">
				<TableHeader>
					<TableRow>
						<TableHead className="text-base font-normal text-neutral-500">
							Username
						</TableHead>
						<TableHead className="text-base font-normal text-neutral-500">
							User ID number
						</TableHead>
						<TableHead></TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{isLoading ? (
						<TableRow>
							<TableCell colSpan={3} className="py-16 text-base text-center">
								Loading users...
							</TableCell>
						</TableRow>
					) : error ? (
						<TableRow>
							<TableCell
								colSpan={3}
								className="py-16 text-base text-center text-red-500"
							>
								Failed to fetch users
							</TableCell>
						</TableRow>
					) : users.length === 0 ? (
						<TableRow>
							<TableCell colSpan={3} className="py-16 text-base text-center">
								{userSearch ? "No users match your search" : "No users found"}
							</TableCell>
						</TableRow>
					) : (
						users.map((user: UserSelectType) => (
							<TableRow key={user.id}>
								<TableCell className="flex flex-row items-center gap-2 text-base font-bold text-slate-900">
									{user.imageUrl && (
										<Avatar>
											<AvatarImage src={user.imageUrl} />
										</Avatar>
									)}
									{user.userName || "N/A"}
								</TableCell>
								<TableCell className="text-base font-normal text-slate-900">
									{user.id}
								</TableCell>
								<TableCell className="text-right">
									<Link href={`${pathname}/${user.id}`}>
										<Button
											variant={"secondary"}
											className="text-base font-normal text-slate-900"
										>
											Go to account
										</Button>
									</Link>
								</TableCell>
							</TableRow>
						))
					)}
				</TableBody>
			</Table>

			{!isLoading && !error && pagination.totalPages > 1 && (
				<div className="flex justify-center mt-4">
					<Pagination>
						<PaginationContent>
							<PaginationItem>
								<PaginationPrevious
									href="#"
									onClick={handlePrevious}
									className={
										pagination.currentPage <= 1
											? "pointer-events-none opacity-50"
											: ""
									}
								/>
							</PaginationItem>

							{getPageNumbers().map((page, i, array) => (
								<Fragment key={`page-group-${page}`}>
									{i > 0 && array[i] - array[i - 1] > 1 && (
										<PaginationItem key={`ellipsis-${i}`}>
											<PaginationEllipsis />
										</PaginationItem>
									)}
									<PaginationItem key={page}>
										<PaginationLink
											href="#"
											onClick={(e) => handlePageClick(e, page)}
											isActive={pagination.currentPage === page}
										>
											{page}
										</PaginationLink>
									</PaginationItem>
								</Fragment>
							))}

							<PaginationItem>
								<PaginationNext
									href="#"
									onClick={handleNext}
									className={
										pagination.currentPage >= pagination.totalPages
											? "pointer-events-none opacity-50"
											: ""
									}
								/>
							</PaginationItem>
						</PaginationContent>
					</Pagination>
				</div>
			)}
		</div>
	);
};

export default UsersTable;
