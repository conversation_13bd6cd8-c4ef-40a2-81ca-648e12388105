import { NextRequest, NextResponse } from "next/server";
import {
	getListingById,
	getListingBySlug,
	updateListing,
	deleteListing,
} from "@/db/services/listings.service";
import { auth } from "@clerk/nextjs/server";

/**
 * GET handler - Fetch a specific listing
 */
export async function GET(
	req: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;
		let listing;

		// Determine if id is a UUID or slug
		if (id.includes("-")) {
			// Try to get by slug first
			listing = await getListingBySlug(id);
			// If not found by slug, try to get by ID
			if (!listing) {
				listing = await getListingById(id);
			}
		} else {
			listing = await getListingById(id);
		}

		if (!listing) {
			return NextResponse.json({ error: "Listing not found" }, { status: 404 });
		}

		return NextResponse.json(listing);
	} catch (error) {
		console.error("Error fetching listing:", error);
		return NextResponse.json(
			{ error: "Failed to fetch listing" },
			{ status: 500 }
		);
	}
}

/**
 * PATCH handler - Update a listing
 */
export async function PATCH(
	req: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { userId } = await auth();
		if (!userId) {
			return NextResponse.json(
				{ error: "Authentication required" },
				{ status: 401 }
			);
		}

		const { id } = await params;
		const body = await req.json();

		// Validate body.data exists and is not empty
		if (
			!body.data ||
			typeof body.data !== "object" ||
			Object.keys(body.data).length === 0
		) {
			return NextResponse.json(
				{
					error: "Invalid request: data field is required and cannot be empty",
				},
				{ status: 400 }
			);
		}

		// Remove the redundant getListingById call here since updateListing will handle it
		const updatedListing = await updateListing(
			id,
			body.data,
			body.reviewStatus
		);
		return NextResponse.json(updatedListing);
	} catch (error) {
		console.error("Error updating listing:", error);
		return NextResponse.json(
			{
				error: "Failed to update listing",
				message: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

/**
 * DELETE handler - Delete a listing
 */
export async function DELETE(
	req: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { userId } = await auth();
		if (!userId) {
			return NextResponse.json(
				{ error: "Authentication required" },
				{ status: 401 }
			);
		}

		const { id } = await params;
		await deleteListing(id);

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting listing:", error);
		return NextResponse.json(
			{
				error: "Failed to delete listing",
				message: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
