"use client";
import use<PERSON><PERSON> from "swr";
import { fetcher } from "@/lib/utils";
import { ListingData } from "@/types/listing";
import { useState, useEffect } from "react";
import { VisibilityType } from "@/db/schema";

// Search results type
interface SearchResults {
	matchedResults: ListingData[];
	recommendedResults: ListingData[];
}

// Hook return type
interface UseSearchQueryReturn {
	searchResults: SearchResults | undefined;
	isLoading: boolean;
	isError: any;
}

export const useSearchQuery = (
	query: string,
	delay: number = 500,
	visibility: VisibilityType = "public"
): UseSearchQueryReturn => {
	const [debouncedQuery, setDebouncedQuery] = useState("");

	// Debounce the query to avoid frequent API calls
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedQuery(query);
		}, delay);

		return () => clearTimeout(timer);
	}, [query, delay]);

	// Only make request if debounced query is not empty
	const shouldFetch = debouncedQuery && debouncedQuery.trim() !== "";

	const { data, error } = useSWR(
		shouldFetch
			? `/api/searchQuery?query=${encodeURIComponent(debouncedQuery)}&visibility=${visibility}`
			: null,
		fetcher,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			dedupingInterval: 300000, // Cache for 5 minutes
			refreshInterval: 0, // Don't auto-refresh search results
			keepPreviousData: true,
			errorRetryCount: 2,
			errorRetryInterval: 1000,
		}
	);

	return {
		searchResults: data,
		isLoading: Boolean(shouldFetch && !error && !data),
		isError: error,
	};
};
