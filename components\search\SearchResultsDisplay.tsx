"use client";
import { Loader2 } from "lucide-react";
import useSearchStore from "@/stores/search";
import CaseDialog from "../global/CaseDialog";
import PublishedListingCard from "../dashboard/cards/PublishedListingCard";
import { useSearchQuery } from "@/hooks/useSearchQuery";
import { useEffect } from "react";

export function SearchResultsDisplay() {
	const { results, hasSearched, searchValue, setResults } = useSearchStore();
	const {
		searchResults,
		isLoading: searchQueryLoading,
		isError: searchQueryError,
	} = useSearchQuery(searchValue);

	// Update store results when searchResults are available
	useEffect(() => {
		if (
			searchResults?.matchedResults &&
			searchResults.matchedResults.length > 0
		) {
			setResults(searchResults.matchedResults);
		}
	}, [searchResults, setResults]);

	// Show loading if either store loading or search query loading
	const isLoadingAny = searchQueryLoading;

	if (isLoadingAny) {
		return (
			<div className="flex items-center justify-center p-8">
				<Loader2 className="w-8 h-8 animate-spin text-primary" />
			</div>
		);
	}

	// Handle both store error and search query error
	if (searchQueryError) {
		const errorMessage = searchQueryError?.message || "Search failed";
		return <div className="p-4 text-sm text-red-500">{errorMessage}</div>;
	}

	// Show matched results first, then fallback to recommended if no matches
	const displayResults =
		results && results.length > 0 ? results : searchResults?.matchedResults;
	const hasMatches = displayResults && displayResults.length > 0;
	const hasRecommendedResults =
		searchResults?.recommendedResults &&
		searchResults.recommendedResults.length > 0;

	if (hasSearched && !hasMatches) {
		return (
			<div className="w-full">
				<div className="flex flex-col items-center justify-center p-8 text-center">
					<div className="my-12 text-xl text-gray-600 font-manrope">
						No Results Found.
					</div>
				</div>

				{hasRecommendedResults && (
					<div>
						<p className="mb-6 text-base text-gray-500">
							The following results are similar to what you searched for:
						</p>
						<div className="grid grid-cols-1 gap-4 px-2 lg:grid-cols-2 xl:grid-cols-3">
							{searchResults.recommendedResults.map((result, index) => {
								const listingData = result;
								return (
									<CaseDialog
										key={result.id}
										data={listingData}
										ind={index}
										backPage="search"
									>
										<PublishedListingCard item={listingData} />
									</CaseDialog>
								);
							})}
						</div>
					</div>
				)}
			</div>
		);
	}

	if (!displayResults) {
		return null;
	}

	return (
		<div className="w-full">
			<div className="grid grid-cols-1 gap-4 px-2 lg:grid-cols-2 xl:grid-cols-3">
				{displayResults.map((result, index) => {
					const listingData = result;
					return (
						<CaseDialog
							key={result.id}
							data={listingData}
							ind={index}
							backPage="search"
						>
							<PublishedListingCard item={listingData} />
						</CaseDialog>
					);
				})}
			</div>
		</div>
	);
}
