import { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { FaRegCalendarAlt, FaClock } from "react-icons/fa";
import { Checkbox } from "@/components/ui/checkbox";
import { format, isBefore, set } from "date-fns";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface User {
	id: string;
	name: string;
}

interface MeetingFormProps {
	onSchedule: (data: MeetingFormData) => void;
	onCancel: () => void;
	availableUsers: User[];
}

export interface MeetingFormData {
	meetingTitle: string;
	selectedDate: Date;
	startTime: string;
	endTime: string;
	timezone: string;
	selectedAttendees: string[];
}

// Get all IANA timezones (browser-native, with fallback)
const timezones =
	typeof Intl !== "undefined" && Intl.supportedValuesOf
		? Intl.supportedValuesOf("timeZone")
		: [
				"UTC",
				"America/New_York",
				"America/Chicago",
				"America/Denver",
				"America/Los_Angeles",
				"Europe/London",
				"Europe/Paris",
				"Europe/Berlin",
				"Asia/Tokyo",
				"Asia/Shanghai",
				"Asia/Kolkata",
				"Australia/Sydney",
			];

const MeetingForm = ({
	onSchedule,
	onCancel,
	availableUsers,
}: MeetingFormProps) => {
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(
		new Date()
	);
	const [meetingTitle, setMeetingTitle] = useState("MG Video Conference");
	const [startTime, setStartTime] = useState<string>(() => {
		const now = new Date();
		const nextHour = new Date(now.setHours(now.getHours() + 1, 0, 0, 0));
		return nextHour.toLocaleTimeString("en-US", {
			hour12: false,
			hour: "2-digit",
			minute: "2-digit",
		});
	});
	const [endTime, setEndTime] = useState<string>(() => {
		const now = new Date();
		const nextHourPlusThirty = new Date(
			now.setHours(now.getHours() + 1, 30, 0, 0)
		);
		return nextHourPlusThirty.toLocaleTimeString("en-US", {
			hour12: false,
			hour: "2-digit",
			minute: "2-digit",
		});
	});
	const [timezone, setTimezone] = useState<string>("");
	const [calendarOpen, setCalendarOpen] = useState(false);
	const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);
	const [attendeesPopoverOpen, setAttendeesPopoverOpen] = useState(false);
	const [errors, setErrors] = useState<{ [key: string]: string }>({});

	// Set default timezone to local timezone
	useEffect(() => {
		try {
			const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
			setTimezone(localTimezone);
		} catch (e) {
			setTimezone("UTC");
			console.error(e);
		}
	}, []);

	const validateForm = () => {
		const newErrors: { [key: string]: string } = {};

		if (!meetingTitle.trim()) {
			newErrors.meetingTitle = "Meeting title is required";
		}

		if (!selectedDate) {
			newErrors.date = "Date is required";
		} else {
			const now = new Date();
			const selectedDateTime = set(selectedDate, {
				hours: parseInt(startTime.split(":")[0]),
				minutes: parseInt(startTime.split(":")[1]),
			});

			if (isBefore(selectedDateTime, now)) {
				newErrors.date = "Start time must be in the future";
			}
		}

		if (startTime >= endTime) {
			newErrors.time = "End time must be after start time";
		}

		if (selectedAttendees.length === 0) {
			newErrors.attendees = "At least one attendee must be selected";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = () => {
		if (!validateForm() || !selectedDate) return;

		onSchedule({
			meetingTitle,
			selectedDate,
			startTime,
			endTime,
			timezone,
			selectedAttendees,
		});
	};

	const toggleAttendee = (id: string) => {
		setSelectedAttendees((prev) =>
			prev.includes(id) ? prev.filter((a) => a !== id) : [...prev, id]
		);
	};

	return (
		<div className="w-full space-y-6">
			{/* Meeting Title */}
			<div className="space-y-2">
				<Label htmlFor="meeting-title">Meeting Title</Label>
				<Input
					id="meeting-title"
					className={`w-full p-3 rounded-xl border ${
						errors.meetingTitle ? "border-red-500" : "border-gray-200"
					} bg-white shadow-sm`}
					value={meetingTitle}
					onChange={(e) => setMeetingTitle(e.target.value)}
					placeholder="Enter meeting title"
				/>
				{errors.meetingTitle && (
					<p className="text-sm text-red-500">{errors.meetingTitle}</p>
				)}
			</div>

			{/* Date Selector */}
			<div className="space-y-2">
				<Label>Date</Label>
				<Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							className={`w-full justify-start text-left font-normal cursor-pointer bg-white ${
								errors.date ? "border-red-500" : "border-gray-200"
							} hover:bg-gray-50`}
						>
							<FaRegCalendarAlt className="w-4 h-4 mr-2" />
							{selectedDate ? (
								format(selectedDate, "PPP")
							) : (
								<span>Pick a date</span>
							)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<Calendar
							mode="single"
							selected={selectedDate}
							onSelect={(date) => {
								setSelectedDate(date);
								setCalendarOpen(false);
							}}
							autoFocus
						/>
					</PopoverContent>
				</Popover>
				{errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
			</div>

			{/* Time Selection */}
			<div className="grid grid-cols-2 gap-4">
				<div className="space-y-2">
					<Label htmlFor="start-time">Start Time</Label>
					<div className="relative">
						<Input
							id="start-time"
							type="time"
							className="pl-10 bg-white border-gray-200"
							value={startTime}
							onChange={(e) => setStartTime(e.target.value)}
						/>
						<FaClock className="absolute text-gray-400 -translate-y-1/2 left-3 top-1/2" />
					</div>
				</div>
				<div className="space-y-2">
					<Label htmlFor="end-time">End Time</Label>
					<div className="relative">
						<Input
							id="end-time"
							type="time"
							className="pl-10 bg-white border-gray-200"
							value={endTime}
							onChange={(e) => setEndTime(e.target.value)}
						/>
						<FaClock className="absolute text-gray-400 -translate-y-1/2 left-3 top-1/2" />
					</div>
				</div>
			</div>
			{errors.time && <p className="text-sm text-red-500">{errors.time}</p>}

			{/* Timezone Selector */}
			<div className="space-y-2">
				<Label htmlFor="timezone">Timezone</Label>
				<Select value={timezone} onValueChange={setTimezone}>
					<SelectTrigger className="w-full bg-white border-gray-200">
						<SelectValue placeholder="Select timezone" />
					</SelectTrigger>
					<SelectContent className="max-h-[300px]">
						{timezones.map((tz) => (
							<SelectItem key={tz} value={tz}>
								{tz}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Attendees */}
			<div className="space-y-2">
				<Label>Attendees</Label>
				<Popover
					open={attendeesPopoverOpen}
					onOpenChange={setAttendeesPopoverOpen}
				>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							className="justify-start w-full font-normal text-left bg-white border-gray-200 cursor-pointer hover:bg-gray-50"
						>
							{selectedAttendees.length === 0
								? "Add invitees"
								: availableUsers
										.filter((u) => selectedAttendees.includes(u.id))
										.map((u) => u.name)
										.join(", ")}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-full p-0" align="start">
						<div className="max-h-[200px] overflow-y-auto p-2">
							{availableUsers.length > 0 &&
								availableUsers.map((user) => (
									<div
										key={user.id}
										className="flex items-center p-2 space-x-2 rounded-md cursor-pointer hover:bg-gray-100"
										onClick={() => toggleAttendee(user.id)}
									>
										<Checkbox
											checked={selectedAttendees.includes(user.id)}
											onCheckedChange={() => toggleAttendee(user.id)}
											id={`attendee-${user.id}`}
										/>
										<label
											htmlFor={`attendee-${user.id}`}
											className="flex-1 cursor-pointer"
										>
											{user.name}
										</label>
									</div>
								))}
						</div>
					</PopoverContent>
				</Popover>
				{errors.attendees && (
					<p className="text-sm text-red-500">{errors.attendees}</p>
				)}
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end pt-4 space-x-4">
				<Button
					variant="outline"
					onClick={onCancel}
					className="bg-white cursor-pointer hover:bg-gray-100"
				>
					Cancel
				</Button>
				<Button
					onClick={handleSubmit}
					disabled={
						!selectedDate || !meetingTitle || selectedAttendees.length === 0
					}
					className="bg-[#10162D] hover:bg-[#10162D]/90 cursor-pointer"
				>
					Schedule Call
				</Button>
			</div>
		</div>
	);
};

export default MeetingForm;
