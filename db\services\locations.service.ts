"use server";
import { eq } from "drizzle-orm";
import { db } from "../index";
import { locations } from "../schema/locations";

export type LocationType = typeof locations.$inferInsert;
export type LocationSelectType = typeof locations.$inferSelect;

/**
 * Gets location by listing ID
 */
export const getLocationByListingId = async (
	listingId: string
): Promise<LocationSelectType | null> => {
	try {
		const result = await db
			.select()
			.from(locations)
			.where(eq(locations.listingId, listingId));

		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting location:", error);
		return null;
	}
};

/**
 * Creates a location for a listing
 */
export const createLocation = async (
	locationData: LocationType
): Promise<LocationSelectType | null> => {
	try {
		const [newLocation] = await db
			.insert(locations)
			.values(locationData)
			.returning();

		return newLocation;
	} catch (error) {
		console.error("Error creating location:", error);
		return null;
	}
};

/**
 * Updates a location
 */
export const updateLocation = async (
	listingId: string,
	updates: Partial<Omit<LocationType, "id" | "listingId">>
): Promise<boolean> => {
	try {
		await db
			.update(locations)
			.set({
				...updates,
				updatedAt: new Date(),
			})
			.where(eq(locations.listingId, listingId));

		return true;
	} catch (error) {
		console.error("Error updating location:", error);
		return false;
	}
};

/**
 * Deletes a location
 */
export const deleteLocation = async (listingId: string): Promise<boolean> => {
	try {
		await db.delete(locations).where(eq(locations.listingId, listingId));

		return true;
	} catch (error) {
		console.error("Error deleting location:", error);
		return false;
	}
};

/**
 * Creates or updates a location for a listing with partial data support
 */
export const upsertLocation = async (
	listingId: string,
	data: Partial<Omit<LocationType, "listingId">>
): Promise<LocationSelectType | null> => {
	try {
		// Check if location already exists
		const existingLocation = await getLocationByListingId(listingId);

		if (existingLocation) {
			// Only update if there are fields to update
			if (Object.keys(data).length > 0) {
				await updateLocation(listingId, data);
			}
			return await getLocationByListingId(listingId);
		} else {
			// For new location, we need at least some required fields
			// Create new record only if we have essential data
			if (Object.keys(data).length > 0) {
				return await createLocation({
					...data,
					listingId,
				} as LocationType);
			}
			return null;
		}
	} catch (error) {
		console.error("Error upserting location:", error);
		return null;
	}
};

/**
 * Searches listings by location fields (address, city, state, country, etc.)
 * Returns listing IDs that match the search term in any location field
 */
export const searchListingsByLocation = async (
	searchTerm: string
): Promise<string[]> => {
	try {
		if (!searchTerm || searchTerm.trim().length === 0) {
			return [];
		}

		// Use query API to search locations and get associated listing IDs
		const matchingLocations = await db.query.locations.findMany({
			where: (locations, { or, ilike }) =>
				or(
					ilike(locations.address, `%${searchTerm}%`),
					ilike(locations.city, `%${searchTerm}%`),
					ilike(locations.state, `%${searchTerm}%`),
					ilike(locations.country, `%${searchTerm}%`),
					ilike(locations.zipCode, `%${searchTerm}%`),
					ilike(locations.neighborhood, `%${searchTerm}%`),
					ilike(locations.schoolDistrict, `%${searchTerm}%`)
				),
			columns: { listingId: true },
		});

		return Array.from(
			new Set(matchingLocations.map((location) => location.listingId))
		);
	} catch (error) {
		console.error("Error searching listings by location:", error);
		return [];
	}
};
