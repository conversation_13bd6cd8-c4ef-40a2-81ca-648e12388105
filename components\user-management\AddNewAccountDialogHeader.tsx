"use client";
import Logo from "../ui/Logo";
import { Button } from "../ui/button";
import { MdChevronLeft } from "react-icons/md";

type AddNewAccountDialogHeaderProps = {
	closeDialog: () => void;
	onSubmit: () => void;
	isSubmitting?: boolean;
};

const AddNewAccountDialogHeader = ({
	closeDialog,
	onSubmit,
	isSubmitting = false,
}: AddNewAccountDialogHeaderProps) => {
	const handleSubmitClick = (e: React.MouseEvent) => {
		// Prevent any default behavior
		e.preventDefault();
		e.stopPropagation();

		// Don't submit if already submitting
		if (isSubmitting) return;

		// Call the submit handler
		onSubmit();
	};

	return (
		<div className="flex flex-row items-center justify-between w-full gap-4">
			<Button
				variant={"secondary"}
				className="flex flex-row items-center justify-center"
				onClick={closeDialog}
				disabled={isSubmitting}
			>
				<MdChevronLeft />
				<span className="text-sm font-inter">Back</span>
			</Button>

			<Logo className="w-auto h-12" />
			<Button
				variant={"default"}
				className="flex justify-end cursor-pointer"
				onClick={handleSubmitClick}
				type="button"
				disabled={isSubmitting}
			>
				{isSubmitting ? "Submitting..." : "Submit"}
			</Button>
		</div>
	);
};

export default AddNewAccountDialogHeader;
