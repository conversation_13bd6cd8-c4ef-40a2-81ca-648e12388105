import { Skeleton } from "@/components/ui/skeleton";

const CallDetailsSkeleton = () => {
	return (
		<div className="flex flex-row w-full text-center">
			{/* Image skeleton - matches fixed dimensions from other components */}
			<Skeleton className="w-[262px] h-[183px] rounded-tl-[11px]" />

			{/* Content skeleton */}
			<div className="flex flex-col w-full gap-4 p-5 shadow-lg">
				<Skeleton className="w-1/2 h-4 mb-2" />
				<Skeleton className="w-3/4 h-4 mb-2" />
				<Skeleton className="w-1/4 h-4 mb-2" />
				<Skeleton className="w-1/2 h-4 mb-2" />
			</div>
		</div>
	);
};

export default CallDetailsSkeleton;
