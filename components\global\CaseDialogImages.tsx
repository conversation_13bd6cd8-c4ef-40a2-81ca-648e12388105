"use client";

import Image from "next/image";
import { Button } from "../ui/button";
import { Skeleton } from "../ui/skeleton";
import { useState } from "react";
import useAppStore from "@/stores/app";
import { handleImageClickForIframe } from "@/utils/imageHandlers";

const CaseDialogImages = ({ media }: { media: any[] }) => {
	const { setDialogShowPhotos, setDialogShowIframe, setIframeUrl, setDialogShowListings } =
		useAppStore();
	const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());

	// Handle image load completion
	const handleImageLoad = (index: number) => {
		setLoadedImages((prev) => new Set(prev).add(index));
	};

	// If no media, show placeholder
	if (!media || media.length === 0) {
		return (
			<div className="grid w-full h-64 grid-cols-1 gap-2">
				<div className="relative flex-1">
					<Skeleton className="w-full h-full" />
				</div>
			</div>
		);
	}

	return (
		<div className="grid w-full h-64 grid-cols-4 grid-rows-2 gap-2">
			{media.slice(0, 4).map((item, i) => (
				<div
					key={item.id}
					className={`gap-y-2 flex flex-col ${
						i === 0
							? "col-span-2 row-span-2"
							: i === 2
								? "col-span-1 row-span-2"
								: "col-span-1 row-span-1"
					}`}
				>
					<div
						className="relative flex-1 cursor-pointer"
						onClick={() =>
							handleImageClickForIframe(item, setIframeUrl, setDialogShowIframe)
						}
					>
						{!loadedImages.has(i) && (
							<Skeleton className="absolute inset-0 w-full h-full" />
						)}
						<Image
							src={
								item.preview
									? `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${item.preview}`
									: item.source || "https://placehold.co/600x400/jpg"
							}
							alt={`Property image ${i + 1}`}
							className="object-cover w-full h-full"
							fill
							sizes={
								i === 0
									? "(max-width: 768px) 100vw, 50vw"
									: "(max-width: 768px) 100vw, 25vw"
							}
							onLoad={() => handleImageLoad(i)}
						/>
					</div>
					{i === 2 && media.length > 4 && (
						<Button
							variant={"secondary"}
							className="text-xs rounded-xs font-inter"
							onClick={() => {
								setDialogShowPhotos(true);
								setDialogShowListings(true);
								setDialogShowIframe(false);
							}}
						>
							See all photos
						</Button>
					)}
				</div>
			))}
		</div>
	);
};

export default CaseDialogImages;
