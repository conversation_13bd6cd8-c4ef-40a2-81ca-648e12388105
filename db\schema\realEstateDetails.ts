import { varchar, numeric, text, uniqueIndex } from "drizzle-orm/pg-core";
import { pgTable } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { uuidField, timestamps } from "../columns.helpers";
import { listings } from "./listings";
import { propertyTypeEnum } from "./enums";

export const realEstateDetails = pgTable(
	"real_estate_details",
	{
		id: uuidField(),
		listingId: text("listing_id")
			.notNull()
			.references(() => listings.id, { onDelete: "cascade" }),
		propertyType: propertyTypeEnum("property_type").default("house").notNull(),
		bedrooms: numeric("bedrooms", { precision: 5, scale: 1 }).notNull(),
		bathrooms: numeric("bathrooms", { precision: 5, scale: 1 }).notNull(),
		homeSize: numeric("home_size", { precision: 15, scale: 2 }).notNull(),
		homeSizeUnit: varchar("home_size_unit", { length: 10 })
			.default("sqft")
			.notNull(),
		lotSize: numeric("lot_size", { precision: 15, scale: 2 }),
		lotSizeUnit: varchar("lot_size_unit", { length: 10 }).default("sqft"),
		parkingType: varchar("parking_type", { length: 30 }),
		parkingSpaces: numeric("parking_spaces", { precision: 5, scale: 0 }),
		yearBuilt: numeric("year_built", { precision: 4, scale: 0 }),
		historicalDesignation: varchar("historical_designation", {
			length: 50,
		}).default("None"),
		// Keeping some of the existing fields that might be valuable
		architectName: varchar("architect_name", { length: 100 }),
		interiorDesigner: varchar("interior_designer", { length: 100 }),
		views: varchar("views", { length: 100 }),
		historicDetails: text("historic_details"),
		hoaFees: numeric("hoa_fees", { precision: 10, scale: 2 }),
		taxes: numeric("taxes", { precision: 10, scale: 2 }),
		...timestamps,
	},
	(table) => [uniqueIndex("re_listing_id_idx").on(table.listingId)]
);

// Add relation definition
export const realEstateDetailsRelations = relations(
	realEstateDetails,
	({ one }) => ({
		listing: one(listings, {
			fields: [realEstateDetails.listingId],
			references: [listings.id],
		}),
	})
);
