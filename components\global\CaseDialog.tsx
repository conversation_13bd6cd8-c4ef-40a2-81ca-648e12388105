"use client";
import {
	<PERSON><PERSON>,
	<PERSON>alogContent,
	<PERSON><PERSON><PERSON>ead<PERSON>,
	Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { useEffect, useState } from "react";
import { incrementViewCount } from "@/db/services/listings.service";

import CaseDialogImages from "./CaseDialogImages";
import CaseDialogHeader from "./CaseDialogHeader";
import CaseDialogContent from "./CaseDialogContent";
import CaseDialogMessageBox from "./CaseDialogMessageBox";
import useAppStore from "@/stores/app";
import CaseDialogGallery from "./CaseDialogGallery";
import { ListingData } from "@/types/listing";
import { KeyedMutator } from "swr";
import { ReviewStatusType } from "@/db/schema/enums";

interface CaseDialogProps {
	children: React.ReactNode;
	data: ListingData;
	ind: number;
	backPage?: string;
	isFavored?: boolean;
	mutate?: KeyedMutator<any>;
	status?: ReviewStatusType | ReviewStatusType[];
	onEdit?: (listingId: string) => void; // Add onEdit prop
}

const CaseDialog = ({
	children,
	data,
	ind,
	backPage,
	mutate,
	status,
	onEdit, // Destructure onEdit prop
}: CaseDialogProps) => {
	const {
		dialogShowPhotos,
		setDialogShowPhotos,
		dialogShowIframe,
		setDialogShowIframe,
		iframeUrl,
		setIsDialogOpen,
	} = useAppStore();
	const [isOpen, setIsOpen] = useState(false);

	// Increment view count when the dialog is opened
	useEffect(() => {
		if (isOpen) {
			// Increment view count only when dialog is opened
			incrementViewCount(data.id).catch((error) =>
				console.error("Failed to increment view count:", error)
			);
		}
	}, [isOpen, data.id]);

	const handleOpenChange = (open: boolean) => {
		setIsOpen(open); // Track dialog open state
		setIsDialogOpen(open); // Update app store state

		if (!open) {
			// reset dialogShowPhotos and dialogShowIframe
			setTimeout(() => {
				setDialogShowPhotos(false);
				setDialogShowIframe(false);
			}, 1000);
		}
	};

	return (
		<Dialog onOpenChange={handleOpenChange}>
			<DialogTrigger asChild>
				<div
					className={`${
						ind === 0 && backPage === "home" ? "col-span-2" : "col-span-1"
					}`}
				>
					{children}
				</div>
			</DialogTrigger>
			<DialogContent
				closeButton={false}
				className="sm:max-w-2xl md:max-w-4xl lg:max-w-7xl max-h-[90dvh] overflow-hidden flex flex-col"
			>
				<DialogHeader>
					<CaseDialogHeader
						backPage={backPage}
						listingId={data.id}
						isFavored={data.isFavored}
						listingReviewRequestId={data.listingReviewRequest?.id}
						mutate={mutate}
						reviewerType={data.listingReviewRequest?.reviewerType}
						ownerId={data.listingReviewRequest?.userId}
						status={status}
						onEdit={onEdit} // Pass onEdit prop
					/>
				</DialogHeader>
				{dialogShowIframe ? (
					<iframe
						src={iframeUrl}
						className="w-full h-[80vh] border-0"
						allow="autoplay;"
						allowFullScreen
						title="Embedded content"
					/>
				) : dialogShowPhotos ? (
					<CaseDialogGallery media={data.media} />
				) : (
					<div className="flex-1 min-h-0 pr-1 scrollable">
						<CaseDialogImages media={data.media} />
						<div className="grid grid-cols-1 gap-2 py-2 md:grid-cols-4">
							<CaseDialogContent data={data} backPage={backPage} />
							{data.user && (
								<CaseDialogMessageBox
									backPage={backPage}
									listingId={data.id}
									listingOwner={data.user}
									previewUrl={
										data.media && data.media.length > 0 && data.media[0].preview
											? `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${data.media[0].preview}`
											: (data.media &&
													data.media.length > 0 &&
													data.media[0].source) ||
												"https://placehold.co/600x400/jpg"
									}
									data={data}
								/>
							)}
						</div>
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
};

export default CaseDialog;
