"use client";
import { SlLocationPin } from "react-icons/sl";
import { useState } from "react";
import CaseDialogAmenities from "./CaseDialogAmenities";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import CaseDialogDetails from "./CaseDialogDetails";
import CaseDialogMap from "./CaseDialogMap";
import { Button } from "../ui/button";
import { formatSimplePrice } from "@/lib/utils";
import { ListingData } from "@/types/listing";
import { TiptapEditor } from "../ui/tiptap-editor";
import { countries } from "countries-list";

const CaseDialogContent = ({
	data,
	backPage,
}: {
	data: ListingData;
	backPage?: string;
}) => {
	// Add state to track map visibility
	const [showMap, setShowMap] = useState(false);

	const toggleMap = () => {
		setShowMap((prev) => !prev);
	};

	return (
		<div className="flex flex-col gap-4 md:col-span-3">
			<DialogTitle className="py-2 text-4xl font-normal truncate font-lora max-w-5/6">
				{data.title}
			</DialogTitle>
			<DialogDescription className="sr-only">
				Property listing details for {data.title}
			</DialogDescription>
			<div>
				{data.location?.address && (
					<p className="text-base font-normal text-slate-900 font-manrope">
						{data.hideStreetAddress ? "Address hidden" : data.location.address}
					</p>
				)}
				{(data.location?.city ||
					data.location?.state ||
					data.location?.country) && (
					<p className="text-2xl font-normal leading-loose text-slate-900 font-lora">
						{[
							data.location.city,
							data.location.state,
							data.location.country &&
								countries[
									data.location.country as unknown as keyof typeof countries
								]?.name,
						]
							.filter(Boolean)
							.join(", ")}
					</p>
				)}
			</div>
			{backPage !== "MG Exclusives" && (
				<>
					{data.price && (
						<div className="text-xl font-bold leading-7 text-slate-900 font-manrope">
							{formatSimplePrice(Number(data.price))} {data.currency}
						</div>
					)}
					{/* Only show map button and map component when address exists */}
					{data.location?.address && (
						<div>
							<div>
								<Button
									className="rounded-sm bg-zinc-100"
									variant={"secondary"}
									onClick={toggleMap}
								>
									<SlLocationPin />
									<span className="text-base font-normal font-manrope">
										{showMap ? "Close map" : "View area"}
									</span>
								</Button>
							</div>
							{/* Render map component with visibility control instead of conditional rendering */}
							<div
								style={{
									visibility: showMap ? "visible" : "hidden",
									height: showMap ? "auto" : "0",
									overflow: "hidden",
								}}
							>
								<CaseDialogMap
									address={data.location?.address || undefined}
									city={data.location?.city || undefined}
									state={data.location?.state || undefined}
									country={data.location?.country || undefined}
									hideStreetAddress={data.hideStreetAddress}
								/>
							</div>
						</div>
					)}
				</>
			)}

			{data.description && (
				<div className="text-lg text-black font-manrope">
					<TiptapEditor value={data.description} />
				</div>
			)}

			{backPage !== "MG Exclusives" && (
				<>
					<CaseDialogAmenities amenitiesData={data.amenities} />
					<CaseDialogDetails data={data.realEstateDetails} />
				</>
			)}
		</div>
	);
};

export default CaseDialogContent;
