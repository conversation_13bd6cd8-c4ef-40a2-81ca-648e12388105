"use server";
import { eq, count, ilike, and, or, ne } from "drizzle-orm";
import { db } from "../index";
import { users } from "../schema/users";
import { auth } from "@clerk/nextjs/server";
import { RoleType } from "../schema/enums";

export type UserType = typeof users.$inferInsert;
export type UserSelectType = typeof users.$inferSelect;

export const getAllUsers = async (page: number = 1, pageSize: number = 10) => {
	try {
		const offset = (page - 1) * pageSize;

		const usersResult = await db.query.users.findMany({
			limit: pageSize,
			offset: offset,
			orderBy: (users, { desc }) => [desc(users.createdAt)],
		});

		const totalResult = await db.select({ count: count() }).from(users);
		const total = totalResult[0].count;

		return {
			users: usersResult,
			pagination: {
				total,
				pageSize,
				currentPage: page,
				totalPages: Math.ceil(total / pageSize),
			},
		};
	} catch (error) {
		console.error("Database error in getAllUsers:", error);
		return {
			users: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

export const getUserById = async (id: string) => {
	try {
		return await db.query.users.findFirst({
			where: eq(users.id, id),
		});
	} catch (error) {
		console.error("Database error in getUserById:", error);
		return null;
	}
};

export const getUserByClerkId = async (clerkId: string) => {
	try {
		// Input validation
		if (!clerkId || typeof clerkId !== 'string' || clerkId.trim() === '') {
			console.error("Invalid clerkId provided:", clerkId);
			return null;
		}

		const trimmedClerkId = clerkId.trim();

		const user = await db.query.users.findFirst({
			where: eq(users.clerkId, trimmedClerkId),
		});

		if (!user) {
			console.log("No user found with clerkId:", trimmedClerkId);
		}

		return user;
	} catch (error) {
		// Enhanced error logging with more context
		console.error("Database error in getUserByClerkId:", {
			clerkId,
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString(),
		});

		// Check for specific database errors
		if (error instanceof Error) {
			if (error.message.includes('connection')) {
				console.error("Database connection issue detected");
			} else if (error.message.includes('timeout')) {
				console.error("Database query timeout detected");
			} else if (error.message.includes('syntax')) {
				console.error("SQL syntax error detected");
			}
		}

		return null;
	}
};

export const getUserByEmail = async (email: string) => {
	try {
		console.log(">>>", email);
		// Direct query on users table since email is now a field in users
		return await db.query.users.findFirst({
			where: eq(users.email, email),
		});
	} catch (error) {
		console.error("Database error in getUserByEmail:", error);
		return null;
	}
};

export const getCurrentUser = async () => {
	try {
		const { userId: clerkId } = await auth();
		if (!clerkId) {
			throw new Error("You must be signed in");
		}
		return await db.query.users.findFirst({
			where: eq(users.clerkId, clerkId),
		});
	} catch (error) {
		console.error("Database error in getCurrentUser:", error);
	}
};

export const createUser = async (user: UserType) => {
	try {
		// Insert the user
		const [newUser] = await db.insert(users).values(user).returning();
		return newUser;
	} catch (error) {
		console.error("Database error in createUser:", error);
		throw new Error(
			`Failed to create user: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

export const updateUser = async (id: string, user: Partial<UserType>) => {
	try {
		return await db
			.update(users)
			.set({ ...user, updatedAt: new Date() })
			.where(eq(users.id, id));
	} catch (error) {
		console.error("Database error in updateUser:", error);
		throw new Error(
			`Failed to update user: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

export const deleteUser = async (id: string) => {
	try {
		return await db.delete(users).where(eq(users.id, id));
	} catch (error) {
		console.error("Database error in deleteUser:", error);
		throw new Error(
			`Failed to delete user: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

// Remove or update email-related methods since userEmails table no longer exists
export const updateUserEmail = async (
	userId: string,
	email: string,
	isVerified: boolean = false
) => {
	try {
		return await db
			.update(users)
			.set({
				email: email,
				emailVerified: isVerified,
				updatedAt: new Date(),
			})
			.where(eq(users.id, userId));
	} catch (error) {
		console.error("Database error in updateUserEmail:", error);
		throw new Error(
			`Failed to update user email: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

interface SearchUsersParams {
	searchTerm?: string;
	page?: number;
	pageSize?: number;
	roleFilter?: RoleType | RoleType[];
	excludeUserId?: string; // Add parameter to exclude specific user
}

export const searchUsers = async ({
	searchTerm = "",
	page = 1,
	pageSize = 10,
	roleFilter,
	excludeUserId,
}: SearchUsersParams = {}) => {
	try {
		const offset = (page - 1) * pageSize;
		const hasSearchTerm = searchTerm && searchTerm.trim().length > 0;

		// Try to parse the search term as an ID (integer)
		const searchId =
			hasSearchTerm && !isNaN(parseInt(searchTerm)) ? searchTerm : null;

		// Query with conditions for username, ID, and role if specified
		const usersResult = await db.query.users.findMany({
			where: (users) => {
				// If we have a search term, build search conditions
				if (hasSearchTerm) {
					const searchConditions = [];

					// Search by username (case insensitive)
					searchConditions.push(ilike(users.userName, `%${searchTerm}%`));

					// Search by ID if the search term can be parsed as a number
					if (searchId !== null) {
						searchConditions.push(eq(users.id, searchId));
					}

					// Combine with role filter if specified
					// Helper function to build role condition
					const buildRoleCondition = () => {
						if (!roleFilter) return undefined;
						return Array.isArray(roleFilter)
							? or(...roleFilter.map((r) => eq(users.role, r)))
							: eq(users.role, roleFilter);
					};

					const searchCondition = or(...searchConditions);
					const roleCondition = buildRoleCondition();

					// Combine conditions based on what's available
					let baseCondition;
					if (roleCondition) {
						baseCondition = and(roleCondition, searchCondition);
					} else {
						baseCondition = searchCondition;
					}

					// Apply exclusion if needed
					return excludeUserId
						? and(baseCondition, ne(users.id, excludeUserId))
						: baseCondition;
				} else {
					// No search term - filter by role only if specified
					if (roleFilter) {
						const roleCondition = Array.isArray(roleFilter)
							? or(...roleFilter.map((r) => eq(users.role, r)))
							: eq(users.role, roleFilter);

						return excludeUserId
							? and(roleCondition, ne(users.id, excludeUserId))
							: roleCondition;
					}

					// Only exclude condition
					return excludeUserId ? ne(users.id, excludeUserId) : undefined;
				}
			},
			limit: pageSize,
			offset: offset,
			orderBy: (users, { desc }) => [desc(users.createdAt)],
		});

		// Count total matching users with the same conditions
		const totalResult = await db
			.select({ count: count() })
			.from(users)
			.where(() => {
				if (hasSearchTerm) {
					const searchConditions = [];

					searchConditions.push(ilike(users.userName, `%${searchTerm}%`));

					if (searchId !== null) {
						searchConditions.push(eq(users.id, searchId));
					}

					if (roleFilter) {
						const roleCondition = Array.isArray(roleFilter)
							? or(...roleFilter.map((r) => eq(users.role, r)))
							: eq(users.role, roleFilter);

						const searchCondition = or(...searchConditions);
						const baseCondition = and(roleCondition, searchCondition);

						return excludeUserId
							? and(baseCondition, ne(users.id, excludeUserId))
							: baseCondition;
					}

					const searchCondition = or(...searchConditions);
					return excludeUserId
						? and(searchCondition, ne(users.id, excludeUserId))
						: searchCondition;
				} else {
					if (roleFilter) {
						const roleCondition = Array.isArray(roleFilter)
							? or(...roleFilter.map((r) => eq(users.role, r)))
							: eq(users.role, roleFilter);

						return excludeUserId
							? and(roleCondition, ne(users.id, excludeUserId))
							: roleCondition;
					}

					return excludeUserId ? ne(users.id, excludeUserId) : undefined;
				}
			});

		const total = totalResult[0].count;

		return {
			users: usersResult,
			pagination: {
				total,
				pageSize,
				currentPage: page,
				totalPages: Math.ceil(total / pageSize),
			},
		};
	} catch (error) {
		console.error("Database error in searchUsers:", error);
		return {
			users: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};
