import React, { useState, useMemo, useEffect, useCallback } from "react";
import useGetStreamToken from "@/hooks/useGetStreamToken";
import { StreamVideoClient, Call } from "@stream-io/video-react-sdk";
import "@stream-io/video-react-sdk/dist/css/styles.css";
import { useFetchScheduledCalls } from "@/hooks/useFetchScheduledCalls";
import { useUsersByIds } from "@/hooks/useUsersByIds";
import { ScheduledCall, Recipients } from "@/types/scheduledCall";
import CallVideoOverlay from "./CallVideoOverlay";
import CallsList from "./CallsList";
import CallDetails from "./CallDetails";
import { UserType } from "@/db/services/users.service";

type ScheduledCallsBoxProps = {
	me: UserType;
};

// Constants
const API_KEY = process.env.NEXT_PUBLIC_GETSTREAM_API_KEY!;
const S3_ENDPOINT = process.env.NEXT_PUBLIC_S3_ENDPOINT;

const ScheduledCallsBox: React.FC<ScheduledCallsBoxProps> = ({ me }) => {
	const [selectedCall, setSelectedCall] = useState<ScheduledCall | null>(null);
	const [streamCall, setStreamCall] = useState<Call | null>(null);
	const [isInitializing, setIsInitializing] = useState(true);

	const { token, error: streamError } = useGetStreamToken(me.id);
	const { data, error } = useFetchScheduledCalls(me.id);

	// Optimized user IDs extraction with better error handling
	const allUserIds = useMemo(() => {
		if (!data) return [];
		const ids = new Set<string>();

		data.forEach((item: any) => {
			if (item.senderId) ids.add(item.senderId);

			try {
				// Handle recipients as boolean-keyed object
				const recipients = item.recipients as Recipients;
				if (recipients && typeof recipients === "object") {
					Object.keys(recipients).forEach((id) => {
						if (id && typeof id === "string") ids.add(id);
					});
				}
			} catch (error) {
				console.warn(
					`Failed to process recipients for schedule ${item.id}:`,
					error
				);
			}
		});

		return Array.from(ids);
	}, [data]);

	const { users: participantDetails, isLoading: isLoadingUsers } =
		useUsersByIds(allUserIds);

	// Optimized schedules processing
	const schedules = useMemo(() => {
		if (!data) return [];

		return data.map(
			(item: any): ScheduledCall => ({
				...item,
				datetime: new Date(item.scheduleTime),
				allIds: [item.senderId, ...Object.keys(item.recipients || {})].filter(
					Boolean
				), // Remove empty values
			})
		);
	}, [data]);

	// Memoized client creation
	const client = useMemo(() => {
		if (!me?.id || !token) return null;

		try {
			return StreamVideoClient.getOrCreateInstance({
				apiKey: API_KEY,
				user: { id: me.id, name: me.userName },
				token,
			});
		} catch (error) {
			console.error("Failed to create StreamVideo client:", error);
			return null;
		}
	}, [me, token]);

	// Auto-select first call
	useEffect(() => {
		if (!selectedCall && schedules.length > 0) {
			setSelectedCall(schedules[0]);
		}
	}, [schedules, selectedCall]);

	// Handle initialization state
	useEffect(() => {
		if (token || streamError) {
			setIsInitializing(false);
		}
	}, [token, streamError]);

	// Optimized join call handler with error handling
	const handleJoinCall = useCallback(
		async (call: ScheduledCall) => {
			if (!token || !me?.id || !client) {
				console.warn("Missing required data for joining call");
				return;
			}

			try {
				const callId = `meeting-${call.id}`;
				const callType = "default";
				const callInstance = client.call(callType, callId);

				await callInstance.getOrCreate({
					data: {
						members: call.allIds.map((id) => ({ user_id: id })),
					},
				});

				await callInstance.join();
				setStreamCall(callInstance);
			} catch (error) {
				console.error("Failed to join call:", error);
				// You might want to show a user-friendly error message here
			}
		},
		[token, me?.id, client]
	);

	// Helper function to get safe image URL
	const getSafeImageUrl = useCallback((imagePath?: string) => {
		if (!imagePath || !S3_ENDPOINT) return null;
		return `${S3_ENDPOINT}/${imagePath}`;
	}, []);

	// Loading states
	if (isInitializing) {
		return (
			<div className="flex items-center justify-center h-64">
				Initializing...
			</div>
		);
	}

	if (streamError || !token) {
		return (
			<div className="flex items-center justify-center h-64 text-red-500">
				Error loading stream credentials:{" "}
				{streamError?.message || "Token not available"}
			</div>
		);
	}

	if (!me || error) {
		return (
			<div className="flex items-center justify-center h-64">
				Loading user...
			</div>
		);
	}

	const isLoading = !data || isLoadingUsers;

	return (
		<>
			{/* Video call overlay */}
			{streamCall && client && (
				<CallVideoOverlay
					streamCall={streamCall}
					client={client}
					onLeave={() => setStreamCall(null)}
				/>
			)}

			{/* Main content */}
			<div className="grid w-full h-full grid-cols-3 gap-4">
				{/* Calls list */}
				<CallsList
					schedules={schedules}
					selectedCall={selectedCall}
					isLoading={isLoading}
					onSelectCall={setSelectedCall}
					getSafeImageUrl={getSafeImageUrl}
				/>

				{/* Call details */}
				{me.id && (
					<div className="col-span-2 max-h-[80vh] overflow-y-auto bg-[#EFEFF1] rounded-lg">
						<CallDetails
							selectedCall={selectedCall}
							isLoading={isLoading}
							participantDetails={participantDetails}
							currentUserId={me.id}
							getSafeImageUrl={getSafeImageUrl}
							onJoinCall={handleJoinCall}
							isClientReady={!!client}
						/>
					</div>
				)}
			</div>
		</>
	);
};

export default ScheduledCallsBox;
