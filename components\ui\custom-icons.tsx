import Image from "next/image";

interface CustomIconProps {
  width?: number;
  height?: number;
  className?: string;
}

// Custom SVG icon components
export const AirConditioningIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/AirConditioning.svg"
		alt="Air Conditioning"
		width={width}
		height={height}
		className={className}
	/>
);

export const CarpetFreeIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/CarpetFree.svg"
		alt="Carpet Free"
		width={width}
		height={height}
		className={className}
	/>
);

export const ChefsKitchenIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/Chefs Kitchen.svg"
		alt="Chefs Kitchen"
		width={width}
		height={height}
		className={className}
	/>
);

export const FireplaceIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/Fireplace.svg"
		alt="Fireplace"
		width={width}
		height={height}
		className={className}
	/>
);

export const GuestHouseIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/GuestHouse.svg"
		alt="Guest House"
		width={width}
		height={height}
		className={className}
	/>
);

export const HomeOfficeIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/HomeOffice.svg"
		alt="Home Office"
		width={width}
		height={height}
		className={className}
	/>
);

export const MainLevelBedroomIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/MainLevelBedroom.svg"
		alt="Main Level Bedroom"
		width={width}
		height={height}
		className={className}
	/>
);

export const NaturalLightIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/NaturalLight.svg"
		alt="Natural Light"
		width={width}
		height={height}
		className={className}
	/>
);

export const PoolIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/Pool.svg"
		alt="Pool"
		width={width}
		height={height}
		className={className}
	/>
);

export const PrivateElevatorIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/PrivateElevator.svg"
		alt="Private Elevator"
		width={width}
		height={height}
		className={className}
	/>
);

export const PrivateGymIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/PrivateGym.svg"
		alt="Private Gym"
		width={width}
		height={height}
		className={className}
	/>
);

export const ScenicViewIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/ScenicView.svg"
		alt="Scenic View"
		width={width}
		height={height}
		className={className}
	/>
);

export const TerraceIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/Terrace.svg"
		alt="Terrace"
		width={width}
		height={height}
		className={className}
	/>
);

export const WineCellarIcon = ({
	width = 24,
	height = 24,
	className,
}: CustomIconProps) => (
	<Image
		src="/icons/WineCellar.svg"
		alt="Wine Cellar"
		width={width}
		height={height}
		className={className}
	/>
);
