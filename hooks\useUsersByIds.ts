import useSWR from "swr";
import { fetcher } from "@/lib/utils";

export interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  userName: string | null;
  imageUrl: string | null;
  role: string;
  email: string | null;
}

export interface UsersMap {
  [key: string]: User;
}

/**
 * Custom hook for fetching multiple users by their IDs
 * @param userIds Array of user IDs to fetch
 * @returns Object containing users data mapped by their IDs, loading state, and error state
 */
export function useUsersByIds(userIds: string[]) {
  const uniqueIds = Array.from(new Set(userIds)); // Remove duplicates
  const idsKey = uniqueIds.length > 0 ? `/api/users/by-ids?ids=${uniqueIds.join(",")}` : null;

  const { data, error, isLoading, mutate } = useSWR<UsersMap>(idsKey, fetcher, {
		revalidateOnFocus: false,
		revalidateOnReconnect: true,
		dedupingInterval: 60000, // Cache for 1 minute
		refreshInterval: 600000, // Refresh every 10 minutes
		keepPreviousData: true,
	});

  return {
    users: data || {},
    isLoading,
    error,
    mutate,
  };
} 
