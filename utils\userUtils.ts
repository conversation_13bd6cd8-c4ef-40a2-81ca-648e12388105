import { LinkedAccount } from "@/components/global/message-box/types";
import { UserSelectType } from "@/db/services/users.service";

// Generic function to deduplicate and sort users
export const deduplicateAndSortUsers = <
	T extends { id: string; name?: string },
>(
	users: T[]
): T[] => {
	// Combine all users and deduplicate using Map
	const uniqueUsersMap = new Map(
		users
			.filter((user) => user.id) // Filter out users without id
			.map((user) => [user.id, user]) // Create [id, user] pairs
	);

	// Convert to array and sort by name
	return Array.from(uniqueUsersMap.values()).sort((a, b) => {
		// Handle cases where name might be undefined or null
		const nameA = a.name || a.id || "";
		const nameB = b.name || b.id || "";

		// Case-insensitive alphabetical sort
		return nameA.toLowerCase().localeCompare(nameB.toLowerCase());
	});
};

// Extract function to get the other party's info
export const getOtherPartyInfo = (
	account: LinkedAccount,
	currentUserId: string
) => {
	return currentUserId === account.buyer.id
		? { id: account.provider.id, userName: account.provider.userName }
		: { id: account.buyer.id, userName: account.buyer.userName };
};

// Build final linked accounts list including listing owner if needed
export type ContactUser = {
	id: string;
	userName: string;
};

export const getContactUsers = (
	linkedAccounts: LinkedAccount[],
	listingOwner: UserSelectType | null,
	currentUser: UserSelectType
): ContactUser[] => {
	const otherUsers = linkedAccounts.map((account) =>
		getOtherPartyInfo(account, currentUser.id)
	);

	const contactUsers = otherUsers.map((account) => ({
		id: account.id,
		userName: account.userName,
	}));

	// Use the generic deduplicate and sort function
	const sortedOtherUsers = deduplicateAndSortUsers(contactUsers);

	if (listingOwner && listingOwner.id !== currentUser.id) {
		sortedOtherUsers.unshift({
			id: listingOwner.id,
			userName: listingOwner.userName,
		});
	}

	return sortedOtherUsers;
};

// Check if user can send messages
export const canUserSendMessage = (
	contactUsersCount: number,
	currentUser: UserSelectType,
	listingOwner?: UserSelectType
): boolean => {
	const hasContactUsers = contactUsersCount > 0;
	const isNonAdvertiser = currentUser.role !== "advertiser";
	const isListingOwner =
		currentUser.role === "advertiser" && currentUser.id === listingOwner?.id;
	return hasContactUsers && (isNonAdvertiser || isListingOwner);
};
