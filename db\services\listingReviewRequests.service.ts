"use server";
import { ReviewerTypeType, ReviewStatusType } from "./../schema/enums"; // Update import
import { eq, count, desc, and } from "drizzle-orm";
import { db } from "../index";
import { listingReviewRequests } from "../schema/listingReviewRequests";
import { getCurrentUser } from "./users.service";
import { upsertVectorPartial } from "@/lib/vectorHelpers";

export type ListingReviewRequestType =
	typeof listingReviewRequests.$inferInsert;
export type ListingReviewRequestSelectType =
	typeof listingReviewRequests.$inferSelect;

/**
 * Creates or updates a listing review request
 */
export const upsertListingReviewRequest = async (data: {
	listingId: string;
	userId?: string; // Add optional userId parameter
	reviewStatus?: ReviewStatusType; // Add optional reviewStatus parameter
	notes?: string;
	requestDate?: Date;
}) => {
	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		// Use provided userId or fall back to current user's ID
		const targetUserId = data.userId || currentUser.id;

		// Check if there's already an existing review request for this listing
		const existingRequest = await db.query.listingReviewRequests.findFirst({
			where: and(
				eq(listingReviewRequests.listingId, data.listingId),
				eq(listingReviewRequests.userId, targetUserId)
			),
		});

		if (existingRequest) {
			await db
				.update(listingReviewRequests)
				.set({
					...(data.reviewStatus && { reviewStatus: data.reviewStatus }), // Update reviewStatus if provided
					notes: data.notes || null,
					updatedAt: new Date(),
					requestDate: data.requestDate || existingRequest.requestDate, // Use provided requestDate or keep existing
				})
				.where(eq(listingReviewRequests.id, existingRequest.id));

			return await getListingReviewRequestById(existingRequest.id);
		} else {
			// Create new request
			const reviewRequestData: ListingReviewRequestType = {
				userId: targetUserId, // Use targetUserId instead of currentUser.id
				listingId: data.listingId,
				reviewStatus: data.reviewStatus || "drafts", // Use provided reviewStatus or default to "drafts"
				requestDate: new Date(),
				notes: data.notes || null,
			};

			const [newReviewRequest] = await db
				.insert(listingReviewRequests)
				.values(reviewRequestData)
				.returning();

			return newReviewRequest;
		}
	} catch (error) {
		console.error("Database error in upsertListingReviewRequest:", error);
		throw new Error(
			`Failed to upsert review request: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Gets a review request by ID
 */
export const getListingReviewRequestById = async (id: string) => {
	try {
		const reviewRequest = await db.query.listingReviewRequests.findFirst({
			where: eq(listingReviewRequests.id, id),
			with: {
				user: true,
				listing: {
					with: {
						location: true,
						realEstateDetails: true,
					},
				},
			},
		});

		return reviewRequest;
	} catch (error) {
		console.error("Database error in getListingReviewRequestById:", error);
		return null;
	}
};

/**
 * Gets review requests for the current user
 */
export const getCurrentUserListingReviewRequests = async (
	page: number = 1,
	pageSize: number = 10,
	status?: ReviewStatusType
) => {
	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		const offset = (page - 1) * pageSize;

		let whereClause = eq(listingReviewRequests.userId, currentUser.id);
		if (status) {
			whereClause = and(
				whereClause,
				eq(listingReviewRequests.reviewStatus, status)
			)!;
		}

		const reviewRequestsResult = await db.query.listingReviewRequests.findMany({
			where: whereClause,
			limit: pageSize,
			offset: offset,
			orderBy: [desc(listingReviewRequests.requestDate)],
			with: {
				listing: {
					with: {
						location: true,
						realEstateDetails: true,
						media: {
							orderBy: (media, { asc }) => [asc(media.sortOrder)],
						},
					},
				},
			},
		});

		const totalResult = await db
			.select({ count: count() })
			.from(listingReviewRequests)
			.where(whereClause);

		const total = totalResult[0].count;

		return {
			reviewRequests: reviewRequestsResult,
			pagination: {
				total,
				pageSize,
				currentPage: page,
				totalPages: Math.ceil(total / pageSize),
			},
		};
	} catch (error) {
		console.error(
			"Database error in getCurrentUserListingReviewRequests:",
			error
		);
		return {
			reviewRequests: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

/**
 * Gets a review request by listing ID
 */
export const getListingReviewRequestByListingId = async (listingId: string) => {
	try {
		const reviewRequest = await db.query.listingReviewRequests.findFirst({
			where: eq(listingReviewRequests.listingId, listingId),
			with: {
				user: true,
				listing: true,
			},
		});

		return reviewRequest;
	} catch (error) {
		console.error(
			"Database error in getListingReviewRequestByListingId:",
			error
		);
		return null;
	}
};

/**
 * Updates review request status (for admin)
 */
export const updateListingReviewRequestStatus = async (data: {
	id: string;
	reviewStatus: ReviewStatusType;
	reviewerId?: string;
	reviewerType?: ReviewerTypeType; // Use imported type
	declinedDate?: Date;
	archivedDate?: Date;
	notes?: string;
	approvedDate?: Date;
}) => {
	try {
		const updateData: Partial<ListingReviewRequestType> = {
			reviewStatus: data.reviewStatus,
			updatedAt: new Date(),
			...(data.reviewerId !== undefined && { reviewerId: data.reviewerId }),
			...(data.reviewerType !== undefined && {
				reviewerType: data.reviewerType,
			}),
			...(data.notes !== undefined && { notes: data.notes }),
		};

		// Set appropriate date based on review status
		const currentDate = new Date();
		switch (data.reviewStatus) {
			case "pending":
				updateData.requestDate = currentDate;
				break;
			case "approved":
				updateData.approvedDate = currentDate;
				break;
			case "declined":
				updateData.declinedDate = currentDate;
				break;
			case "archived":
				updateData.archivedDate = currentDate;
				break;
			// For "drafts" status, don't set specific dates
		}

		const [updatedRecord] = await db
			.update(listingReviewRequests)
			.set(updateData)
			.where(eq(listingReviewRequests.id, data.id))
			.returning();

		upsertVectorPartial(updatedRecord.listingId, {
			id: updatedRecord.id,
			reviewStatus: data.reviewStatus,
		});

		return updatedRecord;
	} catch (error) {
		console.error("Database error in updateListingReviewRequestStatus:", error);
		throw new Error(
			`Failed to update review request status: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};

/**
 * Gets all review requests (for admin)
 */
export const getAllListingReviewRequests = async (
	page: number = 1,
	pageSize: number = 10,
	status?: ReviewStatusType
) => {
	try {
		const currentUser = await getCurrentUser();
		if (!currentUser || currentUser.role !== "admin") {
			throw new Error("Not authorized to view all review requests");
		}

		const offset = (page - 1) * pageSize;

		let whereClause;
		if (status) {
			whereClause = eq(listingReviewRequests.reviewStatus, status);
		}

		const reviewRequestsResult = await db.query.listingReviewRequests.findMany({
			where: whereClause,
			limit: pageSize,
			offset: offset,
			orderBy: [desc(listingReviewRequests.requestDate)],
			with: {
				user: true,
				listing: {
					with: {
						location: true,
						realEstateDetails: true,
						media: {
							orderBy: (media, { asc }) => [asc(media.sortOrder)],
						},
					},
				},
			},
		});

		const totalResult = await db
			.select({ count: count() })
			.from(listingReviewRequests)
			.where(whereClause || undefined);

		const total = totalResult[0].count;

		return {
			reviewRequests: reviewRequestsResult,
			pagination: {
				total,
				pageSize,
				currentPage: page,
				totalPages: Math.ceil(total / pageSize),
			},
		};
	} catch (error) {
		console.error("Database error in getAllListingReviewRequests:", error);
		return {
			reviewRequests: [],
			pagination: {
				total: 0,
				pageSize,
				currentPage: page,
				totalPages: 0,
			},
		};
	}
};

/**
 * Deletes a review request
 */
export const deleteListingReviewRequest = async (id: string) => {
	try {
		const currentUser = await getCurrentUser();
		if (!currentUser) {
			throw new Error("User not authenticated");
		}

		// Get the existing review request to check ownership
		const existingRequest = await db.query.listingReviewRequests.findFirst({
			where: eq(listingReviewRequests.id, id),
		});

		if (!existingRequest) {
			throw new Error("Review request not found");
		}

		// Check if current user is owner or admin
		if (
			existingRequest.userId !== currentUser.id &&
			currentUser.role !== "admin"
		) {
			throw new Error("Not authorized to delete this review request");
		}

		// Only allow deletion if status is drafts or declined
		if (!["drafts", "declined"].includes(existingRequest.reviewStatus)) {
			throw new Error("Cannot delete review request in current status");
		}

		await db
			.delete(listingReviewRequests)
			.where(eq(listingReviewRequests.id, id));

		return { success: true };
	} catch (error) {
		console.error("Database error in deleteListingReviewRequest:", error);
		throw new Error(
			`Failed to delete review request: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}
};
