import { fetcher } from "@/lib/utils";
import useS<PERSON> from "swr";

export const useFetchScheduledCalls = (userId?: string) => {
	const { data, error, isLoading } = useSWR(
		userId ? `/api/schedules?userId=${userId}` : null,
		fetcher,
		{
			dedupingInterval: 60000, // 1 minute deduplication interval
			refreshInterval: 600000, // Refresh every 10 minutes
			revalidateOnFocus: false, // Disable revalidation on window focus
			revalidateOnReconnect: true,
			keepPreviousData: true,
		}
	);

	return {
		data,
		error,
		isLoading,
	};
};
