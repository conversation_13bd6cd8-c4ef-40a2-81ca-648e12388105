import { UserSelectType } from "@/db/services/users.service";
import { ChannelSort } from "stream-chat";
import {
	Chat,
	Channel,
	ChannelList,
	Window,
	MessageList,
	MessageInput,
	Thread,
	useCreateChatClient,
	InfiniteScroll,
} from "stream-chat-react";
import "stream-chat-react/dist/css/v2/index.css";
import * as React from "react";

import CustomMessageUi from "./CustomMessageUi";
import CustomChannelPreview from "./CustomChannelPreview";
import CustomMessageInput from "./CustomMessageInput";
import CustomerHeader from "./CustomerHeader";
import CustomerHeaderSkeleton from "./CustomerHeaderSkeleton";

const ChatComponent = ({
	me,
	token,
}: {
	me: UserSelectType;
	token: string;
}) => {
	const filters = { members: { $in: [me.id] }, type: "messaging" };
	const options = { presence: true, state: true };
	const sort = { last_message_at: -1 } as ChannelSort;

	const client = useCreateChatClient({
		apiKey: process.env.NEXT_PUBLIC_GETSTREAM_API_KEY!,
		tokenOrProvider: token,
		userData: { id: me.id, name: me.userName, image: me.imageUrl || undefined },
	});

	// If client is not ready yet
	if (!client) return <div>Initializing chat...</div>;

	return (
		<Chat client={client}>
			<div className="grid w-full h-full grid-cols-3 gap-4">
				<div className="col-span-1 scrollable">
					<ChannelList
						sort={sort}
						filters={filters}
						options={options}
						Paginator={InfiniteScroll}
						Preview={(props) => <CustomChannelPreview {...props} me={me} />}
					/>
				</div>
				<div className="col-span-2 h-full scrollable bg-[#EFEFF1]">
					<Channel Message={CustomMessageUi}>
						<Window>
							{me ? <CustomerHeader me={me} /> : <CustomerHeaderSkeleton />}
							<div className="flex h-full min-h-[300px] flex-col justify-end">
								<MessageList />
							</div>
							<div className="sticky bottom-0 z-10 rounded-[11px] bg-[#CACAD1] dark:bg-gray-900">
								<MessageInput Input={CustomMessageInput} />
							</div>
						</Window>
						<Thread />
					</Channel>
				</div>
			</div>
		</Chat>
	);
};
export default ChatComponent;
