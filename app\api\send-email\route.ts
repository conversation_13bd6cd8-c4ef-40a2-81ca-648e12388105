import { NextRequest, NextResponse } from "next/server";
import { sendEmail } from "@/lib/ses";

export async function POST(req: NextRequest) {
  try {
    const { to, subject, body } = await req.json();

    if (!to || !subject || !body) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    await sendEmail({
			to,
			from: process.env.SES_EMAIL_FROM_ADDRESS!, // This must be a verified email in SES
			subject,
			body,
			isHtml: false, // Set to false if sending plain text
		});

    return NextResponse.json(
      { message: "Email sent successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in send-email API route:", error);
    return NextResponse.json(
      { error: "Failed to send email" },
      { status: 500 }
    );
  }
}
