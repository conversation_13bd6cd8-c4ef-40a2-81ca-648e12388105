"use client";
import { useState } from "react";
import UsersTable from "./UsersTable";
import AddNewAccount from "./AddNewAccount";
import { RoleType } from "@/db/schema/enums";

interface UserManagementProps {
	role?: RoleType;
}

const UserManagement = ({ role }: UserManagementProps) => {
	const [mutateUsers, setMutateUsers] = useState<(() => void) | null>(null);

	const handleMutateReady = (mutate: () => void) => {
		setMutateUsers(() => mutate);
	};

	return (
		<div className="space-y-4">
			<div className="flex justify-end">
				<AddNewAccount role={role as RoleType} onAccountCreated={mutateUsers} />
			</div>
			<UsersTable roleFilter={role} onMutateReady={handleMutateReady} />
		</div>
	);
};

export default UserManagement;
