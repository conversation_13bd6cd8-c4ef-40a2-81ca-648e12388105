import UserManagementClient from "@/components/user-management/UserManagementClient";
import {
	USER_MANAGEMENT_PATH_MAP,
	UserManagementPathMapKey,
} from "@/constant/const";
import { notFound } from "next/navigation";

const Page = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const { slug } = await params;

	// Only allow access when slug is in the allowed pages list
	if (!(slug in USER_MANAGEMENT_PATH_MAP)) {
		notFound();
	}

	return <UserManagementClient slug={slug as UserManagementPathMapKey} />;
};

export default Page;
