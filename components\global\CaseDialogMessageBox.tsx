"use client";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { MessageBoxProps } from "./message-box/types";
import { MessageBoxSkeleton } from "./message-box/MessageBoxSkeleton";
import { MessageBoxContent } from "./message-box/MessageBoxContent";

// Main component
const CaseDialogMessageBox = ({
	backPage,
	listingId,
	listingOwner,
	previewUrl,
	data,
}: MessageBoxProps) => {
	const { user: me } = useCurrentUser();

	if (!me) {
		return <MessageBoxSkeleton />;
	}

	if (me.role === "admin") return null; // <PERSON><PERSON> should not see the message box

	return (
		<MessageBoxContent
			me={me}
			backPage={backPage}
			listingId={listingId}
			listingOwner={listingOwner}
			previewUrl={previewUrl}
			data={data}
		/>
	);
};

export default CaseDialogMessageBox;
