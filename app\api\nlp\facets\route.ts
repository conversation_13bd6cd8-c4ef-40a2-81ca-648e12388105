import { NextResponse } from "next/server";
import OpenAI from "openai";
import { stateMappings } from "../../../../constant/location";
import { PropertyTypeType } from "@/db/schema";

const openai = new OpenAI({
	apiKey: process.env.OPEN_API_KEY!,
});

interface FacetResponse {
	facets: {
		propertyTypes?: PropertyTypeType;
		bedrooms?: number;
		bathrooms?: number;
		location?: string[];
		homeSize?: number;
		saleType?: string;
		amenities?: string[];
		parkingType?: string;
		parkingSpaces?: number;
	};
	clarifyNeeded: boolean;
	missing: string[];
	isSubjective: boolean;
}


// Function to normalize location names
function normalizeLocation(location: string): string[] {
  // Split the location string by common separators and handle various formats
  const locations = location.toLowerCase()
    .split(/[,;|&]/) // Split by comma, semicolon, pipe, or ampersand
    .map(loc => loc.trim())
    .filter(loc => loc.length > 0); // Remove empty strings
  
  // Map each location to its normalized state code
  return locations.map(loc => {
    // Handle both full state names and abbreviations
    const normalizedLoc = stateMappings[loc] || loc;
    // If the location is a two-letter state code, return it as is
    if (normalizedLoc.length === 2) {
      return normalizedLoc;
    }
    // Otherwise, try to find a mapping for it
    return stateMappings[normalizedLoc] || normalizedLoc;
  });
}

// Function to check if query contains subjective terms using OpenAI
async function isQuerySubjective(query: string): Promise<boolean> {
  const completion = await openai.chat.completions.create({
    model: "gpt-4-turbo-preview",
    messages: [
      {
        role: "system",
        content: `You are a real estate search assistant. Analyze if the query contains subjective or opinion-based terms.
        Return a JSON object with a single boolean field 'isSubjective' indicating whether the query contains subjective terms.
        Consider terms related to aesthetics, feelings, preferences, or opinions.`,
      },
      {
        role: "user",
        content: query,
      },
    ],
    response_format: { type: "json_object" },
  });

  const result = JSON.parse(completion.choices[0].message.content || "{}");
  return result.isSubjective || false;
}

// Function to parse natural language query using OpenAI
async function parseQuery(query: string) {
  const completion = await openai.chat.completions.create({
    model: "gpt-4-turbo-preview",
    messages: [
      {
        role: "system",
        content: `You are a real estate search assistant. Extract search parameters from user queries.
        Return a JSON object with the following fields:
        - propertyType: type of property (house, apartment, condo, etc.)
        - bedrooms: number of bedrooms
        - bathrooms: number of bathrooms
        - location: city, state, or neighborhood (handle both full names and abbreviations)
        - homeSize: size in square feet
        - saleType: type of sale (sale, rent, etc.)
        - amenities: array of amenities mentioned (e.g. ["pool", "garage", "garden"])
        - parkingType: type of parking (garage, carport, street, etc.)
        - parkingSpaces: number of parking spaces
        Only include fields that are explicitly mentioned in the query.
        For location, handle multiple states in various formats (e.g., "wa and ny", "wa & ny", "wa, ny", "washington and florida and nyc", "wa, fl, tx").
        When multiple locations are provided, return them as an array of strings.`,
      },
      {
        role: "user",
        content: query,
      },
    ],
    response_format: { type: "json_object" },
  });

  const parsed = JSON.parse(completion.choices[0].message.content || "{}");
  
  // Normalize location if present
  if (parsed.location) {
    // Handle both string and array inputs for location
    const locationInput = Array.isArray(parsed.location) ? parsed.location.join(',') : parsed.location;
    parsed.location = normalizeLocation(locationInput);
  }

  return parsed;
}

export async function POST(req: Request) {
  try {
    const { query } = await req.json();
    if (!query) {
      return NextResponse.json(
        { error: "Search query is required" },
        { status: 400 }
      );
    }
    // Parse the natural language query
    const searchParams = await parseQuery(query);
    
    // Check for missing required fields
    const missing: string[] = [];
    if (!searchParams.propertyType) missing.push("propertyType");
    if (!searchParams.bedrooms) missing.push("bedrooms");
    if (!searchParams.bathrooms) missing.push("bathrooms");
    if (!searchParams.location) missing.push("location");
    if (!searchParams.homeSize) missing.push("homeSize");
    if (!searchParams.saleType) missing.push("saleType");
    if (!searchParams.amenities) missing.push("amenities");
    if (!searchParams.parkingType) missing.push("parkingType");
    if (!searchParams.parkingSpaces) missing.push("parkingSpaces");


    const response: FacetResponse = {
      facets: {
        propertyTypes: searchParams.propertyType,
        bedrooms: searchParams.bedrooms,
        bathrooms: searchParams.bathrooms,
        location: searchParams.location,
        homeSize: searchParams.homeSize,
        saleType: searchParams.saleType,
        amenities: searchParams.amenities,
        parkingType: searchParams.parkingType,
        parkingSpaces: searchParams.parkingSpaces,
      },
      clarifyNeeded: missing.length > 0,
      missing: missing,
      isSubjective: await isQuerySubjective(query),
    };
    return NextResponse.json(response);
  } catch (error) {
    console.error("Search error:", error);
    return NextResponse.json(
      { error: "An error occurred during search", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
