import { features } from "@/constant/amenities";

type Props = {
  amenitiesData: string[] | null;
};

// Helper function to format category titles by removing 'features' or 'feature' suffix
const formatCategoryTitle = (title: string): string => {
  return title.replace(/\s+(features?|Features?)$/i, "").trim();
};

const CaseDialogAllAmenities = ({ amenitiesData = [] }: Props) => {
  // Ensure amenitiesData is an array
  const safeAmenitiesData = amenitiesData || [];

  // Filter features to only show categories that have matching items
  const filteredFeatures = features
    .map((category) => {
      // Filter items in each category that match amenitiesData
      const filteredItems = category.items.filter((item) =>
        safeAmenitiesData.includes(item.name as string)
      );

      return {
        ...category,
        items: filteredItems,
        // Keep track if this category has any matching items
        hasItems: filteredItems.length > 0,
      };
    })
    .filter((category) => category.hasItems);

  return (
		<div className="grid grid-cols-3 px-6 pt-4 pb-1 bg-zinc-100">
			{filteredFeatures.map((category) => (
				<div key={category.id} className="mb-8 font-manrope">
					<h3 className="mb-4 text-lg font-bold">
						{formatCategoryTitle(category.title)}
					</h3>
					<div className="grid grid-cols-1 gap-2">
						{category.items.map((item) => (
							<div
								key={item.name as string}
								className="flex items-center gap-2"
							>
								<span className="text-base font-normal">{item.label}</span>
							</div>
						))}
					</div>
				</div>
			))}
		</div>
	);
};

export default CaseDialogAllAmenities;
