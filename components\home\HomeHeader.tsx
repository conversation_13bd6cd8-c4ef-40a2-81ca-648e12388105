"use client";

import Searcher from "./Searcher";
import Logo from "../ui/Logo";
import HeaderUserButton from "./HeaderUserButton";
import NotificationIcons from "./NotificationIcons";
import { usePathname } from "next/navigation";
import { UserSelectType } from "@/db/services/users.service";
import useGetStreamToken from "@/hooks/useGetStreamToken";
import useSearchStore from "@/stores/search";
export const dynamic = "force-dynamic";

const HomeHeader = ({ me }: { me: UserSelectType | null }) => {
	const pathname = usePathname();

	const { token, error, isLoading } = useGetStreamToken(me?.id);
	const { resetSearch } = useSearchStore();

	const handleLogoClick = () => {
		resetSearch();
	};

	return (
		<div className="flex flex-row items-center justify-between gap-4 p-4 md:gap-16">
			<div className="hidden w-auto h-full md:block">
				<Logo className="w-auto h-full" onClick={handleLogoClick} />
			</div>
			{pathname === "/" && (
				<div className="w-full max-w-3xl">
					<Searcher />
				</div>
			)}
			<div className="flex items-center">
				{me && !isLoading && !error && (
					<NotificationIcons me={me} token={token} />
				)}
				{me && <HeaderUserButton me={me} />}
			</div>
		</div>
	);
};

export default HomeHeader;
