import React from "react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface SelectOption {
	value: string;
	label: string;
}

interface EditableSelectFieldProps {
	value: string;
	onChange: (value: string) => void;
	options: SelectOption[];
	className?: string;
	isEditing: boolean;
}

const EditableSelectField: React.FC<EditableSelectFieldProps> = ({
	value,
	onChange,
	options,
	className = "",
	isEditing,
}) => {
	// Find the label for the current value
	const getLabel = (val: string) => {
		const option = options.find((opt) => opt.value === val);
		return option ? option.label : val;
	};

	return (
		<div className={className}>
			{isEditing ? (
				<Select defaultValue={value} onValueChange={onChange}>
					<SelectTrigger className="font-bold h-9">
						<SelectValue placeholder="Select option" />
					</SelectTrigger>
					<SelectContent>
						{options.map((option) => (
							<SelectItem key={option.value} value={option.value}>
								{option.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			) : (
				<span className="font-bold">{getLabel(value) || "N/A"}</span>
			)}
		</div>
	);
};

export default EditableSelectField;
