import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";

interface LogoProps {
	width?: number;
	height?: number;
	className?: string;
	onClick?: () => void;
}

const Logo = ({ width = 166, height = 54, className, onClick }: LogoProps) => {
	return (
		<Link href={"/"} onClick={onClick}>
			<Image
				src="/images/MaisonGlobal-Login-Logo.svg"
				width={width}
				height={height}
				priority
				className={cn("object-contain", className)}
				alt={process.env.NEXT_PUBLIC_COMPANY_NAME!}
			/>
		</Link>
	);
};

export default Logo;
