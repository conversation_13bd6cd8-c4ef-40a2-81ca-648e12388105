import { ListingData } from "@/types/listing";

/**
 * Check if all media items have status 'completed'
 */
export const areAllMediaCompleted = (media: ListingData["media"]): boolean => {
	return media.length > 0 && media.every((item) => item.status === "completed");
};

/**
 * Get the preview URL for the first media item if available
 */
export const getPreviewMediaUrl = (media: ListingData["media"]): string => {
	const allCompleted = areAllMediaCompleted(media);

	if (media.length > 0 && allCompleted && media[0]?.preview) {
		return `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${media[0].preview}`;
	}

	return "https://placehold.co/600x400/jpg";
};

/**
 * Check if media is in processing state
 */
export const isMediaProcessing = (media: ListingData["media"]): boolean => {
	return media.length > 0 && !areAllMediaCompleted(media);
};
