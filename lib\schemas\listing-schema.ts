import { z } from "zod";
import { countries } from "countries-list";
import { historicalDesignationEnum, parkingTypeEnum, propertyTypeEnum, visibilityEnum } from "@/db/schema/enums";

// Define data types for dropdown options
export const propertyTypes = propertyTypeEnum.enumValues;

export const parkingTypes = parkingTypeEnum.enumValues;

export const historicalDesignations = historicalDesignationEnum.enumValues;

export const visibilityOptions = visibilityEnum.enumValues;

export const unitOptions = [
	{ value: "sqft", label: "sq ft" },
	{ value: "sqm", label: "sq m" },
	{ value: "acres", label: "acres" },
	{ value: "hectares", label: "hectares" },
] as const;

// Export unit values array for validation
export const unitValues = unitOptions.map((option) => option.value);

export const currencyOptions = [
	{ value: "USD", label: "$" },
	{ value: "EUR", label: "€" },
	{ value: "GBP", label: "£" },
	{ value: "JPY", label: "¥" },
	{ value: "CAD", label: "C$" },
	{ value: "AUD", label: "A$" },
] as const;

export const countryOptions = Object.entries(countries)
	.map(([code, data]) => ({
		value: code,
		label: data.name,
	}))
	.sort((a, b) => a.label.localeCompare(b.label));

// Define the listing schema with Zod
export const listingSchema = z.object({
	// Main Information
	propertyTitle: z
		.string()
		.min(4, "Property title must be at least 4 characters"),
	price: z.object({
		currency: z.string(),
		amount: z.coerce.number().positive("Price must be a positive number"),
	}),
	streetAddress: z.string().min(5, "Street address is required"),
	hideStreetAddress: z.boolean().default(false),
	city: z.string().min(2, "City is required"),
	stateRegion: z.string().min(2, "State/Region is required"),
	zipCode: z.string().min(3, "Zip/Postal code is required"),
	country: z.string(),
	neighborhood: z.string(),

	// Property Details
	propertyType: z.enum(propertyTypes),
	bedrooms: z.coerce
		.number()
		.int()
		.min(0, "Bedrooms must be a non-negative number"),
	bathrooms: z.coerce
		.number()
		.min(0, "Bathrooms must be a non-negative number"),
	homeSize: z.object({
		unit: z.string(),
		value: z.coerce.number().positive("Home size must be a positive number"),
	}),
	lotSize: z.object({
		unit: z.string(),
		value: z.coerce.number().positive("Lot size must be a positive number"),
	}),
	parkingType: z.enum(parkingTypes),
	parkingSpaces: z.coerce
		.number()
		.int()
		.min(0, "Parking spaces must be a non-negative number"),
	yearBuilt: z.coerce
		.number()
		.int()
		.min(1800, "Year must be 1800 or later")
		.max(new Date().getFullYear() + 5, "Year cannot be in the far future"),
	historicalDesignation: z.enum(historicalDesignations),

	// Amenities as a string array
	amenities: z.array(z.string()).default([]),

	// Description - will be added later
	description: z.string().optional(),

	// Media upload - updated structure
	media: z
		.array(
			z.object({
				id: z.string().optional(), // Optional ID for existing media items
				source: z.string().optional(),
				key: z.string().optional(), // Added key field for S3 storage path
				type: z.string().optional(),
				sortOrder: z.number().optional(),
				preview: z.string().optional(),
			})
		)
		.default([]),

	// Primary status
	isPrimary: z.boolean().default(false),

	// Visibility status
	visibility: z.enum(visibilityOptions),
});

export type ListingFormValues = z.infer<typeof listingSchema>;
