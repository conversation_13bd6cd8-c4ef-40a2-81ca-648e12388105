import useSWR from "swr";
import useSWRInfinite from "swr/infinite";
import { fetcher } from "@/lib/utils";
import { ListingData } from "@/types/listing";

// Define the return type for the API response
interface ListingsResponse {
	listings: ListingData[];
	pagination: {
		total: number;
		pageSize: number;
		currentPage: number;
		totalPages: number;
	};
}

// Define the parameters for the useListings hook
interface UseListingsParams {
	status?: string | string[];
	search?: string;
	filters?: Record<string, any>;
	page?: number;
	pageSize?: number;
	userOnly?: boolean;
	favoritesOnly?: boolean;
	infinite?: boolean;
	sortBy?: string;
	reviewerType?: string; // Add reviewerType parameter
}

/**
 * Custom hook for fetching and managing listings data
 */
export const useListings = ({
	status,
	search = "",
	filters = {},
	page = 1,
	pageSize = 6,
	userOnly,
	favoritesOnly = false,
	infinite = false,
	sortBy = "newest",
	reviewerType, // Add reviewerType parameter
}: UseListingsParams) => {
	// Build query string for API
	const buildQueryString = (pageIndex: number = page) => {
		const params = new URLSearchParams();
		params.append("page", pageIndex.toString());
		params.append("pageSize", pageSize.toString());

		// Handle status as string or array
		if (status) {
			const statusString = Array.isArray(status) ? status.join(",") : status;
			params.append("status", statusString);
		}

		if (userOnly) params.append("userOnly", "true");
		if (favoritesOnly) params.append("favoritesOnly", "true");
		if (search) params.append("search", search);
		if (sortBy) params.append("sortBy", sortBy);
		if (reviewerType) params.append("reviewerType", reviewerType); // Add reviewerType to query

		// Add filters to query string (excluding sortBy and reviewerType)
		Object.entries(filters).forEach(([key, value]) => {
			if (
				value !== undefined &&
				value !== null &&
				value !== "" &&
				key !== "sortBy" &&
				key !== "reviewerType" // Exclude reviewerType from filters loop
			) {
				params.append(key, value.toString());
			}
		});

		return params.toString();
	};

	// Setup for infinite scroll
	const getKey = (
		pageIndex: number,
		previousPageData: ListingsResponse | null
	) => {
		// If infinite is disabled, return null
		if (!infinite) return null;

		// If we have previous data and we've reached the total pages, stop fetching
		if (
			previousPageData &&
			previousPageData.pagination &&
			pageIndex >= previousPageData.pagination.totalPages
		) {
			return null;
		}

		// If reached the end (no listings in previous page), return null to stop fetching
		if (
			previousPageData &&
			(!previousPageData.listings || previousPageData.listings.length === 0)
		) {
			return null;
		}

		// First page, no previous data
		return `/api/listings?${buildQueryString(pageIndex + 1)}`;
	};

	// Always call both hooks, but conditionally use their results
	const infiniteResult = useSWRInfinite<ListingsResponse>(getKey, fetcher, {
		revalidateOnFocus: false,
		revalidateOnReconnect: true,
		revalidateIfStale: true,
		keepPreviousData: true, // Add keepPreviousData for infinite scroll
	});

	const singleResult = useSWR<ListingsResponse>(
		infinite ? null : `/api/listings?${buildQueryString()}`,
		fetcher,
		{
			revalidateOnFocus: false,
			refreshInterval: 60000, // Refresh every 60 seconds
			revalidateOnReconnect: true,
			revalidateIfStale: true,
			keepPreviousData: true,
		}
	);

	// Return appropriate result based on infinite flag
	if (infinite) {
		const { data, error, isLoading, size, setSize, mutate, isValidating } =
			infiniteResult;

		// Flatten all pages into a single array
		const allListings = data ? data.flatMap((page) => page.listings || []) : [];
		const totalCount = data?.[0]?.pagination?.total || 0;
		const totalPages = data?.[0]?.pagination?.totalPages || 0;

		// Fix hasMore logic: current loaded pages (size) should be less than total pages
		// and we should have data to ensure we've actually loaded something
		const hasMore = data && data.length > 0 && size < totalPages;

		return {
			listings: allListings,
			totalCount,
			totalPages,
			isLoading: isLoading && size === 1, // Only show loading for initial load
			isLoadingMore: isValidating && size > 1, // Show loading more for subsequent loads
			error,
			mutate,
			isEmpty: !isLoading && allListings.length === 0,
			hasMore,
			loadMore: () => setSize(size + 1),
		};
	}

	// Return single page result
	const { data, error, isLoading, mutate } = singleResult;

	return {
		listings: data?.listings || [],
		totalCount: data?.pagination?.total || 0,
		totalPages: data?.pagination?.totalPages || 0,
		isLoading,
		error,
		mutate, // Expose mutate function for manual revalidation
		isEmpty: !isLoading && (!data?.listings || data.listings.length === 0),
	};
};
