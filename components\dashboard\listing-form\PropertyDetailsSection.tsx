"use client";
import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	ListingFormValues,
	propertyTypes,
	parkingTypes,
	historicalDesignations,
	unitOptions,
} from "@/lib/schemas/listing-schema";
import InputWithUnit from "./InputWithUnit";

type PropertyDetailsSectionProps = {
	form: UseFormReturn<ListingFormValues>;
};

const PropertyDetailsSection = ({ form }: PropertyDetailsSectionProps) => {
	return (
		<div className="space-y-6">
			<h2 className="text-3xl font-normal font-lora">Property Details</h2>

			<FormField
				control={form.control}
				name="propertyType"
				render={({ field }) => (
					<FormItem className="flex flex-col space-y-1.5">
						<FormLabel>Property Type</FormLabel>
						<Select onValueChange={field.onChange} defaultValue={field.value}>
							<FormControl>
								<SelectTrigger className="capitalize">
									<SelectValue placeholder="Select property type" />
								</SelectTrigger>
							</FormControl>
							<SelectContent className="capitalize">
								{propertyTypes.map((type) => (
									<SelectItem key={type} value={type}>
										{type}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<FormMessage />
					</FormItem>
				)}
			/>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="bedrooms"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Bedrooms</FormLabel>
							<FormControl>
								<Input
									type="number"
									placeholder="0"
									min="0"
									{...field}
									onChange={(e) => {
										const value =
											e.target.value === ""
												? ""
												: Math.max(0, parseInt(e.target.value, 10));
										field.onChange(value);
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="bathrooms"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Bathrooms</FormLabel>
							<FormControl>
								<Input
									type="number"
									placeholder="0"
									step="0.5"
									min="0"
									{...field}
									onChange={(e) => {
										const value =
											e.target.value === ""
												? ""
												: Math.max(0, parseFloat(e.target.value));
										field.onChange(value);
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<InputWithUnit
				form={form}
				fieldName="homeSize"
				valueName="value"
				unitName="unit"
				label="Home Size"
				placeholder="0"
				units={[...unitOptions.slice(0, 2)]} // Only sq ft and sq m, converted to mutable array
				defaultUnit="sqft"
			/>

			<InputWithUnit
				form={form}
				fieldName="lotSize"
				valueName="value"
				unitName="unit"
				label="Lot Size"
				placeholder="0"
				units={[...unitOptions]} // Convert readonly array to mutable array
				defaultUnit="sqft"
			/>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="parkingType"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Parking Type</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select parking type" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									{parkingTypes.map((type) => (
										<SelectItem key={type} value={type}>
											{type}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="parkingSpaces"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Parking Spaces</FormLabel>
							<FormControl>
								<Input
									type="number"
									placeholder="0"
									min="0"
									{...field}
									onChange={(e) => {
										const value =
											e.target.value === ""
												? ""
												: Math.max(0, parseInt(e.target.value, 10));
										field.onChange(value);
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="yearBuilt"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Year Built</FormLabel>
							<FormControl>
								<Input
									type="number"
									placeholder={new Date().getFullYear().toString()}
									{...field}
									onChange={(e) => {
										// Allow empty input or any numeric value
										const value =
											e.target.value === "" ? "" : parseInt(e.target.value, 10);
										field.onChange(value);
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="historicalDesignation"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Historical Designation</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select designation" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									{historicalDesignations.map((designation) => (
										<SelectItem key={designation} value={designation}>
											{designation}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
};

export default PropertyDetailsSection;
