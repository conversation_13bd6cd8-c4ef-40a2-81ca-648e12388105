CREATE TYPE "public"."property_type" AS ENUM('house', 'penthouse', 'apartment', 'condo', 'townhouse', 'villa', 'land', 'commercial', 'other');--> statement-breakpoint
CREATE TYPE "public"."review_status" AS ENUM('approved', 'pending', 'drafts', 'archived', 'declined');--> statement-breakpoint
CREATE TYPE "public"."reviewer_type" AS ENUM('advertiser', 'admin');--> statement-breakpoint
CREATE TYPE "public"."role" AS ENUM('admin', 'buyer', 'searcher', 'advertiser', 'concierge', 'combination');--> statement-breakpoint
CREATE TYPE "public"."site_access" AS ENUM('real_estate', 'aviation', 'yachts', 'automobiles', 'fine_art_collectibles');--> statement-breakpoint
CREATE TYPE "public"."two_factor_auth" AS ENUM('disabled', 'sms', 'authenticator');--> statement-breakpoint
CREATE TYPE "public"."visibility" AS ENUM('private', 'public', 'off-market');--> statement-breakpoint
CREATE TABLE "favorites_listings" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"listing_id" text NOT NULL,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "geographic_areas" (
	"id" text PRIMARY KEY NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"region" varchar(100),
	"country" varchar(100),
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "searcher_areas" (
	"searcher_id" text NOT NULL,
	"area_id" text NOT NULL,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "searcher_areas_searcher_id_area_id_pk" PRIMARY KEY("searcher_id","area_id")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" text PRIMARY KEY NOT NULL,
	"clerk_id" varchar(64),
	"role" "role" DEFAULT 'buyer' NOT NULL,
	"sub_category" varchar(50),
	"first_name" varchar(50),
	"last_name" varchar(50),
	"user_name" varchar(50) NOT NULL,
	"company_name" varchar(100),
	"phone_number" varchar(15),
	"email" varchar(255) NOT NULL,
	"email_verified" boolean DEFAULT false,
	"image_url" varchar(255),
	"mg_exclusives" boolean DEFAULT false,
	"two_factor_auth" "two_factor_auth" DEFAULT 'disabled',
	"communication_type" varchar(50),
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_user_name_unique" UNIQUE("user_name"),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "user_subscriptions" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"membership_plan" varchar(50) NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date,
	"status" varchar(20) DEFAULT 'active',
	"billing_status" varchar(20) DEFAULT 'current',
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_site_access" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"site_access" "site_access" NOT NULL,
	"has_access" boolean DEFAULT true,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_geographic_access" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"area_id" text NOT NULL,
	"access_level" varchar(20) DEFAULT 'read',
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_connections" (
	"id" text PRIMARY KEY NOT NULL,
	"buyer_id" text NOT NULL,
	"provider_id" text NOT NULL,
	"provider_role" "role" NOT NULL,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "listings" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text,
	"listing_type" varchar(30) NOT NULL,
	"title" text NOT NULL,
	"slug" varchar(100) NOT NULL,
	"description" text,
	"amenities" text[],
	"price" numeric(15, 2) NOT NULL,
	"currency" varchar(3) DEFAULT 'USD' NOT NULL,
	"is_primary" boolean DEFAULT false NOT NULL,
	"hide_street_address" boolean DEFAULT false NOT NULL,
	"view_count" integer DEFAULT 0 NOT NULL,
	"favorite_count" integer DEFAULT 0 NOT NULL,
	"neighborhood" varchar(100),
	"visibility" "visibility" DEFAULT 'private' NOT NULL,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "locations" (
	"id" text PRIMARY KEY NOT NULL,
	"listing_id" text NOT NULL,
	"address" varchar(255),
	"city" varchar(100),
	"state" varchar(100),
	"country" varchar(100),
	"zip_code" varchar(20),
	"latitude" numeric(9, 6),
	"longitude" numeric(9, 6),
	"map_zoom_level" integer,
	"neighborhood" varchar(100),
	"walk_ability_score" integer,
	"school_district" varchar(100),
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "real_estate_details" (
	"id" text PRIMARY KEY NOT NULL,
	"listing_id" text NOT NULL,
	"property_type" "property_type" DEFAULT 'house' NOT NULL,
	"bedrooms" numeric(5, 1) NOT NULL,
	"bathrooms" numeric(5, 1) NOT NULL,
	"home_size" numeric(15, 2) NOT NULL,
	"home_size_unit" varchar(10) DEFAULT 'sqft' NOT NULL,
	"lot_size" numeric(15, 2),
	"lot_size_unit" varchar(10) DEFAULT 'sqft',
	"parking_type" varchar(30),
	"parking_spaces" numeric(5, 0),
	"year_built" numeric(4, 0),
	"historical_designation" varchar(50) DEFAULT 'None',
	"architect_name" varchar(100),
	"interior_designer" varchar(100),
	"views" varchar(100),
	"historic_details" text,
	"hoa_fees" numeric(10, 2),
	"taxes" numeric(10, 2),
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "media" (
	"id" text PRIMARY KEY NOT NULL,
	"listing_id" text NOT NULL,
	"type" varchar(20) DEFAULT 'image' NOT NULL,
	"source" text,
	"preview" text,
	"key" text,
	"zl_config" text,
	"sort_order" integer DEFAULT 0,
	"status" varchar(30) DEFAULT 'pending' NOT NULL,
	"error_message" text,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "inquiries" (
	"id" text PRIMARY KEY NOT NULL,
	"listing_id" text NOT NULL,
	"sender_id" text NOT NULL,
	"message" text NOT NULL,
	"status" varchar(30) DEFAULT 'pending' NOT NULL,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "listing_recommendations" (
	"id" text PRIMARY KEY NOT NULL,
	"searcher_id" text NOT NULL,
	"buyer_id" text NOT NULL,
	"listing_id" text NOT NULL,
	"status" varchar(30) DEFAULT 'pending' NOT NULL,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payment_history" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"last_payment_date" date,
	"last_payment_amount" numeric(10, 2),
	"next_payment_due_date" date,
	"next_payment_amount" numeric(10, 2),
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "schedules" (
	"id" text PRIMARY KEY NOT NULL,
	"call_name" text,
	"sender_id" text NOT NULL,
	"receiver_id" text NOT NULL,
	"schedule_time" timestamp NOT NULL,
	"end_time" timestamp NOT NULL,
	"listing_id" text,
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "listing_review_requests" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"listing_id" text NOT NULL,
	"review_status" "review_status" DEFAULT 'drafts' NOT NULL,
	"request_date" timestamp DEFAULT now() NOT NULL,
	"response_date" timestamp,
	"approved_date" timestamp,
	"declined_date" timestamp,
	"archived_date" timestamp,
	"notes" text,
	"reviewer_id" text,
	"reviewer_type" "reviewer_type" DEFAULT 'admin',
	"updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "favorites_listings" ADD CONSTRAINT "favorites_listings_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "favorites_listings" ADD CONSTRAINT "favorites_listings_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "searcher_areas" ADD CONSTRAINT "searcher_areas_searcher_id_users_id_fk" FOREIGN KEY ("searcher_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "searcher_areas" ADD CONSTRAINT "searcher_areas_area_id_geographic_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."geographic_areas"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "user_subscriptions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_site_access" ADD CONSTRAINT "user_site_access_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_geographic_access" ADD CONSTRAINT "user_geographic_access_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_geographic_access" ADD CONSTRAINT "user_geographic_access_area_id_geographic_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."geographic_areas"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_connections" ADD CONSTRAINT "user_connections_buyer_id_users_id_fk" FOREIGN KEY ("buyer_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_connections" ADD CONSTRAINT "user_connections_provider_id_users_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listings" ADD CONSTRAINT "listings_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "locations" ADD CONSTRAINT "locations_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "real_estate_details" ADD CONSTRAINT "real_estate_details_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "media" ADD CONSTRAINT "media_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inquiries" ADD CONSTRAINT "inquiries_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inquiries" ADD CONSTRAINT "inquiries_sender_id_users_id_fk" FOREIGN KEY ("sender_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_recommendations" ADD CONSTRAINT "listing_recommendations_searcher_id_users_id_fk" FOREIGN KEY ("searcher_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_recommendations" ADD CONSTRAINT "listing_recommendations_buyer_id_users_id_fk" FOREIGN KEY ("buyer_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_recommendations" ADD CONSTRAINT "listing_recommendations_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_history" ADD CONSTRAINT "payment_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedules" ADD CONSTRAINT "schedules_sender_id_users_id_fk" FOREIGN KEY ("sender_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedules" ADD CONSTRAINT "schedules_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_review_requests" ADD CONSTRAINT "listing_review_requests_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_review_requests" ADD CONSTRAINT "listing_review_requests_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_review_requests" ADD CONSTRAINT "listing_review_requests_reviewer_id_users_id_fk" FOREIGN KEY ("reviewer_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "user_listing_favorite_unique" ON "favorites_listings" USING btree ("user_id","listing_id");--> statement-breakpoint
CREATE INDEX "favorites_user_id_idx" ON "favorites_listings" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "favorites_listing_id_idx" ON "favorites_listings" USING btree ("listing_id");--> statement-breakpoint
CREATE UNIQUE INDEX "area_name_idx" ON "geographic_areas" USING btree ("name");--> statement-breakpoint
CREATE INDEX "searcher_area_searcher_idx" ON "searcher_areas" USING btree ("searcher_id");--> statement-breakpoint
CREATE INDEX "searcher_area_area_idx" ON "searcher_areas" USING btree ("area_id");--> statement-breakpoint
CREATE UNIQUE INDEX "username_idx" ON "users" USING btree ("user_name");--> statement-breakpoint
CREATE UNIQUE INDEX "email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE INDEX "role_idx" ON "users" USING btree ("role");--> statement-breakpoint
CREATE INDEX "subcategory_idx" ON "users" USING btree ("sub_category");--> statement-breakpoint
CREATE UNIQUE INDEX "user_subscription_idx" ON "user_subscriptions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "subscription_plan_idx" ON "user_subscriptions" USING btree ("membership_plan");--> statement-breakpoint
CREATE INDEX "subscription_status_idx" ON "user_subscriptions" USING btree ("status");--> statement-breakpoint
CREATE INDEX "billing_status_idx" ON "user_subscriptions" USING btree ("billing_status");--> statement-breakpoint
CREATE INDEX "subscription_dates_idx" ON "user_subscriptions" USING btree ("start_date","end_date");--> statement-breakpoint
CREATE UNIQUE INDEX "user_site_access_idx" ON "user_site_access" USING btree ("user_id","site_access");--> statement-breakpoint
CREATE INDEX "site_access_user_idx" ON "user_site_access" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_geo_access_idx" ON "user_geographic_access" USING btree ("user_id","area_id");--> statement-breakpoint
CREATE INDEX "geo_access_user_idx" ON "user_geographic_access" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "geo_access_area_idx" ON "user_geographic_access" USING btree ("area_id");--> statement-breakpoint
CREATE UNIQUE INDEX "buyer_provider_unique" ON "user_connections" USING btree ("buyer_id","provider_id");--> statement-breakpoint
CREATE INDEX "connection_buyer_id_idx" ON "user_connections" USING btree ("buyer_id");--> statement-breakpoint
CREATE INDEX "connection_provider_id_idx" ON "user_connections" USING btree ("provider_id");--> statement-breakpoint
CREATE UNIQUE INDEX "slug_idx" ON "listings" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "listing_user_id_idx" ON "listings" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "listing_type_idx" ON "listings" USING btree ("listing_type");--> statement-breakpoint
CREATE INDEX "listing_visibility_idx" ON "listings" USING btree ("visibility");--> statement-breakpoint
CREATE UNIQUE INDEX "location_listing_id_idx" ON "locations" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX "city_country_idx" ON "locations" USING btree ("city","country");--> statement-breakpoint
CREATE UNIQUE INDEX "re_listing_id_idx" ON "real_estate_details" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX "media_listing_id_idx" ON "media" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX "inquiry_listing_id_idx" ON "inquiries" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX "inquiry_sender_id_idx" ON "inquiries" USING btree ("sender_id");--> statement-breakpoint
CREATE INDEX "inquiry_status_idx" ON "inquiries" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "recommendation_unique" ON "listing_recommendations" USING btree ("searcher_id","buyer_id","listing_id");--> statement-breakpoint
CREATE INDEX "recommendation_searcher_idx" ON "listing_recommendations" USING btree ("searcher_id");--> statement-breakpoint
CREATE INDEX "recommendation_buyer_idx" ON "listing_recommendations" USING btree ("buyer_id");--> statement-breakpoint
CREATE INDEX "recommendation_listing_idx" ON "listing_recommendations" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX "recommendation_status_idx" ON "listing_recommendations" USING btree ("status");--> statement-breakpoint
CREATE INDEX "payment_history_user_id_idx" ON "payment_history" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "schedule_sender_idx" ON "schedules" USING btree ("sender_id");--> statement-breakpoint
CREATE INDEX "schedule_listing_idx" ON "schedules" USING btree ("listing_id");--> statement-breakpoint
CREATE UNIQUE INDEX "review_user_listing_unique" ON "listing_review_requests" USING btree ("user_id","listing_id");--> statement-breakpoint
CREATE INDEX "review_user_id_idx" ON "listing_review_requests" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "review_status_idx" ON "listing_review_requests" USING btree ("review_status");--> statement-breakpoint
CREATE INDEX "review_approved_date_idx" ON "listing_review_requests" USING btree ("approved_date");--> statement-breakpoint
CREATE INDEX "review_declined_date_idx" ON "listing_review_requests" USING btree ("declined_date");--> statement-breakpoint
CREATE INDEX "review_archived_date_idx" ON "listing_review_requests" USING btree ("archived_date");--> statement-breakpoint
CREATE INDEX "review_reviewer_id_idx" ON "listing_review_requests" USING btree ("reviewer_id");--> statement-breakpoint
CREATE INDEX "review_reviewer_type_idx" ON "listing_review_requests" USING btree ("reviewer_type");