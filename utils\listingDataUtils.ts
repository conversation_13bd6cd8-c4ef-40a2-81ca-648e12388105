import { ListingData, ListingPartialData } from "@/types/listing";
import { ListingFormValues } from "@/lib/schemas/listing-schema";
import {
	visibilityOptions,
	propertyTypes,
	unitValues,
} from "@/lib/schemas/listing-schema";

// Helper function to remove undefined values from an object recursively
function removeUndefinedValues<T>(obj: T): Partial<T> {
	if (obj === null || obj === undefined) {
		return {};
	}
	
	if (Array.isArray(obj)) {
		return obj.filter(item => item !== undefined) as any;
	}
	
	if (typeof obj === 'object') {
		const result: any = {};
		for (const [key, value] of Object.entries(obj)) {
			if (value !== undefined) {
				if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
					const cleanedValue = removeUndefinedValues(value);
					// Only add the object if it has properties after cleaning
					if (Object.keys(cleanedValue).length > 0) {
						result[key] = cleanedValue;
					}
				} else {
					result[key] = value;
				}
			}
		}
		return result;
	}
	
	return obj;
}

// Convert form values to ListingData partial for optimistic update
export function convertFormValuesToListingData(
	formValues: Partial<ListingFormValues>
): ListingPartialData {
	// Only map fields that are relevant for the listing card display/optimistic update
	const rawData = {
		title: formValues.propertyTitle,
		description: formValues.description,
		price:
			formValues.price?.amount != null
				? formValues.price.amount.toString()
				: undefined,
		currency: formValues.price?.currency,
		hideStreetAddress: formValues.hideStreetAddress,
		parkingType: formValues.parkingType,
		parkingSpaces: formValues.parkingSpaces,
		historicalDesignation: formValues.historicalDesignation,
		amenities: formValues.amenities,
		media: formValues.media,
		visibility: formValues.visibility,
		// location and realEstateDetails are nested, handle if needed
		location: {
			address: formValues.streetAddress,
			city: formValues.city,
			state: formValues.stateRegion,
			zipCode: formValues.zipCode,
			country: formValues.country,
			neighborhood: formValues.neighborhood,
		},
		realEstateDetails: {
			propertyType: formValues.propertyType,
			bedrooms: formValues.bedrooms?.toString(),
			bathrooms: formValues.bathrooms?.toString(),
			homeSize: formValues.homeSize?.value?.toString(),
			homeSizeUnit: formValues.homeSize?.unit,
			lotSize: formValues.lotSize?.value?.toString(),
			lotSizeUnit: formValues.lotSize?.unit,
			yearBuilt: formValues.yearBuilt?.toString(),
		},
	};

	// Remove all undefined values recursively
	return removeUndefinedValues(rawData) as ListingPartialData;
}

// Helper function to validate and return a valid value from an array of allowed values
function validateValue<T extends readonly string[]>(
	value: string | undefined | null,
	allowedValues: T,
	defaultValue: T[number]
): T[number] {
	return allowedValues.includes(value as any)
		? (value as T[number])
		: defaultValue;
}

// Convert ListingData to ListingFormValues for form initialization
export function convertInitialDataToFormValues(
	initialData: ListingData
): Partial<ListingFormValues> {
	return {
		propertyTitle: initialData.title || "",
		description: initialData.description || "",
		price: {
			currency: initialData.currency || "USD",
			amount: Number(initialData.price) || 0,
		},
		hideStreetAddress: initialData.hideStreetAddress || false,
		streetAddress: initialData.location?.address || "",
		city: initialData.location?.city || "",
		stateRegion: initialData.location?.state || "",
		zipCode: initialData.location?.zipCode || "",
		country: initialData.location?.country || "US",
		neighborhood: initialData.location?.neighborhood || "",
		visibility: validateValue(
			initialData.visibility,
			visibilityOptions,
			"private"
		),
		propertyType: validateValue(
			initialData.realEstateDetails?.propertyType,
			propertyTypes,
			"house"
		),
		bedrooms: Number(initialData.realEstateDetails?.bedrooms) || 0,
		bathrooms: Number(initialData.realEstateDetails?.bathrooms) || 0,
		homeSize: {
			unit: validateValue(
				initialData.realEstateDetails?.homeSizeUnit,
				unitValues,
				"sqft"
			),
			value: Number(initialData.realEstateDetails?.homeSize) || 0,
		},
		lotSize: {
			unit: validateValue(
				initialData.realEstateDetails?.lotSizeUnit,
				unitValues,
				"sqft"
			),
			value: Number(initialData.realEstateDetails?.lotSize) || 0,
		},
		yearBuilt:
			Number(initialData.realEstateDetails?.yearBuilt) ||
			new Date().getFullYear(),
		parkingType: initialData.parkingType || "None",
		parkingSpaces: initialData.parkingSpaces || 0,
		historicalDesignation: initialData.historicalDesignation || "None",
		amenities: initialData.amenities || [],
		media:
			initialData.media?.map((media) => ({
				id: media.id,
				source: media.source,
				type: media.type,
				sortOrder: media.sortOrder,
				key: media.key,
			})) || [],
	};
}

// Helper function to deeply compare two values
function isEqual(a: any, b: any): boolean {
	if (a === b) return true;
	if (a == null || b == null) return false;
	if (typeof a !== typeof b) return false;

	if (Array.isArray(a) && Array.isArray(b)) {
		// For arrays, check length first
		if (a.length !== b.length) return false;

		// Then check each element in order
		for (let i = 0; i < a.length; i++) {
			if (!isEqual(a[i], b[i])) return false;
		}
		return true;
	}

	if (typeof a === "object") {
		const keysA = Object.keys(a);
		const keysB = Object.keys(b);
		if (keysA.length !== keysB.length) return false;

		for (const key of keysA) {
			if (!isEqual(a[key], b[key])) return false;
		}
		return true;
	}

	return false;
}

// Helper function to check if media arrays are different
function isMediaArrayChanged(
	newMedia: ListingFormValues["media"],
	originalMedia: ListingFormValues["media"]
): boolean {
	// Handle undefined/null cases - ensure both are arrays
	const newMediaArray = newMedia || [];
	const originalMediaArray = originalMedia || [];

	// Check array length
	if (newMediaArray.length !== originalMediaArray.length) return true;

	// Check each media item in order (order matters for media)
	for (let i = 0; i < newMediaArray.length; i++) {
		const newItem = newMediaArray[i];
		const originalItem = originalMediaArray[i];

		// Compare all relevant properties
		if (
			newItem.id !== originalItem.id ||
			newItem.sortOrder !== originalItem.sortOrder
		) {
			return true;
		}
	}

	return false;
}

// Helper function to check if two string arrays have the same items regardless of order
function areStringArraysEqual(
	a: string[] | undefined,
	b: string[] | undefined
): boolean {
	// Handle undefined/null cases
	if (!a && !b) return true;
	if (!a || !b) return false;

	// Check array length
	if (a.length !== b.length) return false;

	// Sort both arrays and compare
	const sortedA = [...a].sort();
	const sortedB = [...b].sort();

	// Compare each element after sorting
	for (let i = 0; i < sortedA.length; i++) {
		if (sortedA[i] !== sortedB[i]) return false;
	}

	return true;
}

// Compare form values with original listing data and return only changed fields
export function getChangedFormValues(
	originalData: ListingData,
	formValues: ListingFormValues
): Partial<ListingFormValues> {
	const changes: Partial<ListingFormValues> = {};

	// Convert original data to form format for comparison
	const originalFormValues = convertInitialDataToFormValues(originalData);

	// Compare each field and only include changed ones
	if (formValues.propertyTitle !== originalFormValues.propertyTitle) {
		changes.propertyTitle = formValues.propertyTitle;
	}

	if (formValues.description !== originalFormValues.description) {
		changes.description = formValues.description;
	}

	// Compare price object
	if (!isEqual(formValues.price, originalFormValues.price)) {
		changes.price = formValues.price;
	}

	if (formValues.hideStreetAddress !== originalFormValues.hideStreetAddress) {
		changes.hideStreetAddress = formValues.hideStreetAddress;
	}

	if (formValues.streetAddress !== originalFormValues.streetAddress) {
		changes.streetAddress = formValues.streetAddress;
	}

	if (formValues.city !== originalFormValues.city) {
		changes.city = formValues.city;
	}

	if (formValues.stateRegion !== originalFormValues.stateRegion) {
		changes.stateRegion = formValues.stateRegion;
	}

	if (formValues.zipCode !== originalFormValues.zipCode) {
		changes.zipCode = formValues.zipCode;
	}

	if (formValues.country !== originalFormValues.country) {
		changes.country = formValues.country;
	}

	if (formValues.neighborhood !== originalFormValues.neighborhood) {
		changes.neighborhood = formValues.neighborhood;
	}

	if (formValues.visibility !== originalFormValues.visibility) {
		changes.visibility = formValues.visibility;
	}

	if (formValues.propertyType !== originalFormValues.propertyType) {
		changes.propertyType = formValues.propertyType;
	}

	if (formValues.bedrooms !== originalFormValues.bedrooms) {
		changes.bedrooms = formValues.bedrooms;
	}

	if (formValues.bathrooms !== originalFormValues.bathrooms) {
		changes.bathrooms = formValues.bathrooms;
	}

	// Compare homeSize object
	if (!isEqual(formValues.homeSize, originalFormValues.homeSize)) {
		changes.homeSize = formValues.homeSize;
	}

	// Compare lotSize object
	if (!isEqual(formValues.lotSize, originalFormValues.lotSize)) {
		changes.lotSize = formValues.lotSize;
	}

	if (formValues.yearBuilt !== originalFormValues.yearBuilt) {
		changes.yearBuilt = formValues.yearBuilt;
	}

	if (formValues.parkingType !== originalFormValues.parkingType) {
		changes.parkingType = formValues.parkingType;
	}

	if (formValues.parkingSpaces !== originalFormValues.parkingSpaces) {
		changes.parkingSpaces = formValues.parkingSpaces;
	}

	if (
		formValues.historicalDesignation !==
		originalFormValues.historicalDesignation
	) {
		changes.historicalDesignation = formValues.historicalDesignation;
	}

	// Compare amenities array (order doesn't matter, only content matters)
	if (
		!areStringArraysEqual(formValues.amenities, originalFormValues.amenities)
	) {
		changes.amenities = formValues.amenities;
	}

	// Compare media array with special handling for order, additions, and removals
	// Ensure both arrays are defined before comparison
	if (
		originalFormValues.media &&
		isMediaArrayChanged(formValues.media, originalFormValues.media)
	) {
		changes.media = formValues.media;
	}

	return changes;
}
