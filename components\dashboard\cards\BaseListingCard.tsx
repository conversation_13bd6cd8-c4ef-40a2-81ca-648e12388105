import { Card, CardContent } from "../../ui/card";
import { Skeleton } from "../../ui/skeleton";
import Image from "next/image";
import { Loader2 } from "lucide-react";
import { ListingData } from "@/types/listing";
import { useState } from "react";
import { getPreviewMediaUrl, isMediaProcessing } from "@/utils/media";

interface BaseListingCardProps {
	item: ListingData;
	children: React.ReactNode;
	className?: string;
}

const BaseListingCard = ({
	item,
	children,
	className = "",
}: BaseListingCardProps) => {
	const [imageLoading, setImageLoading] = useState(true);

	// Use utility functions for media processing logic
	const mediaProcessing = isMediaProcessing(item.media);
	const previewMedia = getPreviewMediaUrl(item.media);

	return (
		<Card
			className={`gap-2 p-0 overflow-hidden bg-gray-100 border-0 rounded-none shadow-none ${className}`}
		>
			<CardContent className="relative p-0">
				{mediaProcessing ? (
					// Show processing state when media is still being processed
					<div className="flex flex-col items-center justify-center w-full bg-gray-200 h-72">
						<Loader2 className="w-8 h-8 mb-2 text-gray-500 animate-spin" />
						<p className="font-medium text-gray-600">Processing</p>
					</div>
				) : (
					// Show skeleton while image is loading, then show actual image
					<div className="relative w-full h-72">
						{imageLoading && (
							<Skeleton className="absolute inset-0 w-full h-full rounded-none" />
						)}
						<Image
							src={previewMedia}
							alt={item.title || "Property image"}
							width={600}
							height={400}
							className="object-cover w-full h-72"
							onLoad={() => setImageLoading(false)}
							onError={() => setImageLoading(false)}
						/>
					</div>
				)}
			</CardContent>
			{children}
		</Card>
	);
};

export default BaseListingCard;
