"use client";

import { useFormMethods } from "./listing-form/AddNewListingForm";
import ListingCard from "./ListingCard";
import CaseDialog from "../global/CaseDialog";
import SearchAndSortControls from "./SearchAndSortControls";
import ListingsGrid from "./ListingsGrid";
import ListingsSkeleton from "./ListingsSkeleton";
import EditListingDialog from "./EditListingDialog";

import { useListings } from "@/hooks/useListings";
import { useListingsInfiniteScroll } from "@/hooks/useListingsInfiniteScroll";
import { useListingSubmit } from "@/hooks/useListingSubmit";
import { useSearchAndSort } from "@/hooks/useSearchAndSort";
import { ReviewerTypeType, ReviewStatusType } from "@/db/schema/enums";
import { useListingStatusActions } from "@/hooks/useListingStatusActions";
import {
	convertFormValuesToListingData,
	getChangedFormValues,
} from "@/utils/listingDataUtils";

interface ListingsProps {
	backPage?: string;
	status?: ReviewStatusType | ReviewStatusType[];
	search?: string;
	filters?: Record<string, any>;
	page?: number;
	pageSize?: number;
	userOnly?: boolean;
	favoritesOnly?: boolean;
	showSearch?: boolean;
	isManagement?: boolean;
	reviewerType?: ReviewerTypeType;
}

const Listings = ({
	backPage,
	status,
	search = "",
	filters = {},
	page = 1,
	pageSize = 6,
	userOnly,
	favoritesOnly = false,
	showSearch = false,
	isManagement = false,
	reviewerType,
}: ListingsProps) => {
	// Search and sort functionality
	const {
		searchValue,
		setSearchValue,
		sortBy,
		handleSearchClear,
		handleSortChange,
	} = useSearchAndSort({ initialSearch: search });

	// Edit functionality
	const {
		editDialogOpen,
		setEditDialogOpen,
		editingListing,
		handleEdit,
		handleEditSubmit,
		handleCreateSubmit,
		handleDialogClose,
	} = useListingSubmit();

	// Form methods for editing dialog
	const formMethods = useFormMethods();

	// Use the listings hook to fetch and manage listings data
	const {
		listings,
		isLoading,
		isLoadingMore,
		error,
		isEmpty,
		hasMore,
		loadMore,
		mutate,
	} = useListings({
		status: status || "approved",
		search: searchValue,
		filters,
		page,
		pageSize,
		userOnly,
		favoritesOnly,
		infinite: true,
		sortBy,
		reviewerType,
	});

	const { optimisticallyRemoveListing, optimisticUpdateItem } =
		useListingStatusActions(mutate);

	// Infinite scroll functionality
	useListingsInfiniteScroll({
		hasMore,
		isLoadingMore,
		loadMore,
	});

	// Handle form submission for editing
	const handleFormSubmit = async (
		reviewStatus: ReviewStatusType
	): Promise<void> => {
		return new Promise((resolve, reject) => {
			const submitHandler = formMethods.handleSubmit(
				async (data) => {
					// Success callback - validation passed
					try {
						if (editingListing && editingListing.listingReviewRequest) {
							// If editing a listing, get changed fields
							const changedFields = getChangedFormValues(editingListing, data);
							// Optimistic update first for admin users
							const shouldRemove =
								editingListing.listingReviewRequest.reviewStatus !==
								reviewStatus;
							// Fire and forget the API call for admin (optimistic update)
							await handleEditSubmit({
								data: changedFields,
								reviewStatus,
							});

							if (shouldRemove) {
								optimisticallyRemoveListing(
									editingListing.listingReviewRequest.id
								);
							} else {
								optimisticUpdateItem(
									editingListing.listingReviewRequest.id,
									convertFormValuesToListingData(changedFields)
								);
							}
							resolve();
						} else {
							// If no listing is being edited, just submit the form
							await handleCreateSubmit({
								data,
								reviewStatus,
							});
							mutate();
							resolve();
						}
					} catch (error) {
						reject(error);
					}
				},
				() => {
					reject(new Error("Form validation failed"));
				}
			);

			// Call the submit handler
			submitHandler();
		});
	};

	// Enhanced dialog close handler with form reset
	const enhancedDialogClose = () => {
		handleDialogClose();
		formMethods.reset();
	};

	// Render search and sort controls
	const renderSearchControls = () => {
		if (!showSearch) return null;

		return (
			<SearchAndSortControls
				searchValue={searchValue}
				onSearchChange={setSearchValue}
				onSearchClear={handleSearchClear}
				sortBy={sortBy}
				onSortChange={handleSortChange}
			/>
		);
	};

	// Render loading state
	if (isLoading && !listings.length) {
		return (
			<div className="flex flex-col h-full overflow-hidden">
				{renderSearchControls()}
				<div className="flex-1 overflow-hidden">
					<div className="grid grid-cols-1 gap-8 my-4 lg:grid-cols-2 3xl:grid-cols-3">
						<ListingsSkeleton count={pageSize} />
					</div>
				</div>
			</div>
		);
	}

	// Render error state
	if (error) {
		return (
			<div className="py-4 text-center text-red-500">
				Error loading listings: {error.message}
			</div>
		);
	}

	// Render empty state
	if (isEmpty) {
		return (
			<div className="flex flex-col h-full overflow-hidden">
				{renderSearchControls()}
				<div className="flex items-center justify-center flex-1">
					<div className="py-4 text-center">No listings found.</div>
				</div>
			</div>
		);
	}

	// Render main content
	return (
		<div className="flex flex-col h-full overflow-hidden">
			{renderSearchControls()}

			<div className="flex-1 overflow-hidden">
				<ListingsGrid
					isLoadingMore={isLoadingMore}
					hasMore={hasMore}
					listingsCount={listings.length}
				>
					{listings.map((item, i) => (
						<CaseDialog
							key={item.id}
							data={item}
							ind={i}
							backPage={backPage}
							mutate={mutate}
							status={status}
							onEdit={(listingId) => handleEdit(listingId, listings)} // Pass handleEdit function
						>
							<ListingCard
								key={item.id}
								item={item}
								status={status}
								onEdit={(listingId) => handleEdit(listingId, listings)}
								isFavored={item.isFavored}
								isManagement={isManagement}
								mutate={mutate}
							/>
						</CaseDialog>
					))}
				</ListingsGrid>
			</div>

			{/* Edit Dialog */}
			<EditListingDialog
				open={editDialogOpen}
				onOpenChange={setEditDialogOpen}
				editingListing={editingListing}
				onSubmit={handleFormSubmit}
				onClose={enhancedDialogClose}
				formMethods={formMethods}
			/>
		</div>
	);
};

export default Listings;
