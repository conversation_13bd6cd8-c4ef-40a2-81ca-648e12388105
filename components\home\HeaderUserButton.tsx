import { useState, useRef, useEffect } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import Link from "next/link";
import { FaRegUser } from "react-icons/fa6";
import { IoChevronDownOutline } from "react-icons/io5";
import { Button } from "../ui/button";
import LogoutPopup from "../global/LogoutPopup";
import { RoleType } from "@/db/schema/enums";
import { UserType } from "@/db/services/users.service";

// Role-based dashboard link mapping - now supports multiple links per role
const roleDashboardLinks: Record<RoleType, { path: string; label: string }[]> =
	{
		advertiser: [{ path: "/dashboard/listings", label: "My listings" }],
		buyer: [{ path: "/dashboard/listings/favorites", label: "My favorites" }],
		admin: [
			{
				path: "/dashboard/user-management/buyers",
				label: "User Management",
			},
			{
				path: "/dashboard/listing-management/approved-listings",
				label: "Listing Management",
			},
		],
		searcher: [{ path: "/dashboard/listings", label: "Listings" }],
		concierge: [{ path: "/dashboard/listings", label: "Listings" }],
		combination: [{ path: "/dashboard/listings", label: "Listings" }],
	};

const HeaderUserButton = ({ me }: { me: UserType }) => {
  // Get dashboard links based on user role or empty array if not found
  const dashboardLinks =
    me?.role && me.role in roleDashboardLinks
      ? roleDashboardLinks[me.role as RoleType]
      : [];

  // State for controlling popover visibility
  const [isOpen, setIsOpen] = useState(false);
  const closeTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Clear timeout on component unmount
  useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // Handle mouse enter - open immediately and clear any pending close
  const handleMouseEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
    setIsOpen(true);
  };

  // Handle mouse leave - close after 2 second delay
  const handleMouseLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setIsOpen(false);
      closeTimeoutRef.current = null;
    }, 2000); // 2 second delay
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div
          className="flex flex-row items-center h-10 gap-2 p-4 overflow-hidden font-bold bg-gray-100 rounded-md cursor-pointer font-manrope"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <FaRegUser className="text-slate-900" />

          <div className="flex flex-row items-center gap-2">
            <span>Dashboard</span>
            <IoChevronDownOutline />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="flex flex-col gap-4 p-4 bg-gray-100 w-44"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {dashboardLinks.map((link, index) => (
          <Button
            key={index}
            className="justify-start font-bold font-manrope"
            variant={"ghost"}
            asChild
          >
            <Link href={link.path}>{link.label}</Link>
          </Button>
        ))}
        <Button
          className="justify-start font-bold font-manrope"
          variant={"ghost"}
          asChild
        >
          <Link href="/dashboard/inbox">Inbox</Link>
        </Button>
        <Button
          className="justify-start font-bold font-manrope"
          variant={"ghost"}
          asChild
        >
          <Link href="/dashboard/schedule">Meetings</Link>
        </Button>
        <LogoutPopup>
          <Button
            className="justify-start font-bold underline font-manrope"
            variant={"ghost"}
          >
            Logout
          </Button>
        </LogoutPopup>
      </PopoverContent>
    </Popover>
  );
};

export default HeaderUserButton;
