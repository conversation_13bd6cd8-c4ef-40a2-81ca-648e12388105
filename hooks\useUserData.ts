import { useState, useEffect } from "react";
import use<PERSON><PERSON> from "swr";
import { formatDistanceToNow } from "date-fns";
import { enUS } from "date-fns/locale";
import { getClerkUserById } from "@/lib/clerk";
import { fetcher } from "@/lib/utils";

export interface UserStatus {
	isOnline: boolean;
	lastActiveTime: string | null;
}

// Global cache for Clerk user status
const statusCache = new Map<string, { status: UserStatus; timestamp: number }>();

export function useUserData(userId: string) {
	// Use SWR to fetch user data
	const { data, error, mutate } = useSWR(`/api/accounts/${userId}`, fetcher, {
		dedupingInterval: 60000,
		refreshInterval: 600000, // Refresh every 10 minutes
		revalidateOnFocus: false,
		revalidateOnReconnect: true,
	});
	
	// Add another SWR hook specifically for Clerk user data to avoid duplicate calls
	const { data: clerkUserData, mutate: mutateClerkUser } = useSWR(
		data?.user?.clerkId ? `clerk-user-${data.user.clerkId}` : null,
		async () => {
			const clerkId = data?.user?.clerkId;
			if (!clerkId) return null;
			
			// Check in-memory cache first
			const cachedData = statusCache.get(clerkId);
			if (cachedData && (Date.now() - cachedData.timestamp < 300000)) {
				return cachedData.status;
			}
			
			return getUserStatus(clerkId);
		},
		{
			refreshInterval: 300000,
			dedupingInterval: 60000,
			revalidateOnFocus: false,
		}
	);
	
	const [userStatus, setUserStatus] = useState<UserStatus>({
		isOnline: false,
		lastActiveTime: null,
	});

	// Determine user status (online or last active time)
	const getUserStatus = async (clerkId: string) => {
		try {
			// Directly call the API without redundant cache checks
			// (SWR and its cache mechanism already handle this)
			const clerkUser = await getClerkUserById(clerkId);

			// Check if user data exists and has the lastActiveAt property
			if (!clerkUser || clerkUser.status === "error") {
				console.error("Failed to get user data:", clerkUser);
				const status = {
					isOnline: false,
					lastActiveTime: null,
				};
				
				// Cache the result even if it's an error to prevent repeated failed calls
				statusCache.set(clerkId, { status, timestamp: Date.now() });
				
				return status;
			}

			// Safely access lastActiveAt property
			const lastActiveAtValue =
				clerkUser.data?.lastActiveAt ||
				clerkUser.data?.last_active_at ||
				clerkUser.data?.lastActive;

			if (!lastActiveAtValue) {
				return {
					isOnline: false,
					lastActiveTime: null,
				};
			}

			const lastActiveTime = new Date(lastActiveAtValue);

			// Validate the date is valid
			if (isNaN(lastActiveTime.getTime())) {
				console.error("Invalid lastActiveAt date format:", lastActiveAtValue);
				return {
					isOnline: false,
					lastActiveTime: null,
				};
			}

			const fifteenMinutesAgo = new Date();
			fifteenMinutesAgo.setMinutes(fifteenMinutesAgo.getMinutes() - 15);

			// User is considered online if active in the last 15 minutes
			const isOnline = lastActiveTime > fifteenMinutesAgo;

			// Format the last active time in a relative format
			const lastActiveFormatted = formatDistanceToNow(lastActiveTime, {
				addSuffix: true,
				locale: enUS,
			});

			const status = {
				isOnline,
				lastActiveTime: lastActiveFormatted,
			};
			
			// Cache the status in memory
			statusCache.set(clerkId, { status, timestamp: Date.now() });
			
			return status;
		} catch (error) {
			console.error("Error getting user status:", error);
			return {
				isOnline: false,
				lastActiveTime: null,
			};
		}
	};

	useEffect(() => {
		if (clerkUserData) {
			setUserStatus(clerkUserData);
		}
	}, [clerkUserData]);

	// Calculate membership plan
	const membershipPlan =
		data?.subscriptions &&
		data.subscriptions.length > 0 &&
		data.subscriptions[0]?.status === "active"
			? data.subscriptions[0].membershipPlan
			: "None";

	return {
		data,
		error,
		userStatus,
		membershipPlan,
		isLoading: !error && !data,
		// Optimize the mutate function to support data updates and avoid recursion
		mutate: async (data?: any, options?: any) => {
			// Create an array of mutation promises
			const mutations = [
				// Mutate the main user data
				mutate(data, options),
			];

			// Also mutate Clerk user data if available
			if (data?.user?.clerkId) {
				mutations.push(mutateClerkUser());
			}

			// Wait for all mutations to complete and return the main mutation result
			const [mainResult] = await Promise.all(mutations);
			return mainResult;
		},
	};
}
